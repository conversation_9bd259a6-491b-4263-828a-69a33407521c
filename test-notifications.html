<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإشعارات - لوحة التراخيص</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            color: #2d3748;
        }

        .test-section {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #4299e1;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-btn {
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-success { background: #48bb78; color: white; }
        .btn-error { background: #f56565; color: white; }
        .btn-warning { background: #ed8936; color: white; }
        .btn-info { background: #4299e1; color: white; }
        .btn-primary { background: #667eea; color: white; }

        .log-section {
            background: #1a202c;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 2rem;
        }

        .dashboard-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: white;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: #48bb78; }
        .status-error { background: #f56565; }
        .status-warning { background: #ed8936; }
        .status-info { background: #4299e1; }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bell"></i> اختبار نظام الإشعارات</h1>
            <p>اختبار شامل لنظام الإشعارات في لوحة التراخيص</p>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-vial"></i> اختبار أنواع الإشعارات</h3>
            <div class="test-buttons">
                <button class="test-btn btn-success" onclick="testNotification('تم إرسال طلب التفعيل بنجاح', 'success')">
                    <i class="fas fa-check-circle"></i> إشعار نجاح
                </button>
                <button class="test-btn btn-error" onclick="testNotification('خطأ في الاتصال بالخادم', 'error')">
                    <i class="fas fa-exclamation-circle"></i> إشعار خطأ
                </button>
                <button class="test-btn btn-warning" onclick="testNotification('تحذير: انتهاء صلاحية الترخيص قريباً', 'warning')">
                    <i class="fas fa-exclamation-triangle"></i> إشعار تحذير
                </button>
                <button class="test-btn btn-info" onclick="testNotification('معلومات: تم تحديث البيانات', 'info')">
                    <i class="fas fa-info-circle"></i> إشعار معلومات
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-rocket"></i> اختبار طلبات التفعيل</h3>
            <div class="test-buttons">
                <button class="test-btn btn-primary" onclick="sendActivationRequest()">
                    <i class="fas fa-paper-plane"></i> إرسال طلب تفعيل
                </button>
                <button class="test-btn btn-primary" onclick="sendMultipleRequests()">
                    <i class="fas fa-layer-group"></i> إرسال عدة طلبات
                </button>
                <button class="test-btn btn-info" onclick="checkDashboardData()">
                    <i class="fas fa-search"></i> فحص بيانات اللوحة
                </button>
                <button class="test-btn btn-warning" onclick="clearAllData()">
                    <i class="fas fa-trash"></i> مسح جميع البيانات
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> سجل الأحداث</h3>
            <div id="logOutput" class="log-section">
                <div>🚀 نظام اختبار الإشعارات جاهز...</div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-desktop"></i> لوحة التراخيص المدمجة</h3>
            <iframe id="dashboardFrame" class="dashboard-frame" 
                    src="resources/app/src/license-management/admin-dashboard.html">
            </iframe>
        </div>
    </div>

    <script>
        let requestCounter = 1;

        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const statusClass = `status-${type}`;
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                [${timestamp}] ${message}
            `;
            
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }

        function testNotification(message, type) {
            log(`اختبار إشعار ${type}: ${message}`, type);
            
            // إنشاء إشعار مباشر
            showTestNotification(message, type);
            
            // محاولة إرسال الإشعار للوحة التراخيص
            try {
                const dashboardFrame = document.getElementById('dashboardFrame');
                if (dashboardFrame && dashboardFrame.contentWindow) {
                    dashboardFrame.contentWindow.postMessage({
                        type: 'showNotification',
                        message: message,
                        notificationType: type
                    }, '*');
                }
            } catch (error) {
                log(`خطأ في إرسال الإشعار للوحة: ${error.message}`, 'error');
            }
        }

        function showTestNotification(message, type) {
            const colors = {
                'success': '#48bb78',
                'error': '#f56565',
                'warning': '#ed8936',
                'info': '#4299e1'
            };

            const icons = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                background: ${colors[type]};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                display: flex;
                align-items: center;
                gap: 0.75rem;
                animation: slideInRight 0.3s ease-out;
                font-weight: 600;
                min-width: 300px;
                max-width: 400px;
            `;

            notification.innerHTML = `
                <i class="fas ${icons[type]}" style="font-size: 1.2rem;"></i>
                <span style="flex: 1;">${message}</span>
                <button onclick="this.parentElement.remove()" style="
                    background: rgba(255,255,255,0.2);
                    border: none;
                    color: white;
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 0.8rem;
                ">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        function generateRandomRequest() {
            const names = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'علي محمود', 'خديجة سالم'];
            const wilayas = ['الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'باتنة', 'سطيف'];
            const municipalities = ['المدينة المركزية', 'الحي الشعبي', 'المنطقة الصناعية', 'الضاحية الجنوبية'];

            return {
                id: Date.now() + Math.random(),
                fullName: names[Math.floor(Math.random() * names.length)],
                phone: '0' + Math.floor(Math.random() * 900000000 + 100000000),
                wilaya: wilayas[Math.floor(Math.random() * wilayas.length)],
                municipality: municipalities[Math.floor(Math.random() * municipalities.length)],
                status: 'pending',
                submittedAt: new Date().toISOString(),
                requestNumber: `REQ-${Date.now()}`
            };
        }

        function sendActivationRequest() {
            const request = generateRandomRequest();
            
            try {
                // حفظ في localStorage
                let requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                requests.push(request);
                localStorage.setItem('activationRequests', JSON.stringify(requests));
                
                log(`تم إرسال طلب من: ${request.fullName} (${request.phone})`, 'success');
                
                // إشعار نجاح
                testNotification(`طلب تفعيل جديد من: ${request.fullName}`, 'success');
                
                // تحديث العداد
                requestCounter++;
                
            } catch (error) {
                log(`خطأ في إرسال الطلب: ${error.message}`, 'error');
            }
        }

        function sendMultipleRequests() {
            const count = 3;
            log(`إرسال ${count} طلبات تفعيل...`, 'info');
            
            for (let i = 0; i < count; i++) {
                setTimeout(() => {
                    sendActivationRequest();
                }, i * 1000);
            }
        }

        function checkDashboardData() {
            try {
                const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                log(`عدد الطلبات المحفوظة: ${requests.length}`, 'info');
                
                if (requests.length > 0) {
                    log(`آخر طلب: ${requests[requests.length - 1].fullName}`, 'info');
                }
                
                // إرسال رسالة للوحة التراخيص للتحديث
                const dashboardFrame = document.getElementById('dashboardFrame');
                if (dashboardFrame && dashboardFrame.contentWindow) {
                    dashboardFrame.contentWindow.postMessage({
                        type: 'refreshData'
                    }, '*');
                }
                
            } catch (error) {
                log(`خطأ في فحص البيانات: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('activationRequests');
                log('تم مسح جميع البيانات', 'warning');
                
                // إشعار اللوحة بالتحديث
                const dashboardFrame = document.getElementById('dashboardFrame');
                if (dashboardFrame && dashboardFrame.contentWindow) {
                    dashboardFrame.contentWindow.postMessage({
                        type: 'refreshData'
                    }, '*');
                }
            }
        }

        // استقبال رسائل من لوحة التراخيص
        window.addEventListener('message', function(event) {
            if (event.data.type === 'dashboardReady') {
                log('لوحة التراخيص جاهزة', 'success');
            } else if (event.data.type === 'notificationShown') {
                log(`تم عرض إشعار في اللوحة: ${event.data.message}`, 'info');
            }
        });

        // تحديث دوري لفحص البيانات
        setInterval(() => {
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            if (requests.length > 0) {
                // إشعار صامت للوحة بوجود بيانات
                const dashboardFrame = document.getElementById('dashboardFrame');
                if (dashboardFrame && dashboardFrame.contentWindow) {
                    dashboardFrame.contentWindow.postMessage({
                        type: 'checkForUpdates'
                    }, '*');
                }
            }
        }, 3000);

        log('تم تحميل نظام اختبار الإشعارات بنجاح', 'success');
    </script>
</body>
</html>
