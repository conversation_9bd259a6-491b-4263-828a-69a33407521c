// لوحة إدارة التراخيص الجديدة - بسيطة وفعالة
class LicenseDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.requests = [];
        this.licenses = [];
        this.init();
    }

    init() {
        console.log('🚀 تهيئة لوحة التراخيص الجديدة...');
        this.loadData();
        this.startMonitoring();
        this.showNotification('مرحباً بك في لوحة إدارة التراخيص', 'success');
    }

    // تحميل البيانات
    loadData() {
        try {
            // تحميل طلبات التفعيل
            const requestsData = localStorage.getItem('activationRequests');
            this.requests = requestsData ? JSON.parse(requestsData) : [];
            
            // تحميل التراخيص
            const licensesData = localStorage.getItem('activeLicenses');
            this.licenses = licensesData ? JSON.parse(licensesData) : [];

            console.log(`📊 تم تحميل ${this.requests.length} طلب و ${this.licenses.length} ترخيص`);
            
            this.updateStats();
            this.updateCurrentSection();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            this.showNotification('خطأ في تحميل البيانات', 'error');
        }
    }

    // تحديث الإحصائيات
    updateStats() {
        const totalRequests = this.requests.length;
        const pendingRequests = this.requests.filter(r => r.status === 'pending').length;
        const approvedRequests = this.requests.filter(r => r.status === 'approved').length;
        const activeLicenses = this.licenses.length;

        document.getElementById('totalRequests').textContent = totalRequests;
        document.getElementById('pendingRequests').textContent = pendingRequests;
        document.getElementById('approvedRequests').textContent = approvedRequests;
        document.getElementById('activeLicenses').textContent = activeLicenses;
        document.getElementById('requestsBadge').textContent = pendingRequests;
    }

    // عرض الطلبات
    displayRequests(container, requests, limit = null) {
        const requestsToShow = limit ? requests.slice(0, limit) : requests;
        
        if (requestsToShow.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>لا توجد طلبات</h3>
                    <p>لم يتم استلام أي طلبات تفعيل بعد</p>
                </div>
            `;
            return;
        }

        container.innerHTML = requestsToShow.map(request => `
            <div class="request-card">
                <div class="request-header">
                    <div class="request-name">${request.fullName}</div>
                    <div class="request-status status-${request.status}">
                        ${this.getStatusText(request.status)}
                    </div>
                </div>
                <div class="request-details">
                    <div><i class="fas fa-phone"></i> ${request.phone}</div>
                    <div><i class="fas fa-map-marker-alt"></i> ${request.wilaya}</div>
                    <div><i class="fas fa-building"></i> ${request.municipality}</div>
                    <div><i class="fas fa-calendar"></i> ${this.formatDate(request.submittedAt)}</div>
                </div>
                <div class="request-actions">
                    ${request.status === 'pending' ? `
                        <button class="btn btn-success btn-sm" onclick="dashboard.approveRequest('${request.id}')">
                            <i class="fas fa-check"></i> موافقة
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="dashboard.rejectRequest('${request.id}')">
                            <i class="fas fa-times"></i> رفض
                        </button>
                    ` : ''}
                    <button class="btn btn-primary btn-sm" onclick="dashboard.viewRequestDetails('${request.id}')">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                </div>
            </div>
        `).join('');
    }

    // عرض التراخيص
    displayLicenses(container) {
        if (this.licenses.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-key"></i>
                    <h3>لا توجد تراخيص</h3>
                    <p>لم يتم إنشاء أي تراخيص بعد</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.licenses.map(license => `
            <div class="request-card">
                <div class="request-header">
                    <div class="request-name">${license.clientName}</div>
                    <div class="request-status" style="background: #48bb78; color: white;">
                        مفعل
                    </div>
                </div>
                <div class="request-details">
                    <div><i class="fas fa-key"></i> ${license.licenseKey}</div>
                    <div><i class="fas fa-calendar-check"></i> ${this.formatDate(license.activatedAt)}</div>
                    <div><i class="fas fa-clock"></i> ${license.type}</div>
                    <div><i class="fas fa-calendar-times"></i> ${license.expiresAt ? this.formatDate(license.expiresAt) : 'دائم'}</div>
                </div>
                <div class="request-actions">
                    <button class="btn btn-warning btn-sm" onclick="dashboard.deactivateLicense('${license.id}')">
                        <i class="fas fa-ban"></i> إلغاء التفعيل
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="dashboard.viewLicenseDetails('${license.id}')">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                </div>
            </div>
        `).join('');
    }

    // تحديث القسم الحالي
    updateCurrentSection() {
        if (this.currentSection === 'dashboard') {
            this.displayRequests(document.getElementById('recentRequests'), this.requests.slice(-5).reverse(), 5);
        } else if (this.currentSection === 'requests') {
            this.displayRequests(document.getElementById('allRequests'), this.requests.reverse());
        } else if (this.currentSection === 'licenses') {
            this.displayLicenses(document.getElementById('allLicenses'));
        }
    }

    // موافقة على طلب
    approveRequest(requestId) {
        const request = this.requests.find(r => r.id == requestId);
        if (request) {
            request.status = 'approved';
            request.approvedAt = new Date().toISOString();
            
            // إنشاء ترخيص جديد
            const license = {
                id: Date.now(),
                clientName: request.fullName,
                clientPhone: request.phone,
                licenseKey: this.generateLicenseKey(),
                type: 'lifetime',
                activatedAt: new Date().toISOString(),
                expiresAt: null,
                requestId: requestId
            };
            
            this.licenses.push(license);
            this.saveData();
            this.showNotification(`تم الموافقة على طلب ${request.fullName} وإنشاء ترخيص`, 'success');
        }
    }

    // رفض طلب
    rejectRequest(requestId) {
        const request = this.requests.find(r => r.id == requestId);
        if (request && confirm(`هل أنت متأكد من رفض طلب ${request.fullName}؟`)) {
            request.status = 'rejected';
            request.rejectedAt = new Date().toISOString();
            this.saveData();
            this.showNotification(`تم رفض طلب ${request.fullName}`, 'warning');
        }
    }

    // إلغاء تفعيل ترخيص
    deactivateLicense(licenseId) {
        const license = this.licenses.find(l => l.id == licenseId);
        if (license && confirm(`هل أنت متأكد من إلغاء تفعيل ترخيص ${license.clientName}؟`)) {
            this.licenses = this.licenses.filter(l => l.id != licenseId);
            this.saveData();
            this.showNotification(`تم إلغاء تفعيل ترخيص ${license.clientName}`, 'warning');
        }
    }

    // حفظ البيانات
    saveData() {
        localStorage.setItem('activationRequests', JSON.stringify(this.requests));
        localStorage.setItem('activeLicenses', JSON.stringify(this.licenses));
        this.updateStats();
        this.updateCurrentSection();
    }

    // مراقبة التحديثات
    startMonitoring() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (e.key === 'activationRequests') {
                console.log('📥 تم رصد طلب جديد!');
                this.handleNewRequest();
            }
        });

        // فحص دوري
        setInterval(() => {
            this.checkForNewRequests();
        }, 3000);
    }

    // التعامل مع الطلبات الجديدة
    handleNewRequest() {
        const oldCount = this.requests.length;
        this.loadData();
        const newCount = this.requests.length;
        
        if (newCount > oldCount) {
            const newRequests = newCount - oldCount;
            this.showNotification(`تم استلام ${newRequests} طلب تفعيل جديد`, 'info');
            
            // عرض تفاصيل الطلبات الجديدة
            const latestRequests = this.requests.slice(-newRequests);
            latestRequests.forEach(request => {
                setTimeout(() => {
                    this.showNotification(`طلب جديد من: ${request.fullName}`, 'success');
                }, 500);
            });
        }
    }

    // فحص الطلبات الجديدة
    checkForNewRequests() {
        try {
            const currentData = localStorage.getItem('activationRequests');
            const currentRequests = currentData ? JSON.parse(currentData) : [];
            
            if (currentRequests.length > this.requests.length) {
                this.handleNewRequest();
            }
        } catch (error) {
            console.error('خطأ في فحص الطلبات:', error);
        }
    }

    // عرض إشعار
    showNotification(message, type = 'info') {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${icons[type]}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    // دوال مساعدة
    getStatusText(status) {
        const statusMap = {
            'pending': 'معلق',
            'approved': 'موافق عليه',
            'rejected': 'مرفوض'
        };
        return statusMap[status] || status;
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    generateLicenseKey() {
        return 'LIC-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9).toUpperCase();
    }

    viewRequestDetails(requestId) {
        const request = this.requests.find(r => r.id == requestId);
        if (request) {
            alert(`تفاصيل الطلب:\n\nالاسم: ${request.fullName}\nالهاتف: ${request.phone}\nالولاية: ${request.wilaya}\nالبلدية: ${request.municipality}\nتاريخ الإرسال: ${this.formatDate(request.submittedAt)}\nالحالة: ${this.getStatusText(request.status)}`);
        }
    }

    viewLicenseDetails(licenseId) {
        const license = this.licenses.find(l => l.id == licenseId);
        if (license) {
            alert(`تفاصيل الترخيص:\n\nالعميل: ${license.clientName}\nمفتاح الترخيص: ${license.licenseKey}\nنوع الترخيص: ${license.type}\nتاريخ التفعيل: ${this.formatDate(license.activatedAt)}\nتاريخ الانتهاء: ${license.expiresAt ? this.formatDate(license.expiresAt) : 'دائم'}`);
        }
    }

    generateLicense() {
        const clientName = prompt('اسم العميل:');
        if (clientName) {
            const license = {
                id: Date.now(),
                clientName: clientName,
                clientPhone: '',
                licenseKey: this.generateLicenseKey(),
                type: 'manual',
                activatedAt: new Date().toISOString(),
                expiresAt: null,
                requestId: null
            };
            
            this.licenses.push(license);
            this.saveData();
            this.showNotification(`تم إنشاء ترخيص جديد لـ ${clientName}`, 'success');
        }
    }
}

// الدوال العامة
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });
    
    // إزالة الفئة النشطة من جميع عناصر التنقل
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // عرض القسم المطلوب
    document.getElementById(sectionName + 'Section').style.display = 'block';
    
    // إضافة الفئة النشطة لعنصر التنقل
    event.target.closest('.nav-item').classList.add('active');
    
    // تحديث القسم الحالي
    dashboard.currentSection = sectionName;
    dashboard.updateCurrentSection();
}

function refreshData() {
    dashboard.showNotification('جاري تحديث البيانات...', 'info');
    dashboard.loadData();
}

function refreshRequests() {
    dashboard.showNotification('جاري تحديث الطلبات...', 'info');
    dashboard.loadData();
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        window.location.href = '../login.html';
    }
}

// تهيئة اللوحة عند تحميل الصفحة
let dashboard;
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new LicenseDashboard();
});
