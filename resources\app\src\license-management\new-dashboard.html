<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة التراخيص - الجديدة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2d3748;
            font-size: 1.5rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary { background: #4299e1; color: white; }
        .btn-success { background: #48bb78; color: white; }
        .btn-warning { background: #ed8936; color: white; }
        .btn-danger { background: #f56565; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }

        .sidebar {
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            padding: 1.5rem;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #4a5568;
            font-weight: 500;
        }

        .nav-item:hover, .nav-item.active {
            background: #4299e1;
            color: white;
            transform: translateX(-5px);
        }

        .content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #718096;
            font-weight: 500;
        }

        .requests-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .requests-list {
            display: grid;
            gap: 1rem;
        }

        .request-card {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border-right: 4px solid #4299e1;
            transition: all 0.3s ease;
        }

        .request-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .request-name {
            font-weight: bold;
            font-size: 1.1rem;
            color: #2d3748;
        }

        .request-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending {
            background: #fed7d7;
            color: #c53030;
        }

        .request-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .request-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideInRight 0.3s ease-out;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            min-width: 300px;
            max-width: 400px;
        }

        .notification.success { background: #48bb78; }
        .notification.error { background: #f56565; }
        .notification.warning { background: #ed8936; }
        .notification.info { background: #4299e1; }

        .notification-close {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            margin-right: auto;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
            color: #4299e1;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-shield-alt"></i> لوحة إدارة التراخيص</h1>
        <div class="header-actions">
            <button class="btn btn-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> تحديث
            </button>
            <button class="btn btn-warning" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> خروج
            </button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="nav-item active" onclick="showSection('dashboard')">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </div>
            <div class="nav-item" onclick="showSection('requests')">
                <i class="fas fa-file-alt"></i>
                <span>طلبات التفعيل</span>
                <span id="requestsBadge" class="badge">0</span>
            </div>
            <div class="nav-item" onclick="showSection('licenses')">
                <i class="fas fa-key"></i>
                <span>التراخيص المفعلة</span>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Loading -->
            <div id="loading" class="loading">
                <i class="fas fa-spinner"></i>
                <p>جاري التحميل...</p>
            </div>

            <!-- Dashboard Section -->
            <div id="dashboardSection" class="section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div id="totalRequests" class="stat-number" style="color: #4299e1;">0</div>
                        <div class="stat-label">إجمالي الطلبات</div>
                    </div>
                    <div class="stat-card">
                        <div id="pendingRequests" class="stat-number" style="color: #ed8936;">0</div>
                        <div class="stat-label">طلبات معلقة</div>
                    </div>
                    <div class="stat-card">
                        <div id="approvedRequests" class="stat-number" style="color: #48bb78;">0</div>
                        <div class="stat-label">طلبات موافق عليها</div>
                    </div>
                    <div class="stat-card">
                        <div id="activeLicenses" class="stat-number" style="color: #667eea;">0</div>
                        <div class="stat-label">تراخيص مفعلة</div>
                    </div>
                </div>

                <div class="requests-section">
                    <div class="section-header">
                        <h3><i class="fas fa-clock"></i> أحدث الطلبات</h3>
                        <button class="btn btn-primary btn-sm" onclick="showSection('requests')">
                            عرض الكل
                        </button>
                    </div>
                    <div id="recentRequests"></div>
                </div>
            </div>

            <!-- Requests Section -->
            <div id="requestsSection" class="section" style="display: none;">
                <div class="requests-section">
                    <div class="section-header">
                        <h3><i class="fas fa-file-alt"></i> جميع طلبات التفعيل</h3>
                        <button class="btn btn-success btn-sm" onclick="refreshRequests()">
                            <i class="fas fa-sync-alt"></i> تحديث الطلبات
                        </button>
                    </div>
                    <div id="allRequests"></div>
                </div>
            </div>

            <!-- Licenses Section -->
            <div id="licensesSection" class="section" style="display: none;">
                <div class="requests-section">
                    <div class="section-header">
                        <h3><i class="fas fa-key"></i> التراخيص المفعلة</h3>
                        <button class="btn btn-primary btn-sm" onclick="generateLicense()">
                            <i class="fas fa-plus"></i> إنشاء ترخيص جديد
                        </button>
                    </div>
                    <div id="allLicenses"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="new-dashboard.js"></script>
</body>
</html>
