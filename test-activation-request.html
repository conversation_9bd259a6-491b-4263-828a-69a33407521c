<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طلب التفعيل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
            padding: 2rem;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .test-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #48bb78;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-warning {
            background: #ed8936;
        }

        .btn-warning:hover {
            background: #dd6b20;
        }

        .btn-danger {
            background: #f56565;
        }

        .btn-danger:hover {
            background: #e53e3e;
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 1rem;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success {
            background: #48bb78;
        }

        .status-error {
            background: #f56565;
        }

        .status-warning {
            background: #ed8936;
        }

        .status-info {
            background: #4299e1;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-paper-plane"></i> اختبار طلب التفعيل</h1>
            <p>محاكاة إرسال طلب تفعيل من التطبيق إلى لوحة التراخيص</p>
        </div>

        <div class="grid">
            <div class="test-section">
                <h2><i class="fas fa-user-plus"></i> إرسال طلب تفعيل جديد</h2>
                
                <form id="activationForm">
                    <div class="form-group">
                        <label for="fullName">الاسم الكامل</label>
                        <input type="text" id="fullName" name="fullName" value="أحمد محمد الجزائري" required>
                    </div>

                    <div class="form-group">
                        <label for="phone">رقم الهاتف</label>
                        <input type="tel" id="phone" name="phone" value="0555123456" required>
                    </div>

                    <div class="form-group">
                        <label for="state">الولاية</label>
                        <select id="state" name="state" required>
                            <option value="">اختر الولاية</option>
                            <option value="الجزائر" selected>الجزائر</option>
                            <option value="وهران">وهران</option>
                            <option value="قسنطينة">قسنطينة</option>
                            <option value="عنابة">عنابة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="municipality">البلدية</label>
                        <select id="municipality" name="municipality" required>
                            <option value="">اختر البلدية</option>
                            <option value="الجزائر الوسطى" selected>الجزائر الوسطى</option>
                            <option value="باب الوادي">باب الوادي</option>
                            <option value="القصبة">القصبة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="licenseType">نوع الترخيص</label>
                        <select id="licenseType" name="licenseType" required>
                            <option value="trial" selected>تجريبي (30 يوم)</option>
                            <option value="lifetime">مدى الحياة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="ملاحظات إضافية (اختياري)">طلب تفعيل لمحطة وقود جديدة</textarea>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-paper-plane"></i>
                        إرسال طلب التفعيل
                    </button>
                </form>

                <div style="margin-top: 2rem;">
                    <button class="btn btn-warning" onclick="sendMultipleRequests()">
                        <i class="fas fa-clone"></i>
                        إرسال 3 طلبات تجريبية
                    </button>
                    
                    <button class="btn" onclick="clearAllRequests()">
                        <i class="fas fa-trash"></i>
                        مسح جميع الطلبات
                    </button>
                </div>
            </div>

            <div class="test-section">
                <h2><i class="fas fa-list"></i> مراقبة الطلبات</h2>
                
                <button class="btn" onclick="checkRequests()">
                    <i class="fas fa-sync-alt"></i>
                    فحص الطلبات المحفوظة
                </button>
                
                <button class="btn btn-success" onclick="openDashboard()">
                    <i class="fas fa-external-link-alt"></i>
                    فتح لوحة التراخيص
                </button>
                
                <button class="btn btn-danger" onclick="clearLog()">
                    <i class="fas fa-eraser"></i>
                    مسح السجل
                </button>

                <div id="logArea" class="log-area"></div>
            </div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('logArea');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icons = {
                'info': '🔵',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            };
            
            const logEntry = `${icons[type]} [${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLog() {
            logElement.textContent = '';
        }

        function generateDeviceId() {
            return 'device_' + Math.random().toString(36).substr(2, 9);
        }

        function generateRequestId() {
            return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
        }

        // معالج إرسال النموذج
        document.getElementById('activationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            log('بدء إرسال طلب التفعيل...', 'info');
            
            try {
                const formData = new FormData(this);
                const requestData = {
                    id: generateRequestId(),
                    fullName: formData.get('fullName'),
                    phone: formData.get('phone'),
                    state: formData.get('state'),
                    municipality: formData.get('municipality'),
                    licenseType: formData.get('licenseType'),
                    notes: formData.get('notes'),
                    deviceId: generateDeviceId(),
                    requestDate: new Date().toISOString(),
                    status: 'pending'
                };

                // حفظ الطلب في localStorage
                const existingRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                existingRequests.push(requestData);
                localStorage.setItem('activationRequests', JSON.stringify(existingRequests));

                log(`✅ تم إرسال طلب التفعيل بنجاح!`, 'success');
                log(`📋 معرف الطلب: ${requestData.id}`, 'info');
                log(`👤 الاسم: ${requestData.fullName}`, 'info');
                log(`📱 الهاتف: ${requestData.phone}`, 'info');
                log(`📍 الموقع: ${requestData.state} - ${requestData.municipality}`, 'info');
                log(`🔑 نوع الترخيص: ${requestData.licenseType}`, 'info');
                
                // إشعار النوافذ الأخرى بالتحديث
                window.dispatchEvent(new StorageEvent('storage', {
                    key: 'activationRequests',
                    newValue: JSON.stringify(existingRequests)
                }));
                
                log('📡 تم إشعار لوحة التراخيص بالطلب الجديد', 'success');
                
            } catch (error) {
                log(`❌ خطأ في إرسال الطلب: ${error.message}`, 'error');
            }
        });

        async function sendMultipleRequests() {
            log('🔄 إرسال طلبات تجريبية متعددة...', 'info');
            
            const testRequests = [
                {
                    fullName: 'محمد الطاهر بوعلام',
                    phone: '**********',
                    state: 'وهران',
                    municipality: 'وهران',
                    licenseType: 'trial',
                    notes: 'طلب تجريبي 1'
                },
                {
                    fullName: 'فاطمة الزهراء بن علي',
                    phone: '**********',
                    state: 'قسنطينة',
                    municipality: 'قسنطينة',
                    licenseType: 'lifetime',
                    notes: 'طلب تجريبي 2'
                },
                {
                    fullName: 'خالد بن سعد',
                    phone: '**********',
                    state: 'عنابة',
                    municipality: 'عنابة',
                    licenseType: 'trial',
                    notes: 'طلب تجريبي 3'
                }
            ];

            const existingRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            
            for (let i = 0; i < testRequests.length; i++) {
                const request = testRequests[i];
                const requestData = {
                    id: generateRequestId(),
                    ...request,
                    deviceId: generateDeviceId(),
                    requestDate: new Date().toISOString(),
                    status: 'pending'
                };
                
                existingRequests.push(requestData);
                log(`✅ تم إضافة طلب ${i + 1}: ${request.fullName}`, 'success');
                
                // تأخير قصير بين الطلبات
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            localStorage.setItem('activationRequests', JSON.stringify(existingRequests));
            
            // إشعار النوافذ الأخرى
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'activationRequests',
                newValue: JSON.stringify(existingRequests)
            }));
            
            log('🎉 تم إرسال جميع الطلبات التجريبية بنجاح!', 'success');
        }

        function checkRequests() {
            log('🔍 فحص الطلبات المحفوظة...', 'info');
            
            try {
                const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                
                log(`📊 عدد الطلبات المحفوظة: ${requests.length}`, 'info');
                
                if (requests.length > 0) {
                    log('📋 قائمة الطلبات:', 'info');
                    requests.forEach((request, index) => {
                        log(`${index + 1}. ${request.fullName} - ${request.phone} (${request.status})`, 'info');
                    });
                } else {
                    log('📭 لا توجد طلبات محفوظة', 'warning');
                }
                
            } catch (error) {
                log(`❌ خطأ في قراءة الطلبات: ${error.message}`, 'error');
            }
        }

        function clearAllRequests() {
            if (confirm('هل أنت متأكد من مسح جميع الطلبات؟')) {
                localStorage.removeItem('activationRequests');
                log('🗑️ تم مسح جميع الطلبات', 'warning');
                
                // إشعار النوافذ الأخرى
                window.dispatchEvent(new StorageEvent('storage', {
                    key: 'activationRequests',
                    newValue: null
                }));
            }
        }

        function openDashboard() {
            log('🔗 فتح لوحة التراخيص...', 'info');
            window.open('resources/app/src/license-management/admin-dashboard.html', '_blank');
        }

        // تسجيل بدء التطبيق
        window.addEventListener('load', () => {
            log('🚀 تم تحميل أداة اختبار طلب التفعيل', 'success');
            log('💡 يمكنك الآن إرسال طلبات تفعيل تجريبية', 'info');
            checkRequests();
        });
    </script>
</body>
</html>
