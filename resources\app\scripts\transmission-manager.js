// مدير جدول الإرسال الكامل
class TransmissionManager {
    constructor() {
        this.transmissionData = [];
        this.init();
    }

    // تهيئة المدير
    init() {
        this.loadData();
        this.setupEventListeners();
    }

    // تحميل البيانات
    loadData() {
        try {
            // تحميل من localStorage
            const savedData = localStorage.getItem('transmissionTableData');
            if (savedData) {
                this.transmissionData = JSON.parse(savedData);
            } else {
                // تحميل من البيانات الرئيسية
                if (typeof appData !== 'undefined' && appData.transmissionTable) {
                    this.transmissionData = appData.transmissionTable;
                } else {
                    this.transmissionData = [];
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات جدول الإرسال:', error);
            this.transmissionData = [];
        }
    }

    // حفظ البيانات
    saveData() {
        try {
            // حفظ في localStorage
            localStorage.setItem('transmissionTableData', JSON.stringify(this.transmissionData));
            
            // حفظ في البيانات الرئيسية
            if (typeof appData !== 'undefined') {
                appData.transmissionTable = this.transmissionData;
                if (typeof saveData === 'function') {
                    saveData();
                }
            }
        } catch (error) {
            console.error('خطأ في حفظ بيانات جدول الإرسال:', error);
        }
    }

    // إضافة عملية جديدة
    addEntry(data) {
        const entry = {
            id: Date.now().toString(),
            type: data.type, // تركيب أو مراقبة
            tankNumber: data.tankNumber || '',
            carType: data.carType || '',
            serialNumber: data.serialNumber || '',
            registrationNumber: data.registrationNumber,
            ownerName: data.ownerName,
            phoneNumber: data.phoneNumber || '',
            operationDate: data.operationDate || new Date().toISOString().split('T')[0],
            notes: data.notes || '',
            source: data.source || 'manual', // manual, customer_form, certificate
            sourceId: data.sourceId || null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.transmissionData.push(entry);
        this.saveData();
        this.updateDisplay();
        
        return entry;
    }

    // تحديث عملية
    updateEntry(id, data) {
        const index = this.transmissionData.findIndex(entry => entry.id === id);
        if (index !== -1) {
            this.transmissionData[index] = {
                ...this.transmissionData[index],
                ...data,
                updatedAt: new Date().toISOString()
            };
            this.saveData();
            this.updateDisplay();
            return true;
        }
        return false;
    }

    // حذف عملية
    deleteEntry(id) {
        const index = this.transmissionData.findIndex(entry => entry.id === id);
        if (index !== -1) {
            this.transmissionData.splice(index, 1);
            this.saveData();
            this.updateDisplay();
            return true;
        }
        return false;
    }

    // الحصول على العمليات المفلترة
    getFilteredEntries() {
        let filtered = [...this.transmissionData];

        // فلتر النوع
        const typeFilter = document.getElementById('transmission-type-filter')?.value;
        if (typeFilter && typeFilter !== 'all') {
            filtered = filtered.filter(entry => entry.type === typeFilter);
        }

        // فلتر الشهر
        const monthFilter = document.getElementById('transmission-month-filter')?.value;
        if (monthFilter && monthFilter !== 'all') {
            const now = new Date();
            const currentMonth = now.getMonth();
            const currentYear = now.getFullYear();

            if (monthFilter === 'current') {
                filtered = filtered.filter(entry => {
                    const entryDate = new Date(entry.operationDate);
                    return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
                });
            } else if (monthFilter === 'last') {
                const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
                const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
                filtered = filtered.filter(entry => {
                    const entryDate = new Date(entry.operationDate);
                    return entryDate.getMonth() === lastMonth && entryDate.getFullYear() === lastMonthYear;
                });
            }
        }

        // فلتر التاريخ
        const dateFrom = document.getElementById('transmission-date-from')?.value;
        const dateTo = document.getElementById('transmission-date-to')?.value;
        
        if (dateFrom) {
            filtered = filtered.filter(entry => entry.operationDate >= dateFrom);
        }
        if (dateTo) {
            filtered = filtered.filter(entry => entry.operationDate <= dateTo);
        }

        // فلتر البحث
        const searchTerm = document.getElementById('search-transmission')?.value?.toLowerCase();
        if (searchTerm) {
            filtered = filtered.filter(entry =>
                entry.ownerName.toLowerCase().includes(searchTerm) ||
                entry.registrationNumber.toLowerCase().includes(searchTerm) ||
                entry.tankNumber.toLowerCase().includes(searchTerm) ||
                entry.carType.toLowerCase().includes(searchTerm) ||
                (entry.phoneNumber && entry.phoneNumber.toLowerCase().includes(searchTerm))
            );
        }

        return filtered;
    }

    // تحديث العرض
    updateDisplay() {
        this.updateTable();
        this.updateSummary();
    }

    // تحديث الجدول
    updateTable() {
        const tableBody = document.querySelector('#transmission-table-main tbody');
        if (!tableBody) return;

        const filteredData = this.getFilteredEntries();
        
        if (filteredData.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" style="text-align: center; padding: 2rem; color: #6b7280;">
                        <i class="fas fa-table" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <br>لا توجد عمليات مطابقة للفلاتر المحددة
                    </td>
                </tr>
            `;
            return;
        }

        // ترتيب البيانات حسب تاريخ العملية (الأحدث أولاً)
        filteredData.sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate));

        tableBody.innerHTML = filteredData.map((entry, index) => `
            <tr>
                <td>
                    <span class="operation-type ${entry.type}">${entry.type}</span>
                </td>
                <td>${entry.tankNumber || '-'}</td>
                <td>${entry.carType || '-'}</td>
                <td>${entry.serialNumber || '-'}</td>
                <td><strong>${entry.registrationNumber}</strong></td>
                <td>${entry.ownerName}</td>
                <td>${entry.phoneNumber || '-'}</td>
                <td>${this.formatDate(entry.operationDate)}</td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm" onclick="transmissionManager.editEntry('${entry.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm danger" onclick="transmissionManager.confirmDeleteEntry('${entry.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // تحديث الملخص
    updateSummary() {
        const totalCount = this.transmissionData.length;
        const installationCount = this.transmissionData.filter(entry => entry.type === 'تركيب').length;
        const monitoringCount = this.transmissionData.filter(entry => entry.type === 'مراقبة').length;

        // عمليات الشهر الحالي
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        const currentMonthCount = this.transmissionData.filter(entry => {
            const entryDate = new Date(entry.operationDate);
            return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
        }).length;

        // تحديث العناصر
        this.updateElement('transmission-total-count', totalCount);
        this.updateElement('transmission-installation-count', installationCount);
        this.updateElement('transmission-monitoring-count', monitoringCount);
        this.updateElement('transmission-current-month-count', currentMonthCount);
    }

    // تحديث عنصر
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    }

    // تنسيق التاريخ
    formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    // تأكيد حذف عملية
    confirmDeleteEntry(id) {
        if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
            if (this.deleteEntry(id)) {
                if (typeof showToast === 'function') {
                    showToast('تم حذف العملية بنجاح', true);
                }
            } else {
                if (typeof showToast === 'function') {
                    showToast('حدث خطأ أثناء حذف العملية', false);
                }
            }
        }
    }

    // تعديل عملية
    editEntry(id) {
        const entry = this.transmissionData.find(e => e.id === id);
        if (entry && typeof showTransmissionEntryModal === 'function') {
            showTransmissionEntryModal(id);
        }
    }

    // مسح الجدول
    clearTable() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            if (confirm('تأكيد أخير: سيتم حذف جميع العمليات نهائياً. هل تريد المتابعة؟')) {
                this.transmissionData = [];
                this.saveData();
                this.updateDisplay();
                if (typeof showToast === 'function') {
                    showToast('تم مسح جدول الإرسال بنجاح', true);
                }
            }
        }
    }

    // استيراد من الشهادات
    importFromCertificates() {
        if (typeof appData === 'undefined') {
            if (typeof showToast === 'function') {
                showToast('لا يمكن الوصول إلى بيانات الشهادات', false);
            }
            return;
        }

        let importedCount = 0;
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

        // استيراد من شهادات التركيب
        if (appData.installationCertificates) {
            appData.installationCertificates.forEach(cert => {
                const certDate = new Date(cert.installationDate || cert.operationDate);
                if (certDate >= thirtyDaysAgo) {
                    const exists = this.transmissionData.some(entry => 
                        entry.sourceId === cert.id && entry.source === 'installation_certificate'
                    );

                    if (!exists) {
                        this.addEntry({
                            type: 'تركيب',
                            tankNumber: cert.tankNumber || '',
                            carType: cert.vehicleType || cert.carType || '',
                            serialNumber: cert.serialNumber || '',
                            registrationNumber: cert.vehicleNumber || cert.registrationNumber,
                            ownerName: cert.customerName || cert.ownerName,
                            phoneNumber: cert.phoneNumber || '',
                            operationDate: cert.installationDate || cert.operationDate,
                            source: 'installation_certificate',
                            sourceId: cert.id
                        });
                        importedCount++;
                    }
                }
            });
        }

        // استيراد من شهادات المراقبة الدورية
        if (appData.monitoringCertificates) {
            appData.monitoringCertificates.forEach(cert => {
                const certDate = new Date(cert.lastMonitoringDate || cert.operationDate);
                if (certDate >= thirtyDaysAgo) {
                    const exists = this.transmissionData.some(entry => 
                        entry.sourceId === cert.id && entry.source === 'monitoring_certificate'
                    );

                    if (!exists) {
                        this.addEntry({
                            type: 'مراقبة',
                            tankNumber: cert.tankNumber || '',
                            carType: cert.vehicleType || cert.carType || '',
                            serialNumber: cert.serialNumber || '',
                            registrationNumber: cert.vehicleNumber || cert.registrationNumber,
                            ownerName: cert.customerName || cert.ownerName,
                            phoneNumber: cert.phoneNumber || '',
                            operationDate: cert.lastMonitoringDate || cert.operationDate,
                            source: 'monitoring_certificate',
                            sourceId: cert.id
                        });
                        importedCount++;
                    }
                }
            });
        }

        if (importedCount > 0) {
            if (typeof showToast === 'function') {
                showToast(`تم استيراد ${importedCount} عملية من الشهادات`, true);
            }
        } else {
            if (typeof showToast === 'function') {
                showToast('لا توجد عمليات جديدة للاستيراد', false);
            }
        }
    }

    // طباعة الجدول
    printTable() {
        window.open('transmission-table.html', '_blank');
    }

    // تصدير إلى PDF
    exportToPDF() {
        if (typeof showToast === 'function') {
            showToast('ميزة تصدير PDF قيد التطوير', false);
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.bindEvents();
        });

        // إذا كان DOM محمل بالفعل
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.bindEvents());
        } else {
            this.bindEvents();
        }
    }

    // ربط الأحداث
    bindEvents() {
        // أزرار التحكم
        this.bindButton('add-transmission-entry-btn', () => this.showAddEntryModal());
        this.bindButton('import-from-certificates-btn', () => this.importFromCertificates());
        this.bindButton('print-transmission-btn', () => this.printTable());
        this.bindButton('export-transmission-pdf', () => this.exportToPDF());
        this.bindButton('clear-transmission-table-btn', () => this.clearTable());

        // فلاتر
        this.bindElement('transmission-type-filter', 'change', () => this.updateDisplay());
        this.bindElement('transmission-month-filter', 'change', () => this.updateDisplay());
        this.bindElement('transmission-date-from', 'change', () => this.updateDisplay());
        this.bindElement('transmission-date-to', 'change', () => this.updateDisplay());

        // البحث
        this.bindElement('search-transmission', 'input', () => this.updateDisplay());
    }

    // ربط زر
    bindButton(id, handler) {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('click', handler);
        }
    }

    // ربط عنصر
    bindElement(id, event, handler) {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener(event, handler);
        }
    }

    // عرض نافذة إضافة عملية
    showAddEntryModal() {
        if (typeof showTransmissionEntryModal === 'function') {
            showTransmissionEntryModal();
        }
    }
}

// إنشاء مثيل من مدير جدول الإرسال بعد التأكد من تحميل appData
let transmissionManager;

// انتظار تحميل appData
function initTransmissionManager() {
    if (typeof appData !== 'undefined') {
        console.log('✅ تم تحميل appData، إنشاء transmissionManager...');
        transmissionManager = new TransmissionManager();
        console.log('✅ تم إنشاء transmissionManager بنجاح');
    } else {
        console.log('⏳ انتظار تحميل appData...');
        setTimeout(initTransmissionManager, 100);
    }
}

// بدء التهيئة
initTransmissionManager();
