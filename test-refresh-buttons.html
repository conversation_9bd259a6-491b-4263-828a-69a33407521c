<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار التحديث</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            direction: rtl;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .test-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-info {
            background: #17a2b8;
        }

        .btn-warning {
            background: #f39c12;
        }

        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 1rem;
        }

        .status {
            padding: 0.5rem 1rem;
            border-radius: 5px;
            margin: 0.5rem 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .dashboard-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .test-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .test-card h4 {
            margin-bottom: 0.5rem;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1><i class="fas fa-sync-alt"></i> اختبار أزرار التحديث</h1>
            <p>اختبار شامل لجميع أزرار التحديث في لوحة التراخيص</p>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-play"></i> اختبارات التحديث</h2>
            
            <div class="test-grid">
                <div class="test-card">
                    <h4>التحديث العام</h4>
                    <button class="btn btn-info" onclick="testMainRefresh()">
                        <i class="fas fa-sync-alt"></i> اختبار التحديث العام
                    </button>
                </div>
                
                <div class="test-card">
                    <h4>تحديث الطلبات</h4>
                    <button class="btn btn-success" onclick="testRequestsRefresh()">
                        <i class="fas fa-inbox"></i> اختبار تحديث الطلبات
                    </button>
                </div>
                
                <div class="test-card">
                    <h4>تحديث التراخيص</h4>
                    <button class="btn btn-warning" onclick="testLicensesRefresh()">
                        <i class="fas fa-key"></i> اختبار تحديث التراخيص
                    </button>
                </div>
                
                <div class="test-card">
                    <h4>تصدير التراخيص</h4>
                    <button class="btn btn-success" onclick="testExportLicenses()">
                        <i class="fas fa-download"></i> اختبار التصدير
                    </button>
                </div>
                
                <div class="test-card">
                    <h4>اختبار شامل</h4>
                    <button class="btn btn-danger" onclick="runAllTests()">
                        <i class="fas fa-cogs"></i> تشغيل جميع الاختبارات
                    </button>
                </div>
                
                <div class="test-card">
                    <h4>مسح السجل</h4>
                    <button class="btn" onclick="clearLog()">
                        <i class="fas fa-trash"></i> مسح السجل
                    </button>
                </div>
            </div>
            
            <div id="status"></div>
            <div id="log" class="log-area"></div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-external-link-alt"></i> لوحة التراخيص</h2>
            <iframe id="dashboardFrame" class="dashboard-frame" src="resources/app/src/license-management/admin-dashboard.html"></iframe>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            
            logElement.innerHTML += logEntry + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function setStatus(message, type = 'info') {
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            logElement.innerHTML = '';
            statusElement.innerHTML = '';
        }

        function getFrame() {
            return document.getElementById('dashboardFrame');
        }

        function getDashboard() {
            const frame = getFrame();
            return frame.contentWindow.dashboard;
        }

        async function testMainRefresh() {
            log('بدء اختبار التحديث العام...');
            setStatus('جاري اختبار التحديث العام...', 'info');

            try {
                const frame = getFrame();
                const refreshBtn = frame.contentDocument.getElementById('refreshBtn');
                
                if (refreshBtn) {
                    log('✓ تم العثور على زر التحديث العام');
                    refreshBtn.click();
                    log('✓ تم النقر على زر التحديث العام');
                    setStatus('تم اختبار التحديث العام بنجاح', 'success');
                } else {
                    log('✗ لم يتم العثور على زر التحديث العام');
                    setStatus('فشل في العثور على زر التحديث العام', 'error');
                }
            } catch (error) {
                log('✗ خطأ في اختبار التحديث العام: ' + error.message);
                setStatus('خطأ في اختبار التحديث العام', 'error');
            }
        }

        async function testRequestsRefresh() {
            log('بدء اختبار تحديث الطلبات...');
            setStatus('جاري اختبار تحديث الطلبات...', 'info');

            try {
                const frame = getFrame();
                const dashboard = getDashboard();
                
                // Switch to requests section first
                if (dashboard) {
                    dashboard.switchSection('requests');
                    log('✓ تم التبديل إلى قسم الطلبات');
                    
                    setTimeout(() => {
                        const refreshBtn = frame.contentDocument.getElementById('refreshRequestsBtn');
                        if (refreshBtn) {
                            log('✓ تم العثور على زر تحديث الطلبات');
                            refreshBtn.click();
                            log('✓ تم النقر على زر تحديث الطلبات');
                            setStatus('تم اختبار تحديث الطلبات بنجاح', 'success');
                        } else {
                            log('✗ لم يتم العثور على زر تحديث الطلبات');
                            setStatus('فشل في العثور على زر تحديث الطلبات', 'error');
                        }
                    }, 500);
                } else {
                    log('✗ كائن اللوحة غير متاح');
                    setStatus('كائن اللوحة غير متاح', 'error');
                }
            } catch (error) {
                log('✗ خطأ في اختبار تحديث الطلبات: ' + error.message);
                setStatus('خطأ في اختبار تحديث الطلبات', 'error');
            }
        }

        async function testLicensesRefresh() {
            log('بدء اختبار تحديث التراخيص...');
            setStatus('جاري اختبار تحديث التراخيص...', 'info');

            try {
                const frame = getFrame();
                const dashboard = getDashboard();
                
                // Switch to licenses section first
                if (dashboard) {
                    dashboard.switchSection('licenses');
                    log('✓ تم التبديل إلى قسم التراخيص');
                    
                    setTimeout(() => {
                        const refreshBtn = frame.contentDocument.getElementById('refreshLicensesBtn');
                        if (refreshBtn) {
                            log('✓ تم العثور على زر تحديث التراخيص');
                            refreshBtn.click();
                            log('✓ تم النقر على زر تحديث التراخيص');
                            setStatus('تم اختبار تحديث التراخيص بنجاح', 'success');
                        } else {
                            log('✗ لم يتم العثور على زر تحديث التراخيص');
                            setStatus('فشل في العثور على زر تحديث التراخيص', 'error');
                        }
                    }, 500);
                } else {
                    log('✗ كائن اللوحة غير متاح');
                    setStatus('كائن اللوحة غير متاح', 'error');
                }
            } catch (error) {
                log('✗ خطأ في اختبار تحديث التراخيص: ' + error.message);
                setStatus('خطأ في اختبار تحديث التراخيص', 'error');
            }
        }

        async function testExportLicenses() {
            log('بدء اختبار تصدير التراخيص...');
            setStatus('جاري اختبار تصدير التراخيص...', 'info');

            try {
                const frame = getFrame();
                const dashboard = getDashboard();
                
                // Switch to licenses section first
                if (dashboard) {
                    dashboard.switchSection('licenses');
                    log('✓ تم التبديل إلى قسم التراخيص');
                    
                    setTimeout(() => {
                        const exportBtn = frame.contentDocument.getElementById('exportLicensesBtn');
                        if (exportBtn) {
                            log('✓ تم العثور على زر تصدير التراخيص');
                            exportBtn.click();
                            log('✓ تم النقر على زر تصدير التراخيص');
                            setStatus('تم اختبار تصدير التراخيص بنجاح', 'success');
                        } else {
                            log('✗ لم يتم العثور على زر تصدير التراخيص');
                            setStatus('فشل في العثور على زر تصدير التراخيص', 'error');
                        }
                    }, 500);
                } else {
                    log('✗ كائن اللوحة غير متاح');
                    setStatus('كائن اللوحة غير متاح', 'error');
                }
            } catch (error) {
                log('✗ خطأ في اختبار تصدير التراخيص: ' + error.message);
                setStatus('خطأ في اختبار تصدير التراخيص', 'error');
            }
        }

        async function runAllTests() {
            log('بدء تشغيل جميع الاختبارات...');
            setStatus('جاري تشغيل جميع الاختبارات...', 'info');

            await testMainRefresh();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await testRequestsRefresh();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await testLicensesRefresh();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await testExportLicenses();
            
            log('✅ تم الانتهاء من جميع الاختبارات');
            setStatus('تم الانتهاء من جميع الاختبارات', 'success');
        }

        // Auto-start testing
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('مرحباً بك في اختبار أزرار التحديث');
                log('انتظار تحميل لوحة التراخيص...');
                
                // Wait for dashboard to load
                setTimeout(() => {
                    const frame = getFrame();
                    if (frame.contentWindow.dashboard) {
                        log('✓ تم تحميل لوحة التراخيص بنجاح');
                        setStatus('جاهز للاختبار', 'success');
                    } else {
                        log('⚠ لوحة التراخيص لم تحمل بعد');
                        setStatus('انتظار تحميل لوحة التراخيص...', 'info');
                    }
                }, 3000);
            }, 1000);
        });
    </script>
</body>
</html>
