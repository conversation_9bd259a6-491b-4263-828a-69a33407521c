/**
 * أنماط لوحة إحصائيات العمليات
 * Operations Statistics Dashboard Styles
 */

/* حاوي الإحصائيات الرئيسي */
.statistics-dashboard {
    background: var(--bg-color, #f8fafc);
    min-height: 100vh;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
}

/* رأس لوحة الإحصائيات */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color, #3b82f6) 0%, var(--primary-dark, #2563eb) 100%);
    color: white;
    padding: 24px;
    border-radius: 16px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.dashboard-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.dashboard-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 20px;
}

.dashboard-controls {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.time-range-selector {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.time-range-selector:hover {
    background: rgba(255, 255, 255, 0.3);
}

.export-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.export-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

/* بطاقة الإحصائية */
.stat-card {
    background: var(--card-bg, #ffffff);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color, #e5e7eb);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color, #3b82f6), var(--primary-light, #60a5fa));
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.stat-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
}

.stat-icon {
    font-size: 24px;
    padding: 8px;
    border-radius: 8px;
    background: var(--primary-light, #dbeafe);
    color: var(--primary-color, #3b82f6);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color, #3b82f6);
    margin-bottom: 8px;
    line-height: 1;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    font-weight: 500;
}

.stat-change.positive {
    color: #10b981;
}

.stat-change.negative {
    color: #ef4444;
}

.stat-change.neutral {
    color: var(--text-secondary, #6b7280);
}

/* مخططات الإحصائيات */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.chart-container {
    background: var(--card-bg, #ffffff);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color, #e5e7eb);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
}

.chart-controls {
    display: flex;
    gap: 8px;
}

.chart-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color, #e5e7eb);
    background: var(--card-bg, #ffffff);
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-btn.active {
    background: var(--primary-color, #3b82f6);
    color: white;
    border-color: var(--primary-color, #3b82f6);
}

.chart-content {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-50, #f9fafb);
    border-radius: 8px;
    color: var(--text-secondary, #6b7280);
}

/* توزيع العمليات */
.operations-distribution {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.operation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: var(--gray-50, #f9fafb);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.operation-item:hover {
    background: var(--gray-100, #f3f4f6);
}

.operation-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.operation-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.operation-details {
    display: flex;
    flex-direction: column;
}

.operation-name {
    font-weight: 600;
    color: var(--text-primary, #1f2937);
    margin-bottom: 2px;
}

.operation-count {
    font-size: 12px;
    color: var(--text-secondary, #6b7280);
}

.operation-percentage {
    font-weight: 600;
    color: var(--primary-color, #3b82f6);
}

/* شريط التقدم */
.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--gray-200, #e5e7eb);
    border-radius: 3px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color, #3b82f6), var(--primary-light, #60a5fa));
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* جداول الإحصائيات */
.stats-tables {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.stats-table {
    background: var(--card-bg, #ffffff);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color, #e5e7eb);
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary, #1f2937);
}

.table-content {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    text-align: right;
    padding: 12px 8px;
    border-bottom: 2px solid var(--border-color, #e5e7eb);
    font-weight: 600;
    color: var(--text-primary, #1f2937);
    background: var(--gray-50, #f9fafb);
}

.data-table td {
    padding: 12px 8px;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
    color: var(--text-secondary, #6b7280);
}

.data-table tr:hover {
    background: var(--gray-50, #f9fafb);
}

/* مؤشرات الأداء */
.performance-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.performance-card {
    background: var(--card-bg, #ffffff);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border-color, #e5e7eb);
}

.performance-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
}

.performance-label {
    font-size: 14px;
    color: var(--text-secondary, #6b7280);
    margin-bottom: 8px;
}

.performance-trend {
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .statistics-dashboard {
        background: var(--dark-bg-color, #111827);
    }
    
    .stat-card,
    .chart-container,
    .stats-table,
    .performance-card {
        background: var(--dark-card-bg, #1f2937);
        border-color: var(--dark-border-color, #374151);
    }
    
    .stat-title,
    .chart-title,
    .table-title {
        color: var(--dark-text-primary, #f9fafb);
    }
    
    .data-table th {
        background: var(--dark-gray-800, #1f2937);
        color: var(--dark-text-primary, #f9fafb);
    }
    
    .data-table td {
        color: var(--dark-text-secondary, #d1d5db);
    }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 1024px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .stats-tables {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .statistics-dashboard {
        padding: 12px;
    }
    
    .dashboard-header {
        padding: 20px;
    }
    
    .dashboard-title {
        font-size: 24px;
    }
    
    .dashboard-controls {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .chart-container,
    .stats-table {
        padding: 20px;
    }
    
    .performance-indicators {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .dashboard-controls {
        gap: 8px;
    }
    
    .time-range-selector,
    .export-btn {
        width: 100%;
        justify-content: center;
    }
    
    .performance-indicators {
        grid-template-columns: 1fr;
    }
    
    .operation-info {
        gap: 8px;
    }
    
    .operation-icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }
}

/* رسوم متحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card,
.chart-container,
.stats-table {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* تأثيرات التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* تحسينات الطباعة */
@media print {
    .statistics-dashboard {
        background: white;
        padding: 0;
    }
    
    .dashboard-controls,
    .chart-controls {
        display: none;
    }
    
    .stat-card,
    .chart-container,
    .stats-table {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
        margin-bottom: 20px;
    }
}
