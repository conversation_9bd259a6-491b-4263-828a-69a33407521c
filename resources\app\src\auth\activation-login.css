/* تصميم نظام تفعيل الترخيص */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #ecf0f1;
    --dark-text: #2c3e50;
    --light-text: #7f8c8d;
    --border-color: #bdc3c7;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    color: var(--dark-text);
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.logo i {
    font-size: 2.5rem;
    color: var(--secondary-color);
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
}

.version {
    color: var(--light-text);
    font-size: 0.9rem;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Status Card */
.status-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    backdrop-filter: blur(10px);
}

.status-icon {
    font-size: 3rem;
    color: var(--warning-color);
    margin-bottom: 20px;
}

.status-card h2 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.status-card p {
    color: var(--light-text);
    margin-bottom: 20px;
}

.status-details {
    display: grid;
    gap: 15px;
    margin-top: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: var(--light-bg);
    border-radius: 5px;
}

.detail-item .label {
    font-weight: 600;
    color: var(--dark-text);
}

.detail-item .value {
    font-weight: 500;
}

.value.active {
    color: var(--success-color);
}

.value.inactive {
    color: var(--danger-color);
}

.value.pending {
    color: var(--warning-color);
}

/* Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--light-text);
    color: white;
}

.btn-secondary:hover {
    background: #6c7b7d;
    transform: translateY(-2px);
}

.btn-info {
    background: var(--warning-color);
    color: white;
}

.btn-info:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Form Container */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--light-bg);
}

.form-header h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--light-text);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.close-btn:hover {
    background: var(--light-bg);
    color: var(--danger-color);
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-text);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group input:disabled,
.form-group select:disabled {
    background: var(--light-bg);
    cursor: not-allowed;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-top: 30px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.footer p {
    color: var(--light-text);
    margin-bottom: 10px;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.footer-links a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.loading-spinner p {
    color: var(--dark-text);
    font-weight: 600;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 1001;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification.success {
    border-right: 4px solid var(--success-color);
}

.notification.error {
    border-right: 4px solid var(--danger-color);
}

.notification.warning {
    border-right: 4px solid var(--warning-color);
}

.notification.info {
    border-right: 4px solid var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .logo h1 {
        font-size: 1.4rem;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 10px;
    }
}
