<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص نظام توجيه العمليات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .form-test {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .form-test input, .form-test select {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 فحص نظام توجيه العمليات</h1>
        
        <div class="debug-section">
            <h3>📊 حالة النظام</h3>
            <div id="system-status">جاري التحقق...</div>
            <button class="test-button" onclick="checkSystemStatus()">تحديث حالة النظام</button>
        </div>

        <div class="debug-section">
            <h3>🧪 اختبارات سريعة</h3>
            <button class="test-button" onclick="testDataStructures()">فحص هياكل البيانات</button>
            <button class="test-button" onclick="testTransmissionManager()">فحص مدير الإرسال</button>
            <button class="test-button" onclick="testOperationTypeDropdown()">فحص قائمة نوع العملية</button>
            <button class="test-button" onclick="simulateFormSubmission()">محاكاة إرسال النموذج</button>
        </div>

        <div class="debug-section">
            <h3>📝 اختبار النموذج المباشر</h3>
            <div class="form-test">
                <label>نوع العملية:</label>
                <select id="test-operation-type">
                    <option value="">اختر نوع العملية</option>
                    <option value="تركيب">تركيب / Installation</option>
                    <option value="مراقبة">مراقبة دورية / Contrôle Périodique</option>
                    <option value="تجديد">تجديد بطاقة / Renouvellement de Carte</option>
                </select>
                <br>
                <label>اسم الزبون:</label>
                <input type="text" id="test-customer-name" value="زبون تجريبي" placeholder="اسم الزبون">
                <br>
                <label>رقم الهاتف:</label>
                <input type="text" id="test-customer-phone" value="0555123456" placeholder="رقم الهاتف">
                <br>
                <button class="test-button" onclick="testDirectOperation()">اختبار العملية مباشرة</button>
            </div>
        </div>

        <div class="debug-section">
            <h3>📋 عرض البيانات</h3>
            <button class="test-button" onclick="showGasCardsData()">عرض بطاقات الغاز</button>
            <button class="test-button" onclick="showTransmissionData()">عرض جدول الإرسال</button>
            <button class="test-button" onclick="clearConsole()">مسح وحدة التحكم</button>
        </div>

        <div class="debug-section">
            <h3>🖥️ وحدة التحكم</h3>
            <div id="console-output" class="console-output">جاري التحميل...</div>
        </div>
    </div>

    <!-- تحميل الملفات الأساسية بالترتيب الصحيح -->
    <script src="scripts/script.js"></script>
    <script src="scripts/transmission-manager.js"></script>

    <script>
        // ربط وحدة التحكم بالعرض
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        // وظائف الفحص
        function checkSystemStatus() {
            console.log('🔍 فحص حالة النظام...');

            const statusDiv = document.getElementById('system-status');
            let status = '';

            // فحص البيانات الأساسية
            if (typeof appData !== 'undefined') {
                status += '<span class="status-good">✅ appData متاح</span><br>';
                status += `📊 عدد الزبائن: ${appData.customers?.length || 0}<br>`;
                status += `🎫 عدد بطاقات الغاز: ${appData.gasCards?.length || 0}<br>`;
                status += `📋 عدد عمليات الإرسال: ${appData.transmissionTable?.length || 0}<br>`;

                // فحص تهيئة appData.transmissionTable
                if (Array.isArray(appData.transmissionTable)) {
                    status += '<span class="status-good">✅ appData.transmissionTable مُهيأ بشكل صحيح</span><br>';
                } else {
                    status += '<span class="status-error">❌ appData.transmissionTable غير مُهيأ</span><br>';
                }
            } else {
                status += '<span class="status-error">❌ appData غير متاح</span><br>';
            }

            // فحص transmissionManager
            if (typeof transmissionManager !== 'undefined' && transmissionManager !== null) {
                status += '<span class="status-good">✅ transmissionManager متاح</span><br>';
                status += `📊 عدد العمليات في transmissionManager: ${transmissionManager.transmissionData?.length || 0}<br>`;
            } else {
                status += '<span class="status-warning">⚠️ transmissionManager غير متاح</span><br>';
            }

            // فحص عنصر operation-type
            const operationTypeElement = document.getElementById('operation-type');
            if (operationTypeElement) {
                status += '<span class="status-good">✅ عنصر operation-type موجود</span><br>';
                status += `📋 القيمة الحالية: "${operationTypeElement.value}"<br>`;
            } else {
                status += '<span class="status-warning">⚠️ عنصر operation-type غير موجود (طبيعي في صفحة التشخيص)</span><br>';
            }

            statusDiv.innerHTML = status;
        }

        function testDataStructures() {
            console.log('🧪 اختبار هياكل البيانات...');
            
            if (typeof appData === 'undefined') {
                console.error('❌ appData غير متاح');
                return;
            }
            
            console.log('📊 فحص appData.gasCards:', Array.isArray(appData.gasCards));
            console.log('📊 فحص appData.transmissionTable:', Array.isArray(appData.transmissionTable));
            console.log('📊 فحص appData.customers:', Array.isArray(appData.customers));
            console.log('📊 فحص appData.vehicles:', Array.isArray(appData.vehicles));
            
            console.log('✅ انتهى فحص هياكل البيانات');
        }

        function testTransmissionManager() {
            console.log('🧪 اختبار مدير الإرسال...');

            if (typeof transmissionManager === 'undefined' || transmissionManager === null) {
                console.warn('⚠️ transmissionManager غير متاح');
                console.log('💡 تحقق من تحميل الملفات بالترتيب الصحيح');
                console.log('💡 تأكد من تهيئة appData قبل transmissionManager');
                return;
            }

            console.log('✅ transmissionManager متاح');
            console.log('📋 الوظائف المتاحة:', Object.getOwnPropertyNames(transmissionManager));
            console.log('📊 عدد العمليات الحالي:', transmissionManager.transmissionData?.length || 0);

            // اختبار إضافة عملية تجريبية
            const testEntry = {
                type: 'اختبار',
                tankNumber: 'TEST-001',
                carType: 'سيارة اختبار',
                serialNumber: 'TEST-001',
                registrationNumber: 'TEST-123',
                ownerName: 'زبون اختبار',
                phoneNumber: '0555000000',
                operationDate: new Date().toISOString().split('T')[0],
                notes: 'عملية اختبار من صفحة التشخيص',
                source: 'debug_test'
            };

            console.log('🧪 إضافة عملية اختبار:', testEntry);
            try {
                const beforeCount = transmissionManager.transmissionData?.length || 0;
                console.log('📊 عدد العمليات قبل الإضافة:', beforeCount);

                const result = transmissionManager.addEntry(testEntry);
                console.log('✅ تمت إضافة العملية بنجاح:', result);

                const afterCount = transmissionManager.transmissionData?.length || 0;
                console.log('📊 عدد العمليات بعد الإضافة:', afterCount);
                console.log('📈 الفرق:', afterCount - beforeCount);

                // فحص البيانات في appData أيضاً
                if (typeof appData !== 'undefined' && Array.isArray(appData.transmissionTable)) {
                    console.log('📊 عدد العمليات في appData.transmissionTable:', appData.transmissionTable.length);
                }
            } catch (error) {
                console.error('❌ خطأ في إضافة العملية:', error);
            }
        }

        function testOperationTypeDropdown() {
            console.log('🧪 اختبار قائمة نوع العملية...');
            
            const operationTypeElement = document.getElementById('operation-type');
            if (!operationTypeElement) {
                console.error('❌ عنصر operation-type غير موجود في الصفحة الرئيسية');
                return;
            }
            
            console.log('✅ عنصر operation-type موجود');
            console.log('📋 القيمة الحالية:', operationTypeElement.value);
            console.log('📋 الخيارات المتاحة:', Array.from(operationTypeElement.options).map(opt => `${opt.value} - ${opt.text}`));
        }

        function testDirectOperation() {
            console.log('🧪 اختبار العملية المباشر...');
            
            const operationType = document.getElementById('test-operation-type').value;
            const customerName = document.getElementById('test-customer-name').value;
            const customerPhone = document.getElementById('test-customer-phone').value;
            
            if (!operationType) {
                console.error('❌ يرجى اختيار نوع العملية');
                return;
            }
            
            console.log('📝 بيانات الاختبار:');
            console.log('  - نوع العملية:', operationType);
            console.log('  - اسم الزبون:', customerName);
            console.log('  - رقم الهاتف:', customerPhone);
            
            // محاكاة إضافة البيانات
            if (operationType === 'تجديد') {
                console.log('🎫 محاكاة إضافة بطاقة غاز...');
                const testCard = {
                    id: 'test-card-' + Date.now(),
                    customerName: customerName,
                    operationType: 'تجديد بطاقة',
                    phoneNumber: customerPhone,
                    createdAt: new Date().toISOString()
                };

                if (typeof appData !== 'undefined' && Array.isArray(appData.gasCards)) {
                    const beforeCount = appData.gasCards.length;
                    appData.gasCards.push(testCard);
                    console.log('✅ تمت إضافة البطاقة التجريبية');
                    console.log('📊 عدد البطاقات قبل:', beforeCount, 'بعد:', appData.gasCards.length);
                } else {
                    console.error('❌ لا يمكن إضافة البطاقة - appData.gasCards غير متاح');
                }
            } else if (operationType === 'تركيب' || operationType === 'مراقبة') {
                console.log('📋 محاكاة إضافة عملية إرسال...');

                // اختبار باستخدام transmissionManager أولاً
                if (typeof transmissionManager !== 'undefined' && transmissionManager !== null) {
                    console.log('✅ استخدام transmissionManager...');
                    const testEntry = {
                        type: operationType,
                        tankNumber: 'TEST-' + Date.now(),
                        carType: 'سيارة اختبار',
                        serialNumber: 'SN-' + Date.now(),
                        registrationNumber: 'REG-' + Date.now(),
                        ownerName: customerName,
                        phoneNumber: customerPhone,
                        operationDate: new Date().toISOString().split('T')[0],
                        notes: 'اختبار من صفحة التشخيص',
                        source: 'debug_test'
                    };

                    try {
                        const beforeCount = transmissionManager.transmissionData?.length || 0;
                        const result = transmissionManager.addEntry(testEntry);
                        const afterCount = transmissionManager.transmissionData?.length || 0;
                        console.log('✅ تمت إضافة العملية عبر transmissionManager');
                        console.log('📊 عدد العمليات قبل:', beforeCount, 'بعد:', afterCount);
                        console.log('📋 العملية المضافة:', result);
                    } catch (error) {
                        console.error('❌ خطأ في transmissionManager:', error);
                    }
                } else {
                    console.warn('⚠️ transmissionManager غير متاح، استخدام appData مباشرة...');
                    const testEntry = {
                        id: 'test-entry-' + Date.now(),
                        type: operationType,
                        ownerName: customerName,
                        phoneNumber: customerPhone,
                        operationDate: new Date().toISOString().split('T')[0],
                        source: 'debug_test',
                        createdAt: new Date().toISOString()
                    };

                    if (typeof appData !== 'undefined' && Array.isArray(appData.transmissionTable)) {
                        const beforeCount = appData.transmissionTable.length;
                        appData.transmissionTable.push(testEntry);
                        console.log('✅ تمت إضافة العملية مباشرة إلى appData');
                        console.log('📊 عدد العمليات قبل:', beforeCount, 'بعد:', appData.transmissionTable.length);
                    } else {
                        console.error('❌ لا يمكن إضافة العملية - appData.transmissionTable غير متاح');
                    }
                }
            }
        }

        function showGasCardsData() {
            console.log('📊 عرض بيانات بطاقات الغاز...');
            
            if (typeof appData === 'undefined' || !Array.isArray(appData.gasCards)) {
                console.error('❌ بيانات بطاقات الغاز غير متاحة');
                return;
            }
            
            console.log(`📊 إجمالي عدد البطاقات: ${appData.gasCards.length}`);
            appData.gasCards.forEach((card, index) => {
                console.log(`🎫 البطاقة ${index + 1}:`, {
                    id: card.id,
                    customerName: card.customerName,
                    operationType: card.operationType,
                    createdAt: card.createdAt
                });
            });
        }

        function showTransmissionData() {
            console.log('📊 عرض بيانات جدول الإرسال...');
            
            if (typeof appData === 'undefined' || !Array.isArray(appData.transmissionTable)) {
                console.error('❌ بيانات جدول الإرسال غير متاحة');
                return;
            }
            
            console.log(`📊 إجمالي عدد العمليات: ${appData.transmissionTable.length}`);
            appData.transmissionTable.forEach((entry, index) => {
                console.log(`📋 العملية ${index + 1}:`, {
                    id: entry.id,
                    type: entry.type,
                    ownerName: entry.ownerName,
                    source: entry.source,
                    createdAt: entry.createdAt
                });
            });
        }

        function simulateFormSubmission() {
            console.log('🧪 محاكاة إرسال النموذج...');
            
            // محاولة الوصول إلى النموذج الرئيسي
            const customerForm = document.getElementById('customer-form');
            if (!customerForm) {
                console.error('❌ نموذج الزبون غير موجود في هذه الصفحة');
                console.log('💡 يرجى تشغيل هذا الاختبار من الصفحة الرئيسية');
                return;
            }
            
            console.log('✅ تم العثور على نموذج الزبون');
            // يمكن إضافة المزيد من الاختبارات هنا
        }

        // وظيفة لإعادة تحميل transmissionManager
        function reloadTransmissionManager() {
            console.log('🔄 إعادة تحميل transmissionManager...');

            // محاولة إعادة تحميل الملف
            const script = document.createElement('script');
            script.src = 'scripts/transmission-manager.js';
            script.onload = function() {
                console.log('✅ تم إعادة تحميل transmission-manager.js');
                setTimeout(() => {
                    checkSystemStatus();
                }, 1000);
            };
            script.onerror = function() {
                console.error('❌ فشل في إعادة تحميل transmission-manager.js');
            };

            document.head.appendChild(script);
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🚀 تم تحميل صفحة فحص نظام توجيه العمليات');
            console.log('📂 تحميل الملفات الأساسية...');

            // انتظار تحميل appData و transmissionManager
            let checkCount = 0;
            const maxChecks = 50; // 5 ثوان كحد أقصى

            function waitForInit() {
                checkCount++;
                console.log(`⏳ فحص التهيئة (${checkCount}/${maxChecks})...`);

                if (typeof appData !== 'undefined') {
                    console.log('✅ تم تحميل appData');
                    if (typeof transmissionManager !== 'undefined') {
                        console.log('✅ تم تحميل transmissionManager');
                        console.log('🎯 النظام جاهز للاختبار!');
                        checkSystemStatus();
                    } else {
                        console.log('⏳ انتظار تحميل transmissionManager...');
                        if (checkCount < maxChecks) {
                            setTimeout(waitForInit, 100);
                        } else {
                            console.error('❌ انتهت مهلة انتظار transmissionManager');
                            checkSystemStatus();
                        }
                    }
                } else {
                    console.log('⏳ انتظار تحميل appData...');
                    if (checkCount < maxChecks) {
                        setTimeout(waitForInit, 100);
                    } else {
                        console.error('❌ انتهت مهلة انتظار appData');
                        checkSystemStatus();
                    }
                }
            }

            waitForInit();

            // إضافة زر إعادة التحميل
            const reloadBtn = document.createElement('button');
            reloadBtn.textContent = '🔄 إعادة تحميل TransmissionManager';
            reloadBtn.onclick = reloadTransmissionManager;
            reloadBtn.style.cssText = 'margin: 10px; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;';

            const container = document.querySelector('.container');
            if (container) {
                container.appendChild(reloadBtn);
            }
        });
    </script>
</body>
</html>
