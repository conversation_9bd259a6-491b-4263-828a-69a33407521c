/**
 * نظام الإشعارات التفاعلية المحسن
 * Enhanced Interactive Notifications System
 * 
 * يوفر إشعارات تفاعلية مع روابط مباشرة للانتقال إلى البيانات المحفوظة
 * وإمكانيات متقدمة للتفاعل مع الإشعارات
 */

class EnhancedNotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 10;
        this.defaultDuration = 6000;
        this.soundEnabled = true;
        this.init();
    }

    init() {
        this.createContainer();
        this.loadSounds();
        this.setupEventListeners();
        console.log('✅ تم تهيئة نظام الإشعارات التفاعلية المحسن');
    }

    createContainer() {
        // إنشاء حاوي الإشعارات إذا لم يكن موجوداً
        this.container = document.getElementById('enhanced-notifications-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'enhanced-notifications-container';
            this.container.className = 'enhanced-notifications-container';
            document.body.appendChild(this.container);
        }
    }

    loadSounds() {
        // تحميل الأصوات للإشعارات
        this.sounds = {
            success: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            error: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            warning: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            info: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')
        };
    }

    setupEventListeners() {
        // مستمع للنقر خارج الإشعارات لإخفائها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.enhanced-notification')) {
                this.hideAllNonPersistent();
            }
        });

        // مستمع لاختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAll();
            }
        });
    }

    // إنشاء إشعار تفاعلي محسن
    show(options) {
        const {
            type = 'info',
            title = 'إشعار',
            message = '',
            duration = this.defaultDuration,
            persistent = false,
            actions = [],
            relatedData = null,
            navigateToData = null,
            showProgress = true,
            priority = 'normal'
        } = options;

        const notification = this.createNotification({
            type,
            title,
            message,
            persistent,
            actions,
            relatedData,
            navigateToData,
            showProgress,
            priority
        });

        this.addToContainer(notification);
        this.playSound(type);

        // إخفاء تلقائي للإشعارات غير الدائمة
        if (!persistent && duration > 0) {
            this.scheduleHide(notification, duration, showProgress);
        }

        return notification;
    }

    createNotification(options) {
        const {
            type,
            title,
            message,
            persistent,
            actions,
            relatedData,
            navigateToData,
            showProgress,
            priority
        } = options;

        const notification = document.createElement('div');
        notification.className = `enhanced-notification ${type} priority-${priority}`;
        
        // إضافة معرف فريد
        notification.id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // أيقونات الإشعارات
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            appointment: '📅',
            customer: '👤',
            vehicle: '🚗',
            tank: '⛽',
            card: '💳'
        };

        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-icon">${icons[type] || icons.info}</div>
                <div class="notification-title">${title}</div>
                <div class="notification-controls">
                    ${persistent ? '<div class="persistent-indicator">📌</div>' : ''}
                    <button class="notification-close" onclick="enhancedNotifications.hide('${notification.id}')">×</button>
                </div>
            </div>
            <div class="notification-content">
                <div class="notification-message">${message}</div>
                ${relatedData ? this.createRelatedDataSection(relatedData) : ''}
                ${actions.length > 0 ? this.createActionsSection(actions, notification.id) : ''}
                ${navigateToData ? this.createNavigationSection(navigateToData) : ''}
            </div>
            ${showProgress && !persistent ? '<div class="notification-progress"><div class="progress-bar"></div></div>' : ''}
        `;

        return notification;
    }

    createRelatedDataSection(relatedData) {
        return `
            <div class="related-data-section">
                <div class="related-data-title">البيانات المرتبطة:</div>
                <div class="related-data-content">
                    ${Object.entries(relatedData).map(([key, value]) => 
                        `<div class="data-item"><span class="data-key">${key}:</span> <span class="data-value">${value}</span></div>`
                    ).join('')}
                </div>
            </div>
        `;
    }

    createActionsSection(actions, notificationId) {
        return `
            <div class="notification-actions">
                ${actions.map((action, index) => `
                    <button class="action-btn ${action.style || 'primary'}" 
                            onclick="enhancedNotifications.executeAction('${notificationId}', ${index})">
                        ${action.icon ? `<i class="${action.icon}"></i>` : ''}
                        ${action.label}
                    </button>
                `).join('')}
            </div>
        `;
    }

    createNavigationSection(navigateToData) {
        return `
            <div class="navigation-section">
                <button class="navigate-btn" onclick="enhancedNotifications.navigateToData('${navigateToData.type}', '${navigateToData.id}')">
                    <i class="fas fa-external-link-alt"></i>
                    انتقل إلى ${navigateToData.label || 'البيانات'}
                </button>
            </div>
        `;
    }

    addToContainer(notification) {
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // تأثير الظهور
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // الحد من عدد الإشعارات
        this.limitNotifications();
    }

    scheduleHide(notification, duration, showProgress) {
        if (showProgress) {
            const progressBar = notification.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.animationDuration = `${duration}ms`;
                progressBar.classList.add('animate');
            }
        }

        setTimeout(() => {
            this.hide(notification.id);
        }, duration);
    }

    hide(notificationId) {
        const notification = document.getElementById(notificationId);
        if (notification) {
            notification.classList.add('hide');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications = this.notifications.filter(n => n.id !== notificationId);
            }, 300);
        }
    }

    hideAll() {
        this.notifications.forEach(notification => {
            this.hide(notification.id);
        });
    }

    hideAllNonPersistent() {
        this.notifications.forEach(notification => {
            if (!notification.classList.contains('persistent')) {
                this.hide(notification.id);
            }
        });
    }

    limitNotifications() {
        while (this.notifications.length > this.maxNotifications) {
            const oldestNotification = this.notifications[0];
            this.hide(oldestNotification.id);
        }
    }

    playSound(type) {
        if (this.soundEnabled && this.sounds[type]) {
            this.sounds[type].volume = 0.3;
            this.sounds[type].play().catch(() => {
                // تجاهل أخطاء تشغيل الصوت
            });
        }
    }

    executeAction(notificationId, actionIndex) {
        const notification = document.getElementById(notificationId);
        if (notification && notification.actions && notification.actions[actionIndex]) {
            const action = notification.actions[actionIndex];
            if (typeof action.callback === 'function') {
                action.callback();
            } else if (typeof action.callback === 'string') {
                eval(action.callback);
            }
        }
    }

    navigateToData(type, id) {
        // التنقل إلى البيانات المحددة
        switch (type) {
            case 'customer':
                this.navigateToCustomer(id);
                break;
            case 'vehicle':
                this.navigateToVehicle(id);
                break;
            case 'appointment':
                this.navigateToAppointment(id);
                break;
            case 'transmission':
                this.navigateToTransmission(id);
                break;
            case 'gascard':
                this.navigateToGasCard(id);
                break;
            default:
                console.warn('نوع البيانات غير مدعوم:', type);
        }
    }

    navigateToCustomer(customerId) {
        // فتح نافذة الزبائن والتمرير إلى الزبون المحدد
        const customersTab = document.querySelector('[data-tab="customers"]');
        if (customersTab) {
            customersTab.click();
            setTimeout(() => {
                const customerRow = document.querySelector(`[data-customer-id="${customerId}"]`);
                if (customerRow) {
                    customerRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    customerRow.classList.add('highlight-row');
                    setTimeout(() => customerRow.classList.remove('highlight-row'), 3000);
                }
            }, 500);
        }
    }

    navigateToVehicle(vehicleId) {
        // منطق مشابه للسيارات
        console.log('الانتقال إلى السيارة:', vehicleId);
    }

    navigateToAppointment(appointmentId) {
        // منطق مشابه للمواعيد
        console.log('الانتقال إلى الموعد:', appointmentId);
    }

    navigateToTransmission(transmissionId) {
        // فتح جدول الإرسال
        window.open('transmission-table.html', '_blank');
    }

    navigateToGasCard(cardId) {
        // فتح نافذة بطاقات الغاز
        const gasCardsTab = document.querySelector('[data-tab="gas-cards"]');
        if (gasCardsTab) {
            gasCardsTab.click();
        }
    }

    // إشعارات مخصصة للأحداث المختلفة
    showDataSaved(dataType, dataId, dataName) {
        return this.show({
            type: 'success',
            title: 'تم الحفظ بنجاح',
            message: `تم حفظ ${dataType} "${dataName}" بنجاح`,
            relatedData: {
                'النوع': dataType,
                'المعرف': dataId,
                'الاسم': dataName,
                'التاريخ': new Date().toLocaleString('ar-EG')
            },
            navigateToData: {
                type: dataType.toLowerCase(),
                id: dataId,
                label: dataName
            },
            actions: [
                {
                    label: 'عرض البيانات',
                    icon: 'fas fa-eye',
                    style: 'primary',
                    callback: () => this.navigateToData(dataType.toLowerCase(), dataId)
                },
                {
                    label: 'إضافة جديد',
                    icon: 'fas fa-plus',
                    style: 'secondary',
                    callback: () => this.openNewForm(dataType)
                }
            ]
        });
    }

    showValidationError(errors) {
        return this.show({
            type: 'error',
            title: 'خطأ في التحقق من البيانات',
            message: `تم العثور على ${errors.length} خطأ في البيانات`,
            persistent: true,
            relatedData: {
                'عدد الأخطاء': errors.length,
                'الأخطاء': errors.join(', ')
            },
            actions: [
                {
                    label: 'إصلاح الأخطاء',
                    icon: 'fas fa-wrench',
                    style: 'primary',
                    callback: () => this.highlightErrors(errors)
                }
            ]
        });
    }

    openNewForm(dataType) {
        // فتح نموذج جديد حسب نوع البيانات
        switch (dataType.toLowerCase()) {
            case 'customer':
                document.getElementById('add-customer-btn')?.click();
                break;
            case 'vehicle':
                document.getElementById('add-vehicle-btn')?.click();
                break;
            default:
                console.log('فتح نموذج جديد لـ:', dataType);
        }
    }

    highlightErrors(errors) {
        // تمييز الحقول التي تحتوي على أخطاء
        errors.forEach(error => {
            const field = document.querySelector(`[data-error="${error}"]`);
            if (field) {
                field.classList.add('error-highlight');
                setTimeout(() => field.classList.remove('error-highlight'), 5000);
            }
        });
    }
}

// إنشاء مثيل عام للنظام
const enhancedNotifications = new EnhancedNotificationSystem();

    // إشعارات مخصصة للمواعيد
    showAppointmentReminder(appointment) {
        const customer = appData.customers.find(c => c.id === appointment.customerId);
        const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);

        return this.show({
            type: 'appointment',
            title: 'تذكير بموعد',
            message: `لديك موعد اليوم الساعة ${appointment.time}`,
            persistent: true,
            priority: 'high',
            relatedData: {
                'الزبون': customer?.name || 'غير معروف',
                'السيارة': vehicle?.plateNumber || 'غير معروف',
                'الخدمة': appointment.service,
                'الوقت': appointment.time,
                'التاريخ': appointment.date
            },
            navigateToData: {
                type: 'appointment',
                id: appointment.id,
                label: `موعد ${customer?.name}`
            },
            actions: [
                {
                    label: 'تأكيد الحضور',
                    icon: 'fas fa-check',
                    style: 'primary',
                    callback: () => this.confirmAppointment(appointment.id)
                },
                {
                    label: 'إعادة جدولة',
                    icon: 'fas fa-calendar-alt',
                    style: 'secondary',
                    callback: () => this.rescheduleAppointment(appointment.id)
                }
            ]
        });
    }

    // إشعار انتهاء صلاحية البطاقة
    showCardExpiry(card) {
        const customer = appData.customers.find(c => c.id === card.customerId);

        return this.show({
            type: 'warning',
            title: 'انتهاء صلاحية البطاقة',
            message: `بطاقة الغاز رقم ${card.cardNumber} ستنتهي صلاحيتها قريباً`,
            persistent: true,
            relatedData: {
                'رقم البطاقة': card.cardNumber,
                'الزبون': customer?.name || 'غير معروف',
                'تاريخ الانتهاء': card.expiryDate,
                'الأيام المتبقية': Math.ceil((new Date(card.expiryDate) - new Date()) / (1000 * 60 * 60 * 24))
            },
            navigateToData: {
                type: 'gascard',
                id: card.id,
                label: `بطاقة ${card.cardNumber}`
            },
            actions: [
                {
                    label: 'تجديد البطاقة',
                    icon: 'fas fa-sync',
                    style: 'primary',
                    callback: () => this.renewCard(card.id)
                },
                {
                    label: 'تذكير لاحقاً',
                    icon: 'fas fa-clock',
                    style: 'secondary',
                    callback: () => this.snoozeReminder(card.id)
                }
            ]
        });
    }

    // إشعار نجاح النسخ الاحتياطي
    showBackupSuccess(backupInfo) {
        return this.show({
            type: 'success',
            title: 'تم إنشاء النسخة الاحتياطية',
            message: `تم حفظ النسخة الاحتياطية بنجاح`,
            relatedData: {
                'اسم الملف': backupInfo.filename,
                'الحجم': backupInfo.size,
                'التاريخ': new Date().toLocaleString('ar-EG'),
                'عدد السجلات': backupInfo.recordCount
            },
            actions: [
                {
                    label: 'فتح المجلد',
                    icon: 'fas fa-folder-open',
                    style: 'primary',
                    callback: () => this.openBackupFolder()
                },
                {
                    label: 'إرسال عبر تيليجرام',
                    icon: 'fab fa-telegram',
                    style: 'secondary',
                    callback: () => this.sendToTelegram(backupInfo.filename)
                }
            ]
        });
    }

    // وظائف مساعدة للإجراءات
    confirmAppointment(appointmentId) {
        console.log('تأكيد الموعد:', appointmentId);
        // منطق تأكيد الموعد
    }

    rescheduleAppointment(appointmentId) {
        console.log('إعادة جدولة الموعد:', appointmentId);
        // فتح نافذة إعادة الجدولة
    }

    renewCard(cardId) {
        console.log('تجديد البطاقة:', cardId);
        // فتح نموذج تجديد البطاقة
    }

    snoozeReminder(cardId) {
        console.log('تأجيل التذكير:', cardId);
        // تأجيل التذكير لفترة محددة
    }

    openBackupFolder() {
        console.log('فتح مجلد النسخ الاحتياطية');
        // فتح مجلد النسخ الاحتياطية
    }

    sendToTelegram(filename) {
        console.log('إرسال عبر تيليجرام:', filename);
        // إرسال الملف عبر تيليجرام
    }

    // إشعار تحديث التطبيق
    showUpdateAvailable(updateInfo) {
        return this.show({
            type: 'info',
            title: 'تحديث متوفر',
            message: `يتوفر تحديث جديد للتطبيق (الإصدار ${updateInfo.version})`,
            persistent: true,
            priority: 'normal',
            relatedData: {
                'الإصدار الجديد': updateInfo.version,
                'الإصدار الحالي': updateInfo.currentVersion,
                'حجم التحديث': updateInfo.size,
                'ميزات جديدة': updateInfo.features?.join(', ') || 'تحسينات عامة'
            },
            actions: [
                {
                    label: 'تحديث الآن',
                    icon: 'fas fa-download',
                    style: 'primary',
                    callback: () => this.startUpdate()
                },
                {
                    label: 'تذكير لاحقاً',
                    icon: 'fas fa-clock',
                    style: 'secondary',
                    callback: () => this.hide()
                }
            ]
        });
    }

    startUpdate() {
        console.log('بدء تحديث التطبيق');
        // منطق تحديث التطبيق
    }

    // إشعار حالة الاتصال
    showConnectionStatus(isOnline) {
        return this.show({
            type: isOnline ? 'success' : 'warning',
            title: isOnline ? 'تم استعادة الاتصال' : 'انقطاع الاتصال',
            message: isOnline ? 'تم استعادة الاتصال بالإنترنت' : 'تم فقدان الاتصال بالإنترنت',
            duration: isOnline ? 3000 : 0,
            persistent: !isOnline
        });
    }

    // إحصائيات الإشعارات
    getNotificationStats() {
        return {
            total: this.notifications.length,
            byType: this.notifications.reduce((acc, notification) => {
                const type = notification.className.split(' ')[1];
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            }, {}),
            persistent: this.notifications.filter(n => n.classList.contains('persistent')).length
        };
    }
}

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedNotificationSystem;
}
