// نظام تسجيل دخول المدير
class AdminLoginSystem {
    constructor() {
        this.init();
        this.checkExistingSession();
    }

    init() {
        this.bindEvents();
        this.setupPasswordToggle();
    }

    bindEvents() {
        // Form submission
        document.getElementById('adminLoginForm').addEventListener('submit', (e) => this.handleLogin(e));
        
        // Password toggle
        document.getElementById('togglePassword').addEventListener('click', () => this.togglePasswordVisibility());
        
        // Enter key handling
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                const form = document.getElementById('adminLoginForm');
                if (document.activeElement && form.contains(document.activeElement)) {
                    e.preventDefault();
                    this.handleLogin(e);
                }
            }
        });
    }

    setupPasswordToggle() {
        const passwordInput = document.getElementById('password');
        const toggleBtn = document.getElementById('togglePassword');
        
        toggleBtn.addEventListener('click', () => {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = toggleBtn.querySelector('i');
            icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
        });
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const credentials = {
            username: formData.get('username').trim(),
            password: formData.get('password'),
            rememberMe: formData.get('rememberMe') === 'on'
        };

        // Validate input
        if (!credentials.username || !credentials.password) {
            this.showNotification('يرجى إدخال اسم المستخدم وكلمة المرور', 'warning');
            return;
        }

        this.showLoading();

        try {
            // Simulate authentication delay
            await this.delay(1500);
            
            const isValid = this.validateCredentials(credentials);
            
            if (isValid) {
                // Create session
                this.createSession(credentials);
                
                this.showNotification('تم تسجيل الدخول بنجاح', 'success');
                
                // Redirect to dashboard
                setTimeout(() => {
                    this.redirectToDashboard();
                }, 1500);
                
            } else {
                this.showNotification('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
            
        } catch (error) {
            this.showNotification('خطأ في تسجيل الدخول', 'error');
        } finally {
            this.hideLoading();
        }
    }

    validateCredentials(credentials) {
        // Default admin credentials (في التطبيق الحقيقي، يجب استخدام نظام مصادقة آمن)
        const validCredentials = [
            { username: 'admin', password: 'admin123' },
            { username: 'مدير', password: 'مدير123' },
            { username: 'administrator', password: 'admin@2024' }
        ];

        return validCredentials.some(valid => 
            valid.username === credentials.username && 
            valid.password === credentials.password
        );
    }

    createSession(credentials) {
        const sessionData = {
            username: credentials.username,
            loginTime: new Date().toISOString(),
            rememberMe: credentials.rememberMe,
            sessionId: this.generateSessionId()
        };

        // Save session
        if (credentials.rememberMe) {
            localStorage.setItem('adminSession', JSON.stringify(sessionData));
        } else {
            sessionStorage.setItem('adminSession', JSON.stringify(sessionData));
        }

        // Add to login history
        this.addToLoginHistory(sessionData);
    }

    checkExistingSession() {
        const session = this.getSession();
        
        if (session) {
            // Check if session is still valid (24 hours for remember me, 2 hours for regular)
            const loginTime = new Date(session.loginTime);
            const now = new Date();
            const maxAge = session.rememberMe ? 24 * 60 * 60 * 1000 : 2 * 60 * 60 * 1000; // 24h or 2h
            
            if (now - loginTime < maxAge) {
                // Session is still valid, redirect to dashboard
                this.showNotification('جلسة نشطة موجودة، جاري التوجيه...', 'info');
                setTimeout(() => {
                    this.redirectToDashboard();
                }, 1000);
                return;
            } else {
                // Session expired, clear it
                this.clearSession();
            }
        }
    }

    getSession() {
        try {
            let session = localStorage.getItem('adminSession');
            if (!session) {
                session = sessionStorage.getItem('adminSession');
            }
            return session ? JSON.parse(session) : null;
        } catch {
            return null;
        }
    }

    clearSession() {
        localStorage.removeItem('adminSession');
        sessionStorage.removeItem('adminSession');
    }

    addToLoginHistory(sessionData) {
        try {
            const history = JSON.parse(localStorage.getItem('adminLoginHistory') || '[]');
            history.unshift({
                username: sessionData.username,
                loginTime: sessionData.loginTime,
                sessionId: sessionData.sessionId
            });
            
            // Keep only last 10 logins
            localStorage.setItem('adminLoginHistory', JSON.stringify(history.slice(0, 10)));
        } catch (error) {
            console.error('Error saving login history:', error);
        }
    }

    redirectToDashboard() {
        // Check if running in Electron
        if (typeof window !== 'undefined' && window.electronAPI) {
            window.electronAPI.openAdminDashboard();
        } else {
            // In browser environment
            window.location.href = 'admin-dashboard.html';
        }
    }

    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleBtn = document.getElementById('togglePassword');
        const icon = toggleBtn.querySelector('i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        // Set icon based on type
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        icon.className = `notification-icon ${icons[type]}`;
        messageEl.textContent = message;
        notification.className = `notification ${type}`;
        notification.style.display = 'block';

        // Auto hide after 5 seconds
        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize admin login system
document.addEventListener('DOMContentLoaded', () => {
    new AdminLoginSystem();
});
