<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جميع النماذج</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-form {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            background: #f9f9f9;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log-area {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار جميع النماذج</h1>
        
        <div class="test-section">
            <h2>🔧 أدوات الاختبار</h2>
            <button class="btn" onclick="testAllFormsFunction()">اختبار جميع النماذج</button>
            <button class="btn" onclick="fixAllSaveButtonsFunction()">إصلاح جميع أزرار الحفظ</button>
            <button class="btn" onclick="clearLog()">مسح السجل</button>
        </div>

        <div class="test-section">
            <h2>📝 نموذج اختبار الزبون</h2>
            <form id="customer-form" class="test-form">
                <input type="hidden" id="customer-id" value="">
                
                <label for="customer-name">اسم الزبون *:</label>
                <input type="text" id="customer-name" name="customer-name" required>
                
                <label for="customer-phone">رقم الهاتف *:</label>
                <input type="tel" id="customer-phone" name="customer-phone" required>
                
                <label for="customer-address">العنوان *:</label>
                <textarea id="customer-address" name="customer-address" required></textarea>
                
                <label for="customer-notes">ملاحظات:</label>
                <textarea id="customer-notes" name="customer-notes"></textarea>
                
                <button type="submit" class="btn">حفظ الزبون</button>
            </form>
        </div>

        <div class="test-section">
            <h2>📊 سجل الاختبار</h2>
            <div id="log-area" class="log-area"></div>
        </div>
    </div>

    <script>
        // إعادة توجيه console.log إلى منطقة السجل
        const logArea = document.getElementById('log-area');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : '📝';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        function clearLog() {
            logArea.textContent = '';
        }
        
        function testAllFormsFunction() {
            console.log('🧪 بدء اختبار جميع النماذج...');
            
            const forms = ['customer-form'];
            
            forms.forEach(formId => {
                const form = document.getElementById(formId);
                if (form) {
                    console.log(`✅ ${formId}: موجود`);
                    
                    const saveButton = form.querySelector('button[type="submit"]');
                    if (saveButton) {
                        console.log(`  🔘 زر الحفظ: موجود - "${saveButton.textContent.trim()}"`);
                        
                        // اختبار النقر على الزر
                        saveButton.addEventListener('click', function(e) {
                            console.log(`🖱️ تم النقر على زر الحفظ في ${formId}`);
                        });
                        
                    } else {
                        console.log(`  ❌ زر الحفظ: غير موجود`);
                    }
                } else {
                    console.log(`❌ ${formId}: غير موجود`);
                }
            });
        }
        
        function fixAllSaveButtonsFunction() {
            console.log('🔧 بدء إصلاح أزرار الحفظ...');
            
            const form = document.getElementById('customer-form');
            if (form) {
                const saveButton = form.querySelector('button[type="submit"]');
                if (saveButton) {
                    console.log('✅ تم العثور على زر الحفظ، إضافة معالج...');
                    
                    // إضافة معالج النقر
                    saveButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🖱️ تم النقر على زر الحفظ!');
                        
                        // جمع البيانات
                        const name = document.getElementById('customer-name').value;
                        const phone = document.getElementById('customer-phone').value;
                        const address = document.getElementById('customer-address').value;
                        
                        if (!name || !phone || !address) {
                            console.error('❌ يرجى ملء جميع الحقول المطلوبة');
                            return;
                        }
                        
                        console.log('📊 البيانات المدخلة:');
                        console.log(`  الاسم: ${name}`);
                        console.log(`  الهاتف: ${phone}`);
                        console.log(`  العنوان: ${address}`);
                        
                        console.log('✅ تم حفظ البيانات بنجاح!');
                        
                        // مسح النموذج
                        form.reset();
                    });
                    
                    // إضافة معالج submit
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        console.log('📝 تم تقديم النموذج عبر submit event');
                        saveButton.click();
                    });
                    
                    console.log('✅ تم إصلاح زر الحفظ بنجاح');
                } else {
                    console.error('❌ لم يتم العثور على زر الحفظ');
                }
            } else {
                console.error('❌ لم يتم العثور على النموذج');
            }
        }
        
        // تشغيل الإصلاح عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل صفحة الاختبار');
            setTimeout(fixAllSaveButtonsFunction, 100);
        });
    </script>
</body>
</html>
