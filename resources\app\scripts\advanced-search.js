/**
 * نظام البحث والفلترة المتقدم
 * Advanced Search and Filter System
 * 
 * يوفر إمكانيات بحث وفلترة متقدمة عبر جميع البيانات في النظام
 * مع دعم البحث النصي، الفلترة حسب التاريخ، النوع، والحالة
 */

class AdvancedSearchSystem {
    constructor() {
        this.searchHistory = [];
        this.savedFilters = [];
        this.searchResults = {};
        this.currentFilters = {};
        this.searchableFields = {
            customers: ['name', 'phone', 'address', 'nationalId', 'email'],
            vehicles: ['plateNumber', 'type', 'model', 'year', 'chassisNumber'],
            transmissionTable: ['operationType', 'customerName', 'vehiclePlate', 'notes', 'status'],
            gasCards: ['cardNumber', 'customerName', 'vehiclePlate', 'status', 'issueDate'],
            suppliers: ['name', 'phone', 'address', 'email', 'category'],
            inventory: ['itemName', 'category', 'supplier', 'location', 'barcode'],
            sales: ['customerName', 'itemName', 'paymentMethod', 'notes'],
            purchases: ['supplierName', 'itemName', 'paymentMethod', 'notes'],
            debts: ['customerName', 'supplierName', 'type', 'status', 'notes']
        };
        
        this.filterTypes = {
            text: 'نص',
            date: 'تاريخ',
            number: 'رقم',
            select: 'اختيار',
            range: 'نطاق',
            boolean: 'منطقي'
        };
        
        this.init();
    }

    init() {
        this.loadSearchHistory();
        this.loadSavedFilters();
        console.log('✅ تم تهيئة نظام البحث والفلترة المتقدم');
    }

    // البحث العام عبر جميع البيانات
    globalSearch(query, options = {}) {
        const {
            tables = Object.keys(this.searchableFields),
            caseSensitive = false,
            exactMatch = false,
            includeArchived = false
        } = options;

        const results = {};
        const searchQuery = caseSensitive ? query : query.toLowerCase();

        tables.forEach(table => {
            if (!appData[table]) return;

            const tableResults = this.searchInTable(
                appData[table], 
                this.searchableFields[table], 
                searchQuery, 
                { caseSensitive, exactMatch, includeArchived }
            );

            if (tableResults.length > 0) {
                results[table] = tableResults;
            }
        });

        // حفظ في تاريخ البحث
        this.addToSearchHistory(query, results);

        return results;
    }

    // البحث في جدول محدد
    searchInTable(data, fields, query, options = {}) {
        const { caseSensitive = false, exactMatch = false, includeArchived = false } = options;
        
        return data.filter(item => {
            // تجاهل العناصر المؤرشفة إذا لم يتم طلب تضمينها
            if (!includeArchived && item.archived) return false;

            return fields.some(field => {
                const fieldValue = this.getNestedValue(item, field);
                if (!fieldValue) return false;

                const searchValue = caseSensitive ? fieldValue.toString() : fieldValue.toString().toLowerCase();
                const searchQuery = caseSensitive ? query : query.toLowerCase();

                if (exactMatch) {
                    return searchValue === searchQuery;
                } else {
                    return searchValue.includes(searchQuery);
                }
            });
        });
    }

    // البحث المتقدم مع فلاتر متعددة
    advancedSearch(filters, options = {}) {
        const {
            table = 'all',
            sortBy = 'createdAt',
            sortOrder = 'desc',
            limit = null,
            offset = 0
        } = options;

        let results = {};

        if (table === 'all') {
            // البحث في جميع الجداول
            Object.keys(this.searchableFields).forEach(tableName => {
                if (appData[tableName]) {
                    results[tableName] = this.applyFilters(appData[tableName], filters);
                }
            });
        } else {
            // البحث في جدول محدد
            if (appData[table]) {
                results[table] = this.applyFilters(appData[table], filters);
            }
        }

        // تطبيق الترتيب والتقسيم
        Object.keys(results).forEach(tableName => {
            results[tableName] = this.sortAndPaginate(
                results[tableName], 
                sortBy, 
                sortOrder, 
                limit, 
                offset
            );
        });

        return results;
    }

    // تطبيق الفلاتر على البيانات
    applyFilters(data, filters) {
        return data.filter(item => {
            return Object.entries(filters).every(([field, filterConfig]) => {
                const fieldValue = this.getNestedValue(item, field);
                return this.applyFilter(fieldValue, filterConfig);
            });
        });
    }

    // تطبيق فلتر واحد
    applyFilter(value, filterConfig) {
        const { type, operator, value: filterValue, caseSensitive = false } = filterConfig;

        if (value === null || value === undefined) {
            return operator === 'empty' || operator === 'null';
        }

        switch (type) {
            case 'text':
                return this.applyTextFilter(value, operator, filterValue, caseSensitive);
            case 'date':
                return this.applyDateFilter(value, operator, filterValue);
            case 'number':
                return this.applyNumberFilter(value, operator, filterValue);
            case 'select':
                return this.applySelectFilter(value, operator, filterValue);
            case 'range':
                return this.applyRangeFilter(value, operator, filterValue);
            case 'boolean':
                return this.applyBooleanFilter(value, operator, filterValue);
            default:
                return true;
        }
    }

    // فلتر النصوص
    applyTextFilter(value, operator, filterValue, caseSensitive) {
        const textValue = caseSensitive ? value.toString() : value.toString().toLowerCase();
        const searchValue = caseSensitive ? filterValue : filterValue.toLowerCase();

        switch (operator) {
            case 'contains':
                return textValue.includes(searchValue);
            case 'equals':
                return textValue === searchValue;
            case 'startsWith':
                return textValue.startsWith(searchValue);
            case 'endsWith':
                return textValue.endsWith(searchValue);
            case 'notContains':
                return !textValue.includes(searchValue);
            case 'notEquals':
                return textValue !== searchValue;
            case 'empty':
                return textValue.trim() === '';
            case 'notEmpty':
                return textValue.trim() !== '';
            case 'regex':
                try {
                    const regex = new RegExp(filterValue, caseSensitive ? 'g' : 'gi');
                    return regex.test(textValue);
                } catch (e) {
                    return false;
                }
            default:
                return true;
        }
    }

    // فلتر التواريخ
    applyDateFilter(value, operator, filterValue) {
        const date = new Date(value);
        const filterDate = new Date(filterValue);

        if (isNaN(date.getTime()) || isNaN(filterDate.getTime())) {
            return false;
        }

        switch (operator) {
            case 'equals':
                return date.toDateString() === filterDate.toDateString();
            case 'before':
                return date < filterDate;
            case 'after':
                return date > filterDate;
            case 'onOrBefore':
                return date <= filterDate;
            case 'onOrAfter':
                return date >= filterDate;
            case 'between':
                const endDate = new Date(filterValue.end);
                return date >= filterDate && date <= endDate;
            case 'today':
                return date.toDateString() === new Date().toDateString();
            case 'yesterday':
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                return date.toDateString() === yesterday.toDateString();
            case 'thisWeek':
                return this.isInCurrentWeek(date);
            case 'thisMonth':
                return this.isInCurrentMonth(date);
            case 'thisYear':
                return this.isInCurrentYear(date);
            default:
                return true;
        }
    }

    // فلتر الأرقام
    applyNumberFilter(value, operator, filterValue) {
        const numValue = parseFloat(value);
        const filterNum = parseFloat(filterValue);

        if (isNaN(numValue) || isNaN(filterNum)) {
            return false;
        }

        switch (operator) {
            case 'equals':
                return numValue === filterNum;
            case 'notEquals':
                return numValue !== filterNum;
            case 'greaterThan':
                return numValue > filterNum;
            case 'lessThan':
                return numValue < filterNum;
            case 'greaterThanOrEqual':
                return numValue >= filterNum;
            case 'lessThanOrEqual':
                return numValue <= filterNum;
            case 'between':
                return numValue >= filterValue.min && numValue <= filterValue.max;
            default:
                return true;
        }
    }

    // فلتر الاختيار
    applySelectFilter(value, operator, filterValue) {
        switch (operator) {
            case 'equals':
                return value === filterValue;
            case 'notEquals':
                return value !== filterValue;
            case 'in':
                return Array.isArray(filterValue) && filterValue.includes(value);
            case 'notIn':
                return Array.isArray(filterValue) && !filterValue.includes(value);
            default:
                return true;
        }
    }

    // فلتر النطاق
    applyRangeFilter(value, operator, filterValue) {
        const numValue = parseFloat(value);
        
        switch (operator) {
            case 'between':
                return numValue >= filterValue.min && numValue <= filterValue.max;
            case 'outside':
                return numValue < filterValue.min || numValue > filterValue.max;
            default:
                return true;
        }
    }

    // فلتر منطقي
    applyBooleanFilter(value, operator, filterValue) {
        const boolValue = Boolean(value);
        
        switch (operator) {
            case 'equals':
                return boolValue === Boolean(filterValue);
            case 'true':
                return boolValue === true;
            case 'false':
                return boolValue === false;
            default:
                return true;
        }
    }

    // ترتيب وتقسيم النتائج
    sortAndPaginate(data, sortBy, sortOrder, limit, offset) {
        // الترتيب
        const sortedData = [...data].sort((a, b) => {
            const aValue = this.getNestedValue(a, sortBy);
            const bValue = this.getNestedValue(b, sortBy);

            if (aValue === bValue) return 0;
            
            const comparison = aValue > bValue ? 1 : -1;
            return sortOrder === 'desc' ? -comparison : comparison;
        });

        // التقسيم
        if (limit) {
            return sortedData.slice(offset, offset + limit);
        }

        return sortedData.slice(offset);
    }

    // الحصول على قيمة متداخلة
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    }

    // فلاتر التاريخ المساعدة
    isInCurrentWeek(date) {
        const now = new Date();
        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
        const endOfWeek = new Date(now.setDate(now.getDate() - now.getDay() + 6));
        return date >= startOfWeek && date <= endOfWeek;
    }

    isInCurrentMonth(date) {
        const now = new Date();
        return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
    }

    isInCurrentYear(date) {
        return date.getFullYear() === new Date().getFullYear();
    }

    // إدارة تاريخ البحث
    addToSearchHistory(query, results) {
        const historyItem = {
            id: Date.now().toString(),
            query,
            timestamp: new Date().toISOString(),
            resultCount: Object.values(results).reduce((total, tableResults) => total + tableResults.length, 0),
            tables: Object.keys(results)
        };

        this.searchHistory.unshift(historyItem);
        
        // الاحتفاظ بآخر 50 بحث فقط
        if (this.searchHistory.length > 50) {
            this.searchHistory = this.searchHistory.slice(0, 50);
        }

        this.saveSearchHistory();
    }

    // حفظ تاريخ البحث
    saveSearchHistory() {
        try {
            localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('خطأ في حفظ تاريخ البحث:', error);
        }
    }

    // تحميل تاريخ البحث
    loadSearchHistory() {
        try {
            const saved = localStorage.getItem('searchHistory');
            if (saved) {
                this.searchHistory = JSON.parse(saved);
            }
        } catch (error) {
            console.error('خطأ في تحميل تاريخ البحث:', error);
            this.searchHistory = [];
        }
    }

    // حفظ فلتر
    saveFilter(name, filters, options = {}) {
        const filterItem = {
            id: Date.now().toString(),
            name,
            filters,
            options,
            createdAt: new Date().toISOString(),
            lastUsed: new Date().toISOString()
        };

        // التحقق من عدم وجود فلتر بنفس الاسم
        const existingIndex = this.savedFilters.findIndex(f => f.name === name);
        if (existingIndex >= 0) {
            this.savedFilters[existingIndex] = filterItem;
        } else {
            this.savedFilters.push(filterItem);
        }

        this.saveSavedFilters();
        return filterItem.id;
    }

    // تحميل فلتر محفوظ
    loadFilter(filterId) {
        const filter = this.savedFilters.find(f => f.id === filterId);
        if (filter) {
            filter.lastUsed = new Date().toISOString();
            this.saveSavedFilters();
            return filter;
        }
        return null;
    }

    // حذف فلتر محفوظ
    deleteFilter(filterId) {
        this.savedFilters = this.savedFilters.filter(f => f.id !== filterId);
        this.saveSavedFilters();
    }

    // حفظ الفلاتر المحفوظة
    saveSavedFilters() {
        try {
            localStorage.setItem('savedFilters', JSON.stringify(this.savedFilters));
        } catch (error) {
            console.error('خطأ في حفظ الفلاتر:', error);
        }
    }

    // تحميل الفلاتر المحفوظة
    loadSavedFilters() {
        try {
            const saved = localStorage.getItem('savedFilters');
            if (saved) {
                this.savedFilters = JSON.parse(saved);
            }
        } catch (error) {
            console.error('خطأ في تحميل الفلاتر:', error);
            this.savedFilters = [];
        }
    }

    // تصدير نتائج البحث
    exportSearchResults(results, format = 'json') {
        switch (format) {
            case 'csv':
                return this.exportToCSV(results);
            case 'excel':
                return this.exportToExcel(results);
            case 'pdf':
                return this.exportToPDF(results);
            default:
                return JSON.stringify(results, null, 2);
        }
    }

    // تصدير إلى CSV
    exportToCSV(results) {
        let csv = '';
        
        Object.entries(results).forEach(([tableName, data]) => {
            if (data.length === 0) return;
            
            csv += `\n\n=== ${tableName} ===\n`;
            
            // رؤوس الأعمدة
            const headers = Object.keys(data[0]);
            csv += headers.join(',') + '\n';
            
            // البيانات
            data.forEach(row => {
                const values = headers.map(header => {
                    const value = row[header];
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                });
                csv += values.join(',') + '\n';
            });
        });
        
        return csv;
    }

    // تصدير إلى Excel (مبسط)
    exportToExcel(results) {
        // هذا مثال مبسط - في التطبيق الحقيقي نحتاج مكتبة مثل SheetJS
        return this.exportToCSV(results);
    }

    // تصدير إلى PDF (مبسط)
    exportToPDF(results) {
        // هذا مثال مبسط - في التطبيق الحقيقي نحتاج مكتبة مثل jsPDF
        let content = 'نتائج البحث\n\n';
        
        Object.entries(results).forEach(([tableName, data]) => {
            content += `${tableName}: ${data.length} نتيجة\n`;
        });
        
        return content;
    }

    // إحصائيات البحث
    getSearchStatistics() {
        const totalSearches = this.searchHistory.length;
        const recentSearches = this.searchHistory.slice(0, 10);
        const popularQueries = this.getMostPopularQueries();
        const searchTrends = this.getSearchTrends();

        return {
            totalSearches,
            recentSearches,
            popularQueries,
            searchTrends,
            savedFiltersCount: this.savedFilters.length
        };
    }

    // الاستعلامات الأكثر شيوعاً
    getMostPopularQueries() {
        const queryCount = {};
        
        this.searchHistory.forEach(item => {
            const query = item.query.toLowerCase();
            queryCount[query] = (queryCount[query] || 0) + 1;
        });

        return Object.entries(queryCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([query, count]) => ({ query, count }));
    }

    // اتجاهات البحث
    getSearchTrends() {
        const trends = {};
        const now = new Date();
        
        this.searchHistory.forEach(item => {
            const date = new Date(item.timestamp);
            const daysDiff = Math.floor((now - date) / (1000 * 60 * 60 * 24));
            
            if (daysDiff <= 30) {
                const dateKey = date.toISOString().split('T')[0];
                trends[dateKey] = (trends[dateKey] || 0) + 1;
            }
        });

        return Object.entries(trends)
            .sort(([a], [b]) => new Date(a) - new Date(b))
            .map(([date, count]) => ({ date, count }));
    }

    // مسح تاريخ البحث
    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
    }

    // مسح الفلاتر المحفوظة
    clearSavedFilters() {
        this.savedFilters = [];
        this.saveSavedFilters();
    }
}

// إنشاء مثيل عام للنظام
const advancedSearch = new AdvancedSearchSystem();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedSearchSystem;
}
