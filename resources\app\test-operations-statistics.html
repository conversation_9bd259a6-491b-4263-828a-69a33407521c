<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إحصائيات العمليات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #2196F3;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .stats-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار نظام إحصائيات العمليات</h1>
        <p>هذه الصفحة تختبر جميع وظائف نظام إحصائيات العمليات</p>

        <!-- اختبار تهيئة النظام -->
        <div class="test-section">
            <div class="test-title">1. اختبار تهيئة النظام</div>
            <button onclick="testSystemInitialization()">تشغيل الاختبار</button>
            <div id="init-result" class="test-result"></div>
        </div>

        <!-- اختبار البيانات الأساسية -->
        <div class="test-section">
            <div class="test-title">2. اختبار حساب الإحصائيات الأساسية</div>
            <button onclick="testBasicStats()">تشغيل الاختبار</button>
            <div id="basic-stats-result" class="test-result"></div>
        </div>

        <!-- اختبار توزيع العمليات -->
        <div class="test-section">
            <div class="test-title">3. اختبار توزيع العمليات حسب النوع</div>
            <button onclick="testOperationsByType()">تشغيل الاختبار</button>
            <div id="operations-type-result" class="test-result"></div>
        </div>

        <!-- اختبار الإحصائيات الزمنية -->
        <div class="test-section">
            <div class="test-title">4. اختبار الإحصائيات الزمنية</div>
            <button onclick="testTimeBasedStats()">تشغيل الاختبار</button>
            <div id="time-stats-result" class="test-result"></div>
        </div>

        <!-- اختبار إحصائيات الأداء -->
        <div class="test-section">
            <div class="test-title">5. اختبار إحصائيات الأداء</div>
            <button onclick="testPerformanceStats()">تشغيل الاختبار</button>
            <div id="performance-result" class="test-result"></div>
        </div>

        <!-- اختبار إحصائيات الزبائن -->
        <div class="test-section">
            <div class="test-title">6. اختبار إحصائيات الزبائن</div>
            <button onclick="testCustomerStats()">تشغيل الاختبار</button>
            <div id="customer-stats-result" class="test-result"></div>
        </div>

        <!-- اختبار إحصائيات السيارات -->
        <div class="test-section">
            <div class="test-title">7. اختبار إحصائيات السيارات</div>
            <button onclick="testVehicleStats()">تشغيل الاختبار</button>
            <div id="vehicle-stats-result" class="test-result"></div>
        </div>

        <!-- اختبار تصدير البيانات -->
        <div class="test-section">
            <div class="test-title">8. اختبار تصدير البيانات</div>
            <button onclick="testDataExport()">تشغيل الاختبار</button>
            <div id="export-result" class="test-result"></div>
        </div>

        <!-- اختبار شامل -->
        <div class="test-section">
            <div class="test-title">🚀 اختبار شامل لجميع الوظائف</div>
            <button onclick="runAllTests()" style="background: #4CAF50;">تشغيل جميع الاختبارات</button>
            <div id="all-tests-result" class="test-result"></div>
        </div>
    </div>

    <!-- تحميل الملفات المطلوبة -->
    <script src="scripts/operations-statistics.js"></script>

    <script>
        // بيانات اختبار شاملة
        const testData = {
            transmissionTable: [
                {
                    id: 'trans-1',
                    customerId: 'cust-1',
                    vehicleId: 'veh-1',
                    operationType: 'تركيب',
                    operationDate: '2024-01-15',
                    cost: 5000,
                    status: 'completed',
                    createdAt: '2024-01-15T10:00:00Z'
                },
                {
                    id: 'trans-2',
                    customerId: 'cust-2',
                    vehicleId: 'veh-2',
                    operationType: 'مراقبة',
                    operationDate: '2024-01-20',
                    cost: 3000,
                    status: 'completed',
                    createdAt: '2024-01-20T14:00:00Z'
                },
                {
                    id: 'trans-3',
                    customerId: 'cust-1',
                    vehicleId: 'veh-3',
                    operationType: 'تركيب',
                    operationDate: '2024-02-01',
                    cost: 4500,
                    status: 'pending',
                    createdAt: '2024-02-01T09:00:00Z'
                }
            ],
            gasCards: [
                {
                    id: 'card-1',
                    customerId: 'cust-1',
                    vehicleId: 'veh-1',
                    cardNumber: 'GC-12345',
                    issueDate: '2024-01-10',
                    renewalCost: 2000,
                    status: 'active',
                    createdAt: '2024-01-10T09:00:00Z'
                },
                {
                    id: 'card-2',
                    customerId: 'cust-3',
                    vehicleId: 'veh-4',
                    cardNumber: 'GC-67890',
                    issueDate: '2024-01-25',
                    renewalCost: 2500,
                    status: 'active',
                    createdAt: '2024-01-25T11:00:00Z'
                }
            ],
            customers: [
                {
                    id: 'cust-1',
                    name: 'أحمد محمد علي',
                    phone: '0555123456',
                    address: 'الجزائر العاصمة, الجزائر',
                    createdAt: '2024-01-01T08:00:00Z'
                },
                {
                    id: 'cust-2',
                    name: 'فاطمة عبد الرحمن',
                    phone: '0666789012',
                    address: 'وهران, الجزائر',
                    createdAt: '2024-01-05T10:00:00Z'
                },
                {
                    id: 'cust-3',
                    name: 'محمد الطاهر',
                    phone: '0777456789',
                    address: 'قسنطينة, الجزائر',
                    createdAt: '2024-01-20T14:00:00Z'
                }
            ],
            vehicles: [
                {
                    id: 'veh-1',
                    plateNumber: '123-456-78',
                    type: 'سيارة خاصة',
                    year: 2020,
                    customerId: 'cust-1',
                    createdAt: '2024-01-01T08:30:00Z'
                },
                {
                    id: 'veh-2',
                    plateNumber: '987-654-32',
                    type: 'شاحنة',
                    year: 2018,
                    customerId: 'cust-2',
                    createdAt: '2024-01-05T10:30:00Z'
                },
                {
                    id: 'veh-3',
                    plateNumber: '555-777-99',
                    type: 'حافلة',
                    year: 2019,
                    customerId: 'cust-1',
                    createdAt: '2024-01-15T12:00:00Z'
                },
                {
                    id: 'veh-4',
                    plateNumber: '111-222-33',
                    type: 'سيارة خاصة',
                    year: 2021,
                    customerId: 'cust-3',
                    createdAt: '2024-01-20T15:00:00Z'
                }
            ]
        };

        // تعيين البيانات للاختبار
        window.appData = testData;

        // دوال الاختبار
        function testSystemInitialization() {
            const resultDiv = document.getElementById('init-result');
            try {
                // التحقق من وجود الكلاس
                if (typeof OperationsStatistics === 'undefined') {
                    throw new Error('كلاس OperationsStatistics غير موجود');
                }

                // إنشاء مثيل جديد
                const stats = new OperationsStatistics();

                // التحقق من الخصائص الأساسية
                if (!stats.operationTypes || !stats.timeRanges) {
                    throw new Error('الخصائص الأساسية غير مهيئة بشكل صحيح');
                }

                resultDiv.className = 'test-result success';
                resultDiv.textContent = '✅ تم تهيئة النظام بنجاح\n' +
                    `- أنواع العمليات: ${Object.keys(stats.operationTypes).length}\n` +
                    `- النطاقات الزمنية: ${Object.keys(stats.timeRanges).length}`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ فشل في تهيئة النظام: ' + error.message;
            }
        }

        function testBasicStats() {
            const resultDiv = document.getElementById('basic-stats-result');
            try {
                const stats = operationsStats.calculateBasicStats('all');

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `✅ تم حساب الإحصائيات الأساسية بنجاح:
<div class="stats-display">
- إجمالي العمليات: ${stats.totalOperations}
- إجمالي الزبائن: ${stats.totalCustomers}
- إجمالي السيارات: ${stats.totalVehicles}
- إجمالي الإيرادات: ${stats.totalRevenue} د.ج
- متوسط العمليات اليومية: ${stats.averageOperationsPerDay.toFixed(2)}
- معدل الإنجاز: ${stats.completionRate}%
</div>`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ فشل في حساب الإحصائيات الأساسية: ' + error.message;
            }
        }

        function testOperationsByType() {
            const resultDiv = document.getElementById('operations-type-result');
            try {
                const stats = operationsStats.calculateOperationsByType('all');

                let output = '✅ تم حساب توزيع العمليات بنجاح:\n<div class="stats-display">';

                Object.entries(stats).forEach(([type, data]) => {
                    output += `\n${type}:
  - العدد: ${data.count}
  - النسبة: ${data.percentage}%
  - الإيرادات: ${data.revenue} د.ج
  - الزبائن: ${data.customers}
  - السيارات: ${data.vehicles}`;
                });

                output += '\n</div>';

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = output;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ فشل في حساب توزيع العمليات: ' + error.message;
            }
        }

        function testTimeBasedStats() {
            const resultDiv = document.getElementById('time-stats-result');
            try {
                const stats = operationsStats.calculateTimeBasedStats('month');

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `✅ تم حساب الإحصائيات الزمنية بنجاح:
<div class="stats-display">
- عدد التسميات الزمنية: ${stats.labels.length}
- بيانات العمليات: ${stats.datasets.operations.length} نقطة
- بيانات الإيرادات: ${stats.datasets.revenue.length} نقطة
- بيانات الزبائن: ${stats.datasets.customers.length} نقطة
- إجمالي العمليات: ${stats.datasets.operations.reduce((a, b) => a + b, 0)}
- إجمالي الإيرادات: ${stats.datasets.revenue.reduce((a, b) => a + b, 0)} د.ج
</div>`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ فشل في حساب الإحصائيات الزمنية: ' + error.message;
            }
        }

        function testPerformanceStats() {
            const resultDiv = document.getElementById('performance-result');
            try {
                const stats = operationsStats.calculatePerformanceStats('month');

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `✅ تم حساب إحصائيات الأداء بنجاح:
<div class="stats-display">
- نمو العمليات: ${stats.operationsGrowth.toFixed(2)}%
- نمو الإيرادات: ${stats.revenueGrowth.toFixed(2)}%
- نمو الزبائن: ${stats.customerGrowth.toFixed(2)}%
- متوسط قيمة العملية: ${stats.averageOperationValue.toFixed(2)} د.ج
- معدل الاحتفاظ بالزبائن: ${stats.customerRetentionRate.toFixed(2)}%
- الكفاءة التشغيلية: ${stats.operationalEfficiency.toFixed(2)}%
</div>`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ فشل في حساب إحصائيات الأداء: ' + error.message;
            }
        }

        function testCustomerStats() {
            const resultDiv = document.getElementById('customer-stats-result');
            try {
                const stats = operationsStats.calculateCustomerStats('all');

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `✅ تم حساب إحصائيات الزبائن بنجاح:
<div class="stats-display">
- إجمالي الزبائن: ${stats.totalCustomers}
- الزبائن الجدد: ${stats.newCustomers}
- الزبائن النشطون: ${stats.activeCustomers}
- أفضل الزبائن: ${stats.topCustomers.length}
- التوزيع الجغرافي: ${Object.keys(stats.customersByRegion).length} منطقة
- متوسط العمليات لكل زبون: ${stats.averageOperationsPerCustomer.toFixed(2)}
</div>`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ فشل في حساب إحصائيات الزبائن: ' + error.message;
            }
        }

        function testVehicleStats() {
            const resultDiv = document.getElementById('vehicle-stats-result');
            try {
                const stats = operationsStats.calculateVehicleStats('all');

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `✅ تم حساب إحصائيات السيارات بنجاح:
<div class="stats-display">
- إجمالي السيارات: ${stats.totalVehicles}
- السيارات الجديدة: ${stats.newVehicles}
- أنواع السيارات: ${Object.keys(stats.vehiclesByType).length}
- توزيع السنوات: ${Object.keys(stats.vehiclesByYear).length}
- متوسط عمر السيارات: ${stats.averageVehicleAge.toFixed(1)} سنة
- أكثر السيارات نشاطاً: ${stats.mostActiveVehicles.length}
</div>`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ فشل في حساب إحصائيات السيارات: ' + error.message;
            }
        }

        function testDataExport() {
            const resultDiv = document.getElementById('export-result');
            try {
                const jsonData = operationsStats.exportStats('json', 'all');
                const csvData = operationsStats.exportStats('csv', 'all');
                const excelData = operationsStats.exportStats('excel', 'all');

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `✅ تم اختبار تصدير البيانات بنجاح:
<div class="stats-display">
- تصدير JSON: ${jsonData.length} حرف
- تصدير CSV: ${csvData.length} حرف
- تصدير Excel: ${excelData.length} حرف
- تم إنشاء البيانات في: ${JSON.parse(jsonData).generatedAt}
</div>`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ فشل في اختبار تصدير البيانات: ' + error.message;
            }
        }

        function runAllTests() {
            const resultDiv = document.getElementById('all-tests-result');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '🔄 جاري تشغيل جميع الاختبارات...';

            const tests = [
                { name: 'تهيئة النظام', func: testSystemInitialization },
                { name: 'الإحصائيات الأساسية', func: testBasicStats },
                { name: 'توزيع العمليات', func: testOperationsByType },
                { name: 'الإحصائيات الزمنية', func: testTimeBasedStats },
                { name: 'إحصائيات الأداء', func: testPerformanceStats },
                { name: 'إحصائيات الزبائن', func: testCustomerStats },
                { name: 'إحصائيات السيارات', func: testVehicleStats },
                { name: 'تصدير البيانات', func: testDataExport }
            ];

            let passedTests = 0;
            let totalTests = tests.length;

            tests.forEach((test, index) => {
                setTimeout(() => {
                    try {
                        test.func();
                        passedTests++;
                    } catch (error) {
                        console.error(`فشل اختبار ${test.name}:`, error);
                    }

                    if (index === totalTests - 1) {
                        // جميع الاختبارات انتهت
                        setTimeout(() => {
                            if (passedTests === totalTests) {
                                resultDiv.className = 'test-result success';
                                resultDiv.textContent = `🎉 نجحت جميع الاختبارات! (${passedTests}/${totalTests})`;
                            } else {
                                resultDiv.className = 'test-result error';
                                resultDiv.textContent = `⚠️ نجح ${passedTests} من أصل ${totalTests} اختبارات`;
                            }
                        }, 500);
                    }
                }, index * 200);
            });
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 صفحة اختبار إحصائيات العمليات جاهزة');

            // اختبار سريع للتأكد من تحميل الملفات
            setTimeout(() => {
                if (typeof operationsStats !== 'undefined') {
                    console.log('✅ تم تحميل نظام الإحصائيات بنجاح');
                } else {
                    console.error('❌ فشل في تحميل نظام الإحصائيات');
                }
            }, 1000);
        });
    </script>
</body>
</html>