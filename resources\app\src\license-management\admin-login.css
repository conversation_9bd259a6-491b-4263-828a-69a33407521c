/* تصميم تسجيل دخول المدير */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --dark-bg: #343a40;
    --card-bg: #ffffff;
    --border-color: #dee2e6;
    --text-dark: #212529;
    --text-light: #6c757d;
    --shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    --shadow-lg: 0 1rem 3rem rgba(0,0,0,0.175);
    --border-radius: 0.375rem;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    color: var(--text-dark);
    overflow: hidden;
}

/* <PERSON>gin Container */
.login-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Background */
.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    z-index: 1;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* Login Content */
.login-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 400px;
    padding: 2rem;
}

/* Login Card */
.login-card {
    background: var(--card-bg);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    padding: 2.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    animation: slideUp 0.6s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Login Header */
.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.login-header p {
    color: var(--text-light);
    font-size: 0.875rem;
}

/* Form Styles */
.login-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.875rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 1rem;
    color: var(--text-light);
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 0.875rem;
    transition: var(--transition);
    background: var(--card-bg);
}

.input-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.toggle-password {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--secondary-color);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-dark);
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 1.125rem;
    height: 1.125rem;
    border: 2px solid var(--border-color);
    border-radius: 0.25rem;
    margin-left: 0.5rem;
    position: relative;
    transition: var(--transition);
}

.checkbox-container input:checked + .checkmark {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

.checkbox-container input:checked + .checkmark::after {
    content: '';
    position: absolute;
    top: 1px;
    right: 4px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 0.875rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-btn:active {
    transform: translateY(0);
}

/* Login Footer */
.login-footer {
    text-align: center;
}

.security-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: var(--warning-color);
    font-size: 0.75rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: rgba(243, 156, 18, 0.1);
    border-radius: var(--border-radius);
}

.back-link a {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.875rem;
    transition: var(--transition);
}

.back-link a:hover {
    color: var(--primary-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-spinner {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.loading-spinner p {
    color: var(--text-dark);
    font-weight: 500;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--card-bg);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 3001;
    animation: slideInLeft 0.3s ease;
    max-width: 300px;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification.success {
    border-right: 4px solid var(--success-color);
}

.notification.error {
    border-right: 4px solid var(--danger-color);
}

.notification.warning {
    border-right: 4px solid var(--warning-color);
}

.notification.info {
    border-right: 4px solid var(--info-color);
}

.notification-icon {
    font-size: 1.25rem;
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.error .notification-icon {
    color: var(--danger-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.info .notification-icon {
    color: var(--info-color);
}

.notification-message {
    font-size: 0.875rem;
    color: var(--text-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-content {
        padding: 1rem;
    }
    
    .login-card {
        padding: 2rem;
    }
    
    .logo h1 {
        font-size: 1.25rem;
    }
    
    .notification {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
}
