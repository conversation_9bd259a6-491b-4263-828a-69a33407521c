<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة التراخيص</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 450px;
            backdrop-filter: blur(10px);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-header h1 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #718096;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #4a5568;
            font-weight: 600;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .input-icon {
            position: relative;
        }

        .input-icon i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
        }

        .input-icon .form-input {
            padding-left: 3rem;
        }

        .login-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #4299e1 0%, #667eea 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(66, 153, 225, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .activation-section {
            border-top: 1px solid #e2e8f0;
            padding-top: 1.5rem;
            text-align: center;
        }

        .activation-section h3 {
            color: #4a5568;
            margin-bottom: 1rem;
        }

        .activation-btn {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .activation-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.3);
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        .alert.success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .alert.info {
            background: #ebf8ff;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }

        .loading {
            display: none;
            text-align: center;
            color: #4299e1;
        }

        .loading i {
            font-size: 1.5rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            color: #718096;
            font-size: 0.9rem;
        }

        /* Modal للتفعيل */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .modal-header h2 {
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #a0aec0;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Header -->
        <div class="login-header">
            <h1><i class="fas fa-shield-alt"></i> نظام إدارة التراخيص</h1>
            <p>مرحباً بك في نظام إدارة تراخيص الوقود</p>
        </div>

        <!-- Alert Messages -->
        <div id="alertMessage" class="alert"></div>

        <!-- Loading -->
        <div id="loading" class="loading">
            <i class="fas fa-spinner"></i>
            <p>جاري التحقق...</p>
        </div>

        <!-- Login Form -->
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <div class="input-icon">
                    <input type="text" id="username" class="form-input" placeholder="أدخل اسم المستخدم" required>
                    <i class="fas fa-user"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <div class="input-icon">
                    <input type="password" id="password" class="form-input" placeholder="أدخل كلمة المرور" required>
                    <i class="fas fa-lock"></i>
                </div>
            </div>

            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
            </button>
        </form>

        <!-- Activation Section -->
        <div class="activation-section">
            <h3>طلب تفعيل جديد؟</h3>
            <button class="activation-btn" onclick="showActivationModal()">
                <i class="fas fa-key"></i> طلب تفعيل
            </button>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 نظام إدارة الوقود - جميع الحقوق محفوظة</p>
        </div>
    </div>

    <!-- Activation Modal -->
    <div id="activationModal" class="modal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeActivationModal()">
                <i class="fas fa-times"></i>
            </button>
            
            <div class="modal-header">
                <h2><i class="fas fa-key"></i> طلب تفعيل جديد</h2>
                <p>املأ البيانات التالية لطلب تفعيل النظام</p>
            </div>

            <form id="activationForm">
                <div class="form-group">
                    <label for="fullName">الاسم الكامل *</label>
                    <input type="text" id="fullName" class="form-input" placeholder="أدخل الاسم الكامل" required>
                </div>

                <div class="form-group">
                    <label for="phone">رقم الهاتف *</label>
                    <input type="tel" id="phone" class="form-input" placeholder="05xxxxxxxx" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="wilaya">الولاية *</label>
                        <select id="wilaya" class="form-input" required>
                            <option value="">اختر الولاية</option>
                            <option value="الجزائر">الجزائر</option>
                            <option value="وهران">وهران</option>
                            <option value="قسنطينة">قسنطينة</option>
                            <option value="عنابة">عنابة</option>
                            <option value="باتنة">باتنة</option>
                            <option value="سطيف">سطيف</option>
                            <option value="سيدي بلعباس">سيدي بلعباس</option>
                            <option value="بسكرة">بسكرة</option>
                            <option value="تلمسان">تلمسان</option>
                            <option value="ورقلة">ورقلة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="municipality">البلدية *</label>
                        <input type="text" id="municipality" class="form-input" placeholder="أدخل اسم البلدية" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="companyName">اسم المؤسسة (اختياري)</label>
                    <input type="text" id="companyName" class="form-input" placeholder="أدخل اسم المؤسسة">
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-paper-plane"></i> إرسال طلب التفعيل
                </button>
            </form>
        </div>
    </div>

    <script>
        // بيانات تسجيل الدخول الافتراضية
        const defaultCredentials = {
            username: 'admin',
            password: 'admin123'
        };

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            showLoading(true);
            
            // محاكاة التحقق
            setTimeout(() => {
                if (username === defaultCredentials.username && password === defaultCredentials.password) {
                    showAlert('تم تسجيل الدخول بنجاح!', 'success');
                    setTimeout(() => {
                        window.location.href = 'license-management/new-dashboard.html';
                    }, 1000);
                } else {
                    showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
                }
                showLoading(false);
            }, 1500);
        });

        // طلب التفعيل
        document.getElementById('activationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                id: Date.now(),
                fullName: document.getElementById('fullName').value,
                phone: document.getElementById('phone').value,
                wilaya: document.getElementById('wilaya').value,
                municipality: document.getElementById('municipality').value,
                companyName: document.getElementById('companyName').value,
                status: 'pending',
                submittedAt: new Date().toISOString(),
                requestNumber: 'REQ-' + Date.now()
            };

            // حفظ الطلب
            try {
                let requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                requests.push(formData);
                localStorage.setItem('activationRequests', JSON.stringify(requests));
                
                showAlert('تم إرسال طلب التفعيل بنجاح! سيتم التواصل معك قريباً.', 'success');
                closeActivationModal();
                document.getElementById('activationForm').reset();
                
                console.log('✅ تم إرسال طلب تفعيل جديد:', formData);
                
            } catch (error) {
                showAlert('حدث خطأ في إرسال الطلب. حاول مرة أخرى.', 'error');
                console.error('❌ خطأ في حفظ طلب التفعيل:', error);
            }
        });

        // عرض/إخفاء التحميل
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('loginForm').style.display = show ? 'none' : 'block';
        }

        // عرض الرسائل
        function showAlert(message, type) {
            const alert = document.getElementById('alertMessage');
            alert.textContent = message;
            alert.className = `alert ${type}`;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // عرض نافذة التفعيل
        function showActivationModal() {
            document.getElementById('activationModal').style.display = 'block';
        }

        // إغلاق نافذة التفعيل
        function closeActivationModal() {
            document.getElementById('activationModal').style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('activationModal');
            if (e.target === modal) {
                closeActivationModal();
            }
        });

        console.log('🚀 نظام تسجيل الدخول جاهز');
        console.log('📝 بيانات الدخول الافتراضية:');
        console.log('   اسم المستخدم: admin');
        console.log('   كلمة المرور: admin123');
    </script>
</body>
</html>
