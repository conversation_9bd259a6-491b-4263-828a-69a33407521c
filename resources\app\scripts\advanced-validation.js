/**
 * نظام التحقق المتقدم من البيانات
 * Advanced Data Validation System
 * 
 * يوفر تحققاً شاملاً من صحة البيانات وتجنب التكرار
 * Provides comprehensive data validation and duplicate prevention
 */

class AdvancedValidator {
    constructor() {
        this.validationRules = {
            phone: /^(0[5-7][0-9]{8})$/,  // أرقام الهاتف الجزائرية
            email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            plateNumber: /^[0-9]{4,6}-[0-9]{2}$/,  // تنسيق أرقام اللوحات الجزائرية
            serialNumber: /^[A-Z0-9]{6,20}$/,  // الرقم التسلسلي للخزان
            cardNumber: /^[0-9]{10,16}$/  // رقم بطاقة الغاز
        };
        
        this.duplicateChecks = new Map();
        this.validationHistory = [];
        this.errorMessages = {
            ar: {
                required: 'هذا الحقل مطلوب',
                invalid_phone: 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 05، 06، أو 07)',
                invalid_email: 'عنوان البريد الإلكتروني غير صحيح',
                invalid_plate: 'رقم اللوحة غير صحيح (مثال: 123456-16)',
                invalid_serial: 'الرقم التسلسلي غير صحيح (6-20 حرف/رقم)',
                invalid_card: 'رقم البطاقة غير صحيح (10-16 رقم)',
                duplicate_phone: 'رقم الهاتف مسجل مسبقاً',
                duplicate_email: 'البريد الإلكتروني مسجل مسبقاً',
                duplicate_plate: 'رقم اللوحة مسجل مسبقاً',
                duplicate_serial: 'الرقم التسلسلي مسجل مسبقاً',
                duplicate_card: 'رقم البطاقة مسجل مسبقاً'
            }
        };
    }

    /**
     * التحقق من صحة البيانات الأساسية
     * @param {Object} data - البيانات المراد التحقق منها
     * @param {string} type - نوع البيانات (customer, vehicle, tank, card)
     * @returns {Object} نتيجة التحقق
     */
    validateBasicData(data, type) {
        const errors = [];
        const warnings = [];

        switch (type) {
            case 'customer':
                return this.validateCustomer(data);
            case 'vehicle':
                return this.validateVehicle(data);
            case 'tank':
                return this.validateTank(data);
            case 'card':
                return this.validateCard(data);
            default:
                errors.push('نوع البيانات غير معروف');
        }

        return { isValid: errors.length === 0, errors, warnings };
    }

    /**
     * التحقق من بيانات الزبون
     */
    validateCustomer(customer) {
        const errors = [];
        const warnings = [];

        // التحقق من الحقول المطلوبة
        if (!customer.name || customer.name.trim().length < 2) {
            errors.push('اسم الزبون مطلوب ويجب أن يكون حرفين على الأقل');
        }

        if (!customer.phone) {
            errors.push('رقم الهاتف مطلوب');
        } else if (!this.validationRules.phone.test(customer.phone)) {
            errors.push(this.errorMessages.ar.invalid_phone);
        }

        if (!customer.address || customer.address.trim().length < 5) {
            errors.push('العنوان مطلوب ويجب أن يكون 5 أحرف على الأقل');
        }

        // التحقق من البريد الإلكتروني (اختياري)
        if (customer.email && !this.validationRules.email.test(customer.email)) {
            errors.push(this.errorMessages.ar.invalid_email);
        }

        // التحقق من التكرار
        const duplicateCheck = this.checkCustomerDuplicates(customer);
        if (!duplicateCheck.isUnique) {
            errors.push(...duplicateCheck.errors);
            warnings.push(...duplicateCheck.warnings);
        }

        return { isValid: errors.length === 0, errors, warnings };
    }

    /**
     * التحقق من بيانات السيارة
     */
    validateVehicle(vehicle) {
        const errors = [];
        const warnings = [];

        // التحقق من الحقول المطلوبة
        if (!vehicle.plateNumber) {
            errors.push('رقم اللوحة مطلوب');
        } else if (!this.validationRules.plateNumber.test(vehicle.plateNumber)) {
            errors.push(this.errorMessages.ar.invalid_plate);
        }

        if (!vehicle.brand || vehicle.brand.trim().length < 2) {
            errors.push('ماركة السيارة مطلوبة');
        }

        if (!vehicle.model || vehicle.model.trim().length < 2) {
            errors.push('موديل السيارة مطلوب');
        }

        // التحقق من سنة الصنع
        if (vehicle.year) {
            const currentYear = new Date().getFullYear();
            const year = parseInt(vehicle.year);
            if (year < 1950 || year > currentYear + 1) {
                warnings.push(`سنة الصنع غير معقولة: ${year}`);
            }
        }

        // التحقق من التكرار
        const duplicateCheck = this.checkVehicleDuplicates(vehicle);
        if (!duplicateCheck.isUnique) {
            errors.push(...duplicateCheck.errors);
            warnings.push(...duplicateCheck.warnings);
        }

        return { isValid: errors.length === 0, errors, warnings };
    }

    /**
     * التحقق من بيانات الخزان
     */
    validateTank(tank) {
        const errors = [];
        const warnings = [];

        // التحقق من الحقول المطلوبة
        if (!tank.serialNumber) {
            errors.push('الرقم التسلسلي للخزان مطلوب');
        } else if (!this.validationRules.serialNumber.test(tank.serialNumber)) {
            errors.push(this.errorMessages.ar.invalid_serial);
        }

        if (!tank.brand || tank.brand.trim().length < 2) {
            errors.push('ماركة الخزان مطلوبة');
        }

        if (!tank.type) {
            errors.push('نوع الخزان مطلوب');
        }

        // التحقق من السعة
        if (!tank.capacity || tank.capacity <= 0) {
            errors.push('سعة الخزان مطلوبة ويجب أن تكون أكبر من صفر');
        } else if (tank.capacity > 200) {
            warnings.push('سعة الخزان كبيرة جداً، تأكد من صحة البيانات');
        }

        // التحقق من تاريخ الصنع
        if (tank.manufacturingDate) {
            const mfgDate = new Date(tank.manufacturingDate);
            const now = new Date();
            if (mfgDate > now) {
                errors.push('تاريخ الصنع لا يمكن أن يكون في المستقبل');
            }
        }

        // التحقق من التكرار
        const duplicateCheck = this.checkTankDuplicates(tank);
        if (!duplicateCheck.isUnique) {
            errors.push(...duplicateCheck.errors);
            warnings.push(...duplicateCheck.warnings);
        }

        return { isValid: errors.length === 0, errors, warnings };
    }

    /**
     * التحقق من بيانات بطاقة الغاز
     */
    validateCard(card) {
        const errors = [];
        const warnings = [];

        // التحقق من رقم البطاقة
        if (!card.cardNumber) {
            errors.push('رقم البطاقة مطلوب');
        } else if (!this.validationRules.cardNumber.test(card.cardNumber)) {
            errors.push(this.errorMessages.ar.invalid_card);
        }

        // التحقق من تواريخ الإصدار والانتهاء
        if (card.issueDate && card.expiryDate) {
            const issueDate = new Date(card.issueDate);
            const expiryDate = new Date(card.expiryDate);
            
            if (expiryDate <= issueDate) {
                errors.push('تاريخ انتهاء البطاقة يجب أن يكون بعد تاريخ الإصدار');
            }
        }

        // التحقق من التكرار
        const duplicateCheck = this.checkCardDuplicates(card);
        if (!duplicateCheck.isUnique) {
            errors.push(...duplicateCheck.errors);
            warnings.push(...duplicateCheck.warnings);
        }

        return { isValid: errors.length === 0, errors, warnings };
    }

    /**
     * فحص تكرار بيانات الزبون
     */
    checkCustomerDuplicates(customer) {
        const errors = [];
        const warnings = [];
        let isUnique = true;

        if (typeof appData !== 'undefined' && appData.customers) {
            // فحص تكرار رقم الهاتف
            const phoneExists = appData.customers.find(c => 
                c.id !== customer.id && c.phone === customer.phone
            );
            if (phoneExists) {
                errors.push(`${this.errorMessages.ar.duplicate_phone}: ${phoneExists.name}`);
                isUnique = false;
            }

            // فحص تكرار البريد الإلكتروني
            if (customer.email) {
                const emailExists = appData.customers.find(c => 
                    c.id !== customer.id && c.email === customer.email
                );
                if (emailExists) {
                    errors.push(`${this.errorMessages.ar.duplicate_email}: ${emailExists.name}`);
                    isUnique = false;
                }
            }

            // فحص الأسماء المتشابهة
            const similarNames = appData.customers.filter(c => 
                c.id !== customer.id && 
                this.calculateSimilarity(c.name, customer.name) > 0.8
            );
            if (similarNames.length > 0) {
                warnings.push(`أسماء متشابهة موجودة: ${similarNames.map(c => c.name).join(', ')}`);
            }
        }

        return { isUnique, errors, warnings };
    }

    /**
     * فحص تكرار بيانات السيارة
     */
    checkVehicleDuplicates(vehicle) {
        const errors = [];
        const warnings = [];
        let isUnique = true;

        if (typeof appData !== 'undefined' && appData.vehicles) {
            const plateExists = appData.vehicles.find(v => 
                v.id !== vehicle.id && v.plateNumber === vehicle.plateNumber
            );
            if (plateExists) {
                const owner = appData.customers.find(c => c.id === plateExists.customerId);
                errors.push(`${this.errorMessages.ar.duplicate_plate}: ${owner ? owner.name : 'مالك غير معروف'}`);
                isUnique = false;
            }
        }

        return { isUnique, errors, warnings };
    }

    /**
     * فحص تكرار بيانات الخزان
     */
    checkTankDuplicates(tank) {
        const errors = [];
        const warnings = [];
        let isUnique = true;

        if (typeof appData !== 'undefined' && appData.gasTanks) {
            const serialExists = appData.gasTanks.find(t => 
                t.id !== tank.id && t.serialNumber === tank.serialNumber
            );
            if (serialExists) {
                errors.push(`${this.errorMessages.ar.duplicate_serial}: ${tank.serialNumber}`);
                isUnique = false;
            }
        }

        return { isUnique, errors, warnings };
    }

    /**
     * فحص تكرار بيانات البطاقة
     */
    checkCardDuplicates(card) {
        const errors = [];
        const warnings = [];
        let isUnique = true;

        if (typeof appData !== 'undefined' && appData.gasCards) {
            const cardExists = appData.gasCards.find(c => 
                c.id !== card.id && c.cardNumber === card.cardNumber
            );
            if (cardExists) {
                errors.push(`${this.errorMessages.ar.duplicate_card}: ${card.cardNumber}`);
                isUnique = false;
            }
        }

        return { isUnique, errors, warnings };
    }

    /**
     * حساب التشابه بين نصين
     */
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const distance = this.levenshteinDistance(longer, shorter);
        return (longer.length - distance) / longer.length;
    }

    /**
     * حساب مسافة Levenshtein
     */
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    /**
     * تسجيل عملية التحقق في السجل
     */
    logValidation(type, data, result) {
        this.validationHistory.push({
            timestamp: new Date().toISOString(),
            type,
            data: { ...data },
            result: { ...result }
        });

        // الاحتفاظ بآخر 100 عملية تحقق فقط
        if (this.validationHistory.length > 100) {
            this.validationHistory = this.validationHistory.slice(-100);
        }
    }

    /**
     * الحصول على إحصائيات التحقق
     */
    getValidationStats() {
        const total = this.validationHistory.length;
        const successful = this.validationHistory.filter(v => v.result.isValid).length;
        const failed = total - successful;

        return {
            total,
            successful,
            failed,
            successRate: total > 0 ? (successful / total * 100).toFixed(2) : 0
        };
    }
}

// إنشاء مثيل عام للمدقق
const advancedValidator = new AdvancedValidator();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedValidator;
}
