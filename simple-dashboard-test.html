<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار لوحة التراخيص المبسط</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            direction: rtl;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            text-align: center;
            padding: 0 2rem 2rem;
            border-bottom: 1px solid #34495e;
        }

        .sidebar-nav ul {
            list-style: none;
            padding: 1rem 0;
        }

        .nav-item {
            padding: 1rem 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-item:hover {
            background: #34495e;
        }

        .nav-item.active {
            background: #3498db;
            border-right: 4px solid #2980b9;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
        }

        .content-section {
            display: none;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #3498db;
        }

        .section-header h2 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .test-info {
            background: #e8f4fd;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border-right: 4px solid #3498db;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
        }

        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> لوحة التراخيص</h2>
                <p>نظام إدارة التراخيص</p>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active" data-section="overview">
                        <i class="fas fa-chart-dashboard"></i>
                        <span>نظرة عامة</span>
                    </li>
                    <li class="nav-item" data-section="requests">
                        <i class="fas fa-inbox"></i>
                        <span>طلبات التفعيل</span>
                    </li>
                    <li class="nav-item" data-section="licenses">
                        <i class="fas fa-key"></i>
                        <span>التراخيص المفعلة</span>
                    </li>
                    <li class="nav-item" data-section="generate">
                        <i class="fas fa-plus-circle"></i>
                        <span>إنشاء ترخيص</span>
                    </li>
                    <li class="nav-item" data-section="statistics">
                        <i class="fas fa-chart-bar"></i>
                        <span>الإحصائيات</span>
                    </li>
                    <li class="nav-item" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </li>
                </ul>
            </nav>
        </aside>

        <main class="main-content">
            <!-- Overview Section -->
            <section id="overview-section" class="content-section active">
                <div class="section-header">
                    <h2><i class="fas fa-chart-dashboard"></i> نظرة عامة</h2>
                    <p>إدارة شاملة لتراخيص النظام</p>
                </div>
                <div class="test-info">
                    <h3>✅ قسم النظرة العامة</h3>
                    <p>هذا القسم يعمل بشكل صحيح ويظهر المعلومات الأساسية للنظام.</p>
                </div>
                <button class="btn" onclick="testLog('تم النقر على زر في قسم النظرة العامة')">
                    <i class="fas fa-test"></i> اختبار الزر
                </button>
            </section>

            <!-- Requests Section -->
            <section id="requests-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-inbox"></i> طلبات التفعيل</h2>
                    <p>إدارة طلبات تفعيل التراخيص</p>
                </div>
                <div class="test-info">
                    <h3>✅ قسم طلبات التفعيل</h3>
                    <p>هذا القسم يعرض جميع طلبات التفعيل الواردة من العملاء.</p>
                </div>
                <button class="btn" onclick="testLog('تم النقر على زر في قسم طلبات التفعيل')">
                    <i class="fas fa-check"></i> موافقة على طلب
                </button>
            </section>

            <!-- Licenses Section -->
            <section id="licenses-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-key"></i> التراخيص المفعلة</h2>
                    <p>عرض وإدارة التراخيص المفعلة</p>
                </div>
                <div class="test-info">
                    <h3>✅ قسم التراخيص المفعلة</h3>
                    <p>هذا القسم يعرض جميع التراخيص النشطة في النظام.</p>
                </div>
                <button class="btn" onclick="testLog('تم النقر على زر في قسم التراخيص المفعلة')">
                    <i class="fas fa-edit"></i> تعديل ترخيص
                </button>
            </section>

            <!-- Generate Section -->
            <section id="generate-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-plus-circle"></i> إنشاء ترخيص جديد</h2>
                    <p>إنشاء تراخيص جديدة للعملاء</p>
                </div>
                <div class="test-info">
                    <h3>✅ قسم إنشاء ترخيص</h3>
                    <p>هذا القسم يسمح بإنشاء تراخيص جديدة للعملاء.</p>
                </div>
                <button class="btn" onclick="testLog('تم النقر على زر في قسم إنشاء ترخيص')">
                    <i class="fas fa-plus"></i> إنشاء ترخيص جديد
                </button>
            </section>

            <!-- Statistics Section -->
            <section id="statistics-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> الإحصائيات والتقارير</h2>
                    <p>تقارير وإحصائيات مفصلة</p>
                </div>
                <div class="test-info">
                    <h3>✅ قسم الإحصائيات</h3>
                    <p>هذا القسم يعرض الإحصائيات والتقارير المفصلة.</p>
                </div>
                <button class="btn" onclick="testLog('تم النقر على زر في قسم الإحصائيات')">
                    <i class="fas fa-chart-line"></i> عرض التقرير
                </button>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> إعدادات النظام</h2>
                    <p>إعدادات وتكوين النظام</p>
                </div>
                <div class="test-info">
                    <h3>✅ قسم الإعدادات</h3>
                    <p>هذا القسم يسمح بتكوين إعدادات النظام.</p>
                </div>
                <button class="btn" onclick="testLog('تم النقر على زر في قسم الإعدادات')">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
            </section>

            <!-- Test Log -->
            <div id="testLog" class="log-area" style="display: none;"></div>
        </main>
    </div>

    <script>
        class SimpleDashboard {
            constructor() {
                this.currentSection = 'overview';
                this.init();
            }

            init() {
                console.log('Initializing Simple Dashboard...');
                this.bindEvents();
                console.log('Simple Dashboard initialized successfully');
            }

            bindEvents() {
                // Navigation
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const section = e.currentTarget.dataset.section;
                        console.log('Navigation clicked:', section);
                        this.switchSection(section);
                    });
                });
            }

            switchSection(section) {
                console.log('Switching to section:', section);
                
                // Update navigation
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-section="${section}"]`).classList.add('active');

                // Update content
                document.querySelectorAll('.content-section').forEach(contentSection => {
                    contentSection.classList.remove('active');
                });
                document.getElementById(`${section}-section`).classList.add('active');

                this.currentSection = section;
                this.testLog(`تم التبديل إلى قسم: ${section}`);
                
                console.log('Successfully switched to section:', section);
            }

            testLog(message) {
                const logElement = document.getElementById('testLog');
                const timestamp = new Date().toLocaleTimeString('ar-SA');
                
                logElement.style.display = 'block';
                logElement.innerHTML += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
                
                console.log(message);
            }
        }

        // Test function
        function testLog(message) {
            if (window.dashboard) {
                window.dashboard.testLog(message);
            }
        }

        // Initialize dashboard
        window.addEventListener('DOMContentLoaded', () => {
            window.dashboard = new SimpleDashboard();
            
            // Auto test navigation
            setTimeout(() => {
                console.log('Starting auto navigation test...');
                const sections = ['requests', 'licenses', 'generate', 'statistics', 'settings', 'overview'];
                let index = 0;
                
                const testInterval = setInterval(() => {
                    if (index >= sections.length) {
                        clearInterval(testInterval);
                        window.dashboard.testLog('✅ تم اختبار جميع الأقسام بنجاح');
                        return;
                    }
                    
                    window.dashboard.switchSection(sections[index]);
                    index++;
                }, 1500);
            }, 2000);
        });
    </script>
</body>
</html>
