<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للتطبيق</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .header h1 {
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #718096;
        }

        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f7fafc;
            border-radius: 10px;
            border-left: 4px solid #4299e1;
        }

        .test-section h3 {
            color: #2d3748;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #48bb78;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-warning {
            background: #ed8936;
        }

        .btn-warning:hover {
            background: #dd6b20;
        }

        .btn-info {
            background: #0bc5ea;
        }

        .btn-info:hover {
            background: #00b3d7;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-weight: 600;
        }

        .status.success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .status.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }

        .status.info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }

        .instructions {
            background: #edf2f7;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        .instructions h4 {
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .instructions ol {
            color: #4a5568;
            padding-right: 1.5rem;
        }

        .instructions li {
            margin-bottom: 0.5rem;
        }

        .code {
            background: #1a202c;
            color: #e2e8f0;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> اختبار سريع للتطبيق</h1>
            <p>اختبار نظام تسجيل الدخول الجديد وإنشاء التراخيص</p>
        </div>

        <div class="instructions">
            <h4><i class="fas fa-list-ol"></i> خطوات الاختبار:</h4>
            <ol>
                <li>اضغط <span class="code">إنشاء تراخيص تجريبية</span> لإنشاء بيانات تجريبية</li>
                <li>اضغط <span class="code">فتح واجهة تسجيل الدخول</span> لاختبار تسجيل الدخول</li>
                <li>استخدم البيانات: <span class="code">admin / admin123</span> + أي مفتاح ترخيص</li>
                <li>اختبر إنشاء تراخيص جديدة من لوحة التحكم</li>
            </ol>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-database"></i> إعداد البيانات التجريبية</h3>
            <button class="btn btn-success" onclick="createSampleData()">
                <i class="fas fa-plus"></i> إنشاء تراخيص تجريبية
            </button>
            <button class="btn btn-info" onclick="checkData()">
                <i class="fas fa-search"></i> فحص البيانات الحالية
            </button>
            <button class="btn btn-warning" onclick="clearData()">
                <i class="fas fa-trash"></i> مسح البيانات
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-sign-in-alt"></i> اختبار تسجيل الدخول</h3>
            <button class="btn" onclick="openLogin()">
                <i class="fas fa-external-link-alt"></i> فتح واجهة تسجيل الدخول
            </button>
            <button class="btn btn-info" onclick="openDashboard()">
                <i class="fas fa-tachometer-alt"></i> فتح لوحة التحكم مباشرة
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-tools"></i> أدوات إضافية</h3>
            <button class="btn btn-info" onclick="openTestTool()">
                <i class="fas fa-wrench"></i> أداة الاختبار الشاملة
            </button>
            <button class="btn" onclick="openMainApp()">
                <i class="fas fa-home"></i> فتح التطبيق الرئيسي
            </button>
        </div>

        <div id="status"></div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}"><i class="fas fa-info-circle"></i> ${message}</div>`;
        }

        function createSampleData() {
            const sampleLicenses = [
                {
                    id: Date.now() + 1,
                    clientName: 'أحمد محمد علي',
                    clientPhone: '**********',
                    licenseKey: 'LIC-DEMO-ADMIN-001',
                    type: 'lifetime',
                    activatedAt: new Date().toISOString(),
                    expiresAt: null,
                    notes: 'ترخيص تجريبي للمطور',
                    createdBy: 'system'
                },
                {
                    id: Date.now() + 2,
                    clientName: 'فاطمة حسن أحمد',
                    clientPhone: '**********',
                    licenseKey: 'LIC-DEMO-USER-002',
                    type: 'yearly',
                    activatedAt: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                    notes: 'ترخيص سنوي تجريبي',
                    createdBy: 'system'
                },
                {
                    id: Date.now() + 3,
                    clientName: 'محمد عبدالله حسن',
                    clientPhone: '**********',
                    licenseKey: 'LIC-DEMO-TRIAL-003',
                    type: 'trial',
                    activatedAt: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    notes: 'ترخيص تجريبي 30 يوم',
                    createdBy: 'system'
                }
            ];

            localStorage.setItem('activeLicenses', JSON.stringify(sampleLicenses));
            showStatus('✅ تم إنشاء التراخيص التجريبية بنجاح!', 'success');
            
            setTimeout(() => {
                showStatus(`🔑 التراخيص المتاحة:<br>
                    - LIC-DEMO-ADMIN-001 (مدى الحياة)<br>
                    - LIC-DEMO-USER-002 (سنوي)<br>
                    - LIC-DEMO-TRIAL-003 (تجريبي)`, 'info');
            }, 1000);
        }

        function checkData() {
            const licenses = JSON.parse(localStorage.getItem('activeLicenses') || '[]');
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            
            showStatus(`📊 البيانات الحالية:<br>
                التراخيص المفعلة: ${licenses.length}<br>
                طلبات التفعيل: ${requests.length}`, 'info');
        }

        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('activeLicenses');
                localStorage.removeItem('activationRequests');
                showStatus('🗑️ تم مسح جميع البيانات', 'warning');
            }
        }

        function openLogin() {
            window.open('resources/app/src/new-login.html', '_blank');
            showStatus('🔗 تم فتح واجهة تسجيل الدخول في نافذة جديدة', 'success');
        }

        function openDashboard() {
            window.open('resources/app/src/license-management/new-dashboard.html', '_blank');
            showStatus('🔗 تم فتح لوحة التحكم في نافذة جديدة', 'success');
        }

        function openTestTool() {
            window.open('test-new-system.html', '_blank');
            showStatus('🔗 تم فتح أداة الاختبار الشاملة', 'success');
        }

        function openMainApp() {
            window.open('resources/app/index.html', '_blank');
            showStatus('🔗 تم فتح التطبيق الرئيسي', 'success');
        }

        // رسالة ترحيب
        window.addEventListener('load', () => {
            showStatus('🚀 أداة الاختبار السريع جاهزة للاستخدام', 'success');
        });
    </script>
</body>
</html>
