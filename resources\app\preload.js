const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
    // حفظ البيانات
    saveData: (data) => {
        try {
            console.log('🔄 محاولة حفظ البيانات في Electron...');
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');

            // التأكد من وجود المجلد
            const dataDir = path.dirname(dataPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
            console.log('✅ تم حفظ البيانات بنجاح في:', dataPath);
            return true;
        } catch (error) {
            console.error('❌ خطأ في حفظ البيانات:', error);
            return false;
        }
    },

    // تحميل البيانات
    loadData: () => {
        try {
            console.log('🔄 محاولة تحميل البيانات من Electron...');
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');

            if (fs.existsSync(dataPath)) {
                const data = fs.readFileSync(dataPath, 'utf8');
                const parsedData = JSON.parse(data);
                console.log('✅ تم تحميل البيانات بنجاح من:', dataPath);
                return parsedData;
            } else {
                console.log('⚠️ ملف البيانات غير موجود:', dataPath);
                return null;
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            return null;
        }
    },

    // إنشاء نسخة احتياطية
    createBackup: () => {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, 'data.json');
            const backupPath = path.join(__dirname, `backup_${Date.now()}.json`);
            
            if (fs.existsSync(dataPath)) {
                fs.copyFileSync(dataPath, backupPath);
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return false;
        }
    },

    // معالجة مربعات الحوار
    showSaveDialog: (options) => ipcRenderer.invoke('save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('open-dialog', options),

    // فتح روابط خارجية
    openExternal: (url) => ipcRenderer.invoke('open-external', url),

    // معلومات النظام
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),

    // إدارة الثيم
    setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
    getTheme: () => ipcRenderer.invoke('get-theme'),

    // إشعارات
    createAutoBackup: () => ipcRenderer.send('create-auto-backup'),

    // إشعارات النظام
    showNotification: (title, body, icon) => {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, { body, icon });
        }
    },

    // طلب إذن الإشعارات
    requestNotificationPermission: async () => {
        if ('Notification' in window) {
            return await Notification.requestPermission();
        }
        return 'denied';
    }
});
