// نظام تفعيل الترخيص
class ActivationSystem {
    constructor() {
        this.init();
        this.loadAlgeriaData();
        this.checkLicenseStatus();
    }

    init() {
        // تهيئة العناصر
        this.elements = {
            statusCard: document.getElementById('statusCard'),
            licenseStatus: document.getElementById('licenseStatus'),
            licenseType: document.getElementById('licenseType'),
            expiryDate: document.getElementById('expiryDate'),
            
            // أزرار العمل
            requestActivationBtn: document.getElementById('requestActivationBtn'),
            checkStatusBtn: document.getElementById('checkStatusBtn'),
            enterLicenseBtn: document.getElementById('enterLicenseBtn'),
            
            // نماذج
            activationForm: document.getElementById('activationForm'),
            licenseForm: document.getElementById('licenseForm'),
            
            // نموذج طلب التفعيل
            activationRequestForm: document.getElementById('activationRequestForm'),
            closeFormBtn: document.getElementById('closeFormBtn'),
            cancelFormBtn: document.getElementById('cancelFormBtn'),
            
            // نموذج إدخال الترخيص
            licenseEntryForm: document.getElementById('licenseEntryForm'),
            closeLicenseFormBtn: document.getElementById('closeLicenseFormBtn'),
            cancelLicenseFormBtn: document.getElementById('cancelLicenseFormBtn'),
            
            // قوائم الولايات والبلديات
            stateSelect: document.getElementById('state'),
            municipalitySelect: document.getElementById('municipality'),
            
            // عناصر أخرى
            loadingOverlay: document.getElementById('loadingOverlay'),
            notification: document.getElementById('notification')
        };

        this.bindEvents();
    }

    bindEvents() {
        // أزرار العمل الرئيسية
        this.elements.requestActivationBtn.addEventListener('click', () => this.showActivationForm());
        this.elements.checkStatusBtn.addEventListener('click', () => this.checkLicenseStatus());
        this.elements.enterLicenseBtn.addEventListener('click', () => this.showLicenseForm());

        // أزرار إغلاق النماذج
        this.elements.closeFormBtn.addEventListener('click', () => this.hideActivationForm());
        this.elements.cancelFormBtn.addEventListener('click', () => this.hideActivationForm());
        this.elements.closeLicenseFormBtn.addEventListener('click', () => this.hideLicenseForm());
        this.elements.cancelLicenseFormBtn.addEventListener('click', () => this.hideLicenseForm());

        // إرسال النماذج
        this.elements.activationRequestForm.addEventListener('submit', (e) => this.handleActivationRequest(e));
        this.elements.licenseEntryForm.addEventListener('submit', (e) => this.handleLicenseEntry(e));

        // تغيير الولاية
        this.elements.stateSelect.addEventListener('change', () => this.loadMunicipalities());
    }

    loadAlgeriaData() {
        // تحميل بيانات الولايات
        if (typeof algeriaData !== 'undefined') {
            this.populateStates();
        } else {
            console.error('بيانات الولايات غير متوفرة');
        }
    }

    populateStates() {
        const stateSelect = this.elements.stateSelect;
        stateSelect.innerHTML = '<option value="">اختر الولاية</option>';

        Object.keys(algeriaData).forEach(code => {
            const option = document.createElement('option');
            option.value = code;
            option.textContent = `${code} - ${algeriaData[code].name}`;
            stateSelect.appendChild(option);
        });
    }

    loadMunicipalities() {
        const stateCode = this.elements.stateSelect.value;
        const municipalitySelect = this.elements.municipalitySelect;

        if (!stateCode) {
            municipalitySelect.innerHTML = '<option value="">اختر البلدية</option>';
            municipalitySelect.disabled = true;
            return;
        }

        municipalitySelect.disabled = false;
        municipalitySelect.innerHTML = '<option value="">اختر البلدية</option>';

        if (algeriaData[stateCode] && algeriaData[stateCode].municipalities) {
            algeriaData[stateCode].municipalities.forEach(municipality => {
                const option = document.createElement('option');
                option.value = municipality;
                option.textContent = municipality;
                municipalitySelect.appendChild(option);
            });
        }
    }

    async checkLicenseStatus() {
        this.showLoading();
        
        try {
            // محاكاة فحص حالة الترخيص
            await this.delay(1500);
            
            const licenseData = this.getLicenseFromStorage();
            
            if (licenseData && licenseData.isActive) {
                this.updateStatusDisplay(licenseData);
                
                // إذا كان الترخيص مفعل، انتقل للتطبيق الرئيسي
                if (licenseData.isActive) {
                    this.showNotification('تم التحقق من الترخيص بنجاح', 'success');
                    setTimeout(() => {
                        this.redirectToMainApp();
                    }, 2000);
                }
            } else {
                this.updateStatusDisplay({
                    isActive: false,
                    type: 'غير محدد',
                    expiryDate: 'غير محدد'
                });
            }
        } catch (error) {
            this.showNotification('خطأ في فحص حالة الترخيص', 'error');
        } finally {
            this.hideLoading();
        }
    }

    updateStatusDisplay(licenseData) {
        const statusIcon = this.elements.statusCard.querySelector('.status-icon i');
        const statusTitle = this.elements.statusCard.querySelector('h2');
        const statusDesc = this.elements.statusCard.querySelector('p');

        if (licenseData.isActive) {
            statusIcon.className = 'fas fa-check-circle';
            statusIcon.style.color = 'var(--success-color)';
            statusTitle.textContent = 'الترخيص مفعل';
            statusDesc.textContent = 'يمكنك الآن الوصول إلى النظام';
            
            this.elements.licenseStatus.textContent = 'مفعل';
            this.elements.licenseStatus.className = 'value active';
        } else {
            statusIcon.className = 'fas fa-exclamation-triangle';
            statusIcon.style.color = 'var(--warning-color)';
            statusTitle.textContent = 'يتطلب تفعيل الترخيص';
            statusDesc.textContent = 'يجب تفعيل الترخيص للوصول إلى النظام';
            
            this.elements.licenseStatus.textContent = 'غير مفعل';
            this.elements.licenseStatus.className = 'value inactive';
        }

        this.elements.licenseType.textContent = licenseData.type || 'غير محدد';
        this.elements.expiryDate.textContent = licenseData.expiryDate || 'غير محدد';
    }

    showActivationForm() {
        this.elements.activationForm.style.display = 'block';
        this.elements.activationForm.scrollIntoView({ behavior: 'smooth' });
    }

    hideActivationForm() {
        this.elements.activationForm.style.display = 'none';
        this.elements.activationRequestForm.reset();
    }

    showLicenseForm() {
        this.elements.licenseForm.style.display = 'block';
        this.elements.licenseForm.scrollIntoView({ behavior: 'smooth' });
    }

    hideLicenseForm() {
        this.elements.licenseForm.style.display = 'none';
        this.elements.licenseEntryForm.reset();
    }

    async handleActivationRequest(e) {
        e.preventDefault();
        
        const formData = new FormData(this.elements.activationRequestForm);
        const requestData = {
            fullName: formData.get('fullName'),
            phone: formData.get('phone'),
            state: formData.get('state'),
            municipality: formData.get('municipality'),
            licenseType: formData.get('licenseTypeRequest'),
            notes: formData.get('notes'),
            deviceId: this.generateDeviceId(),
            requestDate: new Date().toISOString()
        };

        this.showLoading();

        try {
            // محاكاة إرسال طلب التفعيل
            await this.delay(2000);
            
            // حفظ الطلب محلياً
            this.saveActivationRequest(requestData);
            
            this.showNotification('تم إرسال طلب التفعيل بنجاح. سيتم مراجعته قريباً.', 'success');
            this.hideActivationForm();
            
        } catch (error) {
            this.showNotification('خطأ في إرسال طلب التفعيل', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async handleLicenseEntry(e) {
        e.preventDefault();
        
        const licenseKey = document.getElementById('licenseKey').value.trim();
        
        if (!licenseKey) {
            this.showNotification('يرجى إدخال رمز الترخيص', 'warning');
            return;
        }

        this.showLoading();

        try {
            // محاكاة التحقق من رمز الترخيص
            await this.delay(1500);
            
            const isValid = this.validateLicenseKey(licenseKey);
            
            if (isValid) {
                const licenseData = this.generateLicenseData(licenseKey);
                this.saveLicenseToStorage(licenseData);
                
                this.showNotification('تم تفعيل الترخيص بنجاح!', 'success');
                this.hideLicenseForm();
                
                setTimeout(() => {
                    this.redirectToMainApp();
                }, 2000);
            } else {
                this.showNotification('رمز الترخيص غير صحيح', 'error');
            }
            
        } catch (error) {
            this.showNotification('خطأ في تفعيل الترخيص', 'error');
        } finally {
            this.hideLoading();
        }
    }

    validateLicenseKey(key) {
        // محاكاة التحقق من رمز الترخيص
        // في التطبيق الحقيقي، سيتم التحقق من الخادم
        return key.length >= 10 && key.includes('-');
    }

    generateLicenseData(key) {
        const isLifetime = key.toLowerCase().includes('life');
        const expiryDate = isLifetime ? 'مدى الحياة' : this.calculateExpiryDate(30);
        
        return {
            key: key,
            isActive: true,
            type: isLifetime ? 'مدى الحياة' : 'تجريبي',
            expiryDate: expiryDate,
            activationDate: new Date().toISOString(),
            deviceId: this.generateDeviceId()
        };
    }

    calculateExpiryDate(days) {
        const date = new Date();
        date.setDate(date.getDate() + days);
        return date.toLocaleDateString('ar-SA');
    }

    generateDeviceId() {
        return 'device_' + Math.random().toString(36).substr(2, 9);
    }

    getLicenseFromStorage() {
        try {
            const license = localStorage.getItem('fuelSystemLicense');
            return license ? JSON.parse(license) : null;
        } catch {
            return null;
        }
    }

    saveLicenseToStorage(licenseData) {
        localStorage.setItem('fuelSystemLicense', JSON.stringify(licenseData));
    }

    saveActivationRequest(requestData) {
        const requests = this.getActivationRequests();
        requests.push(requestData);
        localStorage.setItem('activationRequests', JSON.stringify(requests));
    }

    getActivationRequests() {
        try {
            const requests = localStorage.getItem('activationRequests');
            return requests ? JSON.parse(requests) : [];
        } catch {
            return [];
        }
    }

    redirectToMainApp() {
        // التحقق من وجود Electron
        if (typeof window !== 'undefined' && window.electronAPI) {
            window.electronAPI.redirectToMain();
        } else {
            // في حالة المتصفح العادي
            window.location.href = '../../index.html';
        }
    }

    showLoading() {
        this.elements.loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        this.elements.loadingOverlay.style.display = 'none';
    }

    showNotification(message, type = 'info') {
        const notification = this.elements.notification;
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        // تحديد الأيقونة حسب النوع
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        icon.className = `notification-icon ${icons[type]}`;
        messageEl.textContent = message;
        notification.className = `notification ${type}`;
        notification.style.display = 'block';

        // إخفاء الإشعار بعد 5 ثوان
        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new ActivationSystem();
});
