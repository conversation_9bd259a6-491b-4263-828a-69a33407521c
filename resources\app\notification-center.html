<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز الإشعارات - نظام إدارة الوقود</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/styles.css">
    <link rel="stylesheet" href="styles/enhanced-notifications.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        body {
            background: var(--bg-color, #f8fafc);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }

        .notification-center {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-bg, #ffffff);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .center-header {
            background: linear-gradient(135deg, var(--primary-color, #3b82f6) 0%, var(--primary-dark, #2563eb) 100%);
            color: white;
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .center-title {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .center-controls {
            display: flex;
            gap: 12px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .center-content {
            padding: 24px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: var(--card-bg, #ffffff);
            border: 1px solid var(--border-color, #e5e7eb);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary-color, #3b82f6);
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--text-secondary, #6b7280);
            font-size: 14px;
        }

        .filters-section {
            background: var(--gray-50, #f9fafb);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .filters-title {
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary, #1f2937);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary, #6b7280);
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color, #e5e7eb);
            border-radius: 8px;
            font-size: 14px;
        }

        .notifications-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .notification-item {
            background: var(--card-bg, #ffffff);
            border: 1px solid var(--border-color, #e5e7eb);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .notification-item:hover {
            border-color: var(--primary-color, #3b82f6);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .notification-item.unread {
            border-left: 4px solid var(--primary-color, #3b82f6);
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.02) 0%, transparent 100%);
        }

        .notification-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .notification-type {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .notification-time {
            font-size: 12px;
            color: var(--text-secondary, #6b7280);
        }

        .notification-content {
            margin-bottom: 12px;
        }

        .notification-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: var(--text-primary, #1f2937);
        }

        .notification-message {
            color: var(--text-secondary, #6b7280);
            font-size: 14px;
            line-height: 1.5;
        }

        .notification-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .action-btn-small {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn-small.primary {
            background: var(--primary-color, #3b82f6);
            color: white;
        }

        .action-btn-small.secondary {
            background: var(--gray-100, #f3f4f6);
            color: var(--text-primary, #1f2937);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary, #6b7280);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            margin-top: 24px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid var(--border-color, #e5e7eb);
            background: var(--card-bg, #ffffff);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover {
            border-color: var(--primary-color, #3b82f6);
            color: var(--primary-color, #3b82f6);
        }

        .pagination-btn.active {
            background: var(--primary-color, #3b82f6);
            color: white;
            border-color: var(--primary-color, #3b82f6);
        }

        @media (max-width: 768px) {
            .center-header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .notification-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="notification-center">
        <!-- رأس مركز الإشعارات -->
        <div class="center-header">
            <div class="center-title">
                <i class="fas fa-bell"></i>
                مركز الإشعارات
            </div>
            <div class="center-controls">
                <button class="control-btn" onclick="markAllAsRead()">
                    <i class="fas fa-check-double"></i>
                    تحديد الكل كمقروء
                </button>
                <button class="control-btn" onclick="clearAllNotifications()">
                    <i class="fas fa-trash"></i>
                    مسح الكل
                </button>
                <button class="control-btn" onclick="refreshNotifications()">
                    <i class="fas fa-sync"></i>
                    تحديث
                </button>
            </div>
        </div>

        <div class="center-content">
            <!-- إحصائيات الإشعارات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-notifications">0</div>
                    <div class="stat-label">إجمالي الإشعارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="unread-notifications">0</div>
                    <div class="stat-label">غير مقروءة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="today-notifications">0</div>
                    <div class="stat-label">اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="urgent-notifications">0</div>
                    <div class="stat-label">عاجلة</div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="filters-section">
                <div class="filters-title">فلترة الإشعارات</div>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="filter-label">نوع الإشعار</label>
                        <select class="filter-input" id="type-filter">
                            <option value="">جميع الأنواع</option>
                            <option value="success">نجاح</option>
                            <option value="error">خطأ</option>
                            <option value="warning">تحذير</option>
                            <option value="info">معلومات</option>
                            <option value="appointment">مواعيد</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">الحالة</label>
                        <select class="filter-input" id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="unread">غير مقروءة</option>
                            <option value="read">مقروءة</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">التاريخ من</label>
                        <input type="date" class="filter-input" id="date-from-filter">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">التاريخ إلى</label>
                        <input type="date" class="filter-input" id="date-to-filter">
                    </div>
                </div>
            </div>

            <!-- قائمة الإشعارات -->
            <div class="notifications-list" id="notifications-list">
                <!-- سيتم ملء الإشعارات هنا بواسطة JavaScript -->
            </div>

            <!-- حالة فارغة -->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-bell-slash"></i>
                </div>
                <div>لا توجد إشعارات للعرض</div>
            </div>

            <!-- ترقيم الصفحات -->
            <div class="pagination" id="pagination">
                <!-- سيتم إنشاء أزرار الترقيم هنا -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="scripts/enhanced-notifications.js"></script>
    <script>
        // متغيرات عامة
        let allNotifications = [];
        let filteredNotifications = [];
        let currentPage = 1;
        const itemsPerPage = 10;

        // تهيئة مركز الإشعارات
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
            setupEventListeners();
            updateStats();
        });

        // تحميل الإشعارات
        function loadNotifications() {
            // محاكاة بيانات الإشعارات
            allNotifications = [
                {
                    id: 1,
                    type: 'success',
                    title: 'تم حفظ الزبون بنجاح',
                    message: 'تم حفظ بيانات الزبون "أحمد محمد" بنجاح',
                    timestamp: new Date(Date.now() - 1000 * 60 * 30),
                    read: false,
                    urgent: false
                },
                {
                    id: 2,
                    type: 'warning',
                    title: 'انتهاء صلاحية البطاقة',
                    message: 'بطاقة الغاز رقم GC-12345 ستنتهي صلاحيتها خلال 3 أيام',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
                    read: false,
                    urgent: true
                },
                {
                    id: 3,
                    type: 'appointment',
                    title: 'تذكير بموعد',
                    message: 'لديك موعد مع الزبون "سارة أحمد" اليوم الساعة 2:00 مساءً',
                    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),
                    read: true,
                    urgent: false
                }
            ];

            filteredNotifications = [...allNotifications];
            renderNotifications();
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('type-filter').addEventListener('change', applyFilters);
            document.getElementById('status-filter').addEventListener('change', applyFilters);
            document.getElementById('date-from-filter').addEventListener('change', applyFilters);
            document.getElementById('date-to-filter').addEventListener('change', applyFilters);
        }

        // تطبيق الفلاتر
        function applyFilters() {
            const typeFilter = document.getElementById('type-filter').value;
            const statusFilter = document.getElementById('status-filter').value;
            const dateFromFilter = document.getElementById('date-from-filter').value;
            const dateToFilter = document.getElementById('date-to-filter').value;

            filteredNotifications = allNotifications.filter(notification => {
                if (typeFilter && notification.type !== typeFilter) return false;
                if (statusFilter === 'read' && !notification.read) return false;
                if (statusFilter === 'unread' && notification.read) return false;
                if (dateFromFilter && notification.timestamp < new Date(dateFromFilter)) return false;
                if (dateToFilter && notification.timestamp > new Date(dateToFilter + 'T23:59:59')) return false;
                return true;
            });

            currentPage = 1;
            renderNotifications();
            updateStats();
        }

        // عرض الإشعارات
        function renderNotifications() {
            const container = document.getElementById('notifications-list');
            const emptyState = document.getElementById('empty-state');

            if (filteredNotifications.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            container.style.display = 'flex';
            emptyState.style.display = 'none';

            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageNotifications = filteredNotifications.slice(startIndex, endIndex);

            container.innerHTML = pageNotifications.map(notification => `
                <div class="notification-item ${notification.read ? '' : 'unread'}" onclick="markAsRead(${notification.id})">
                    <div class="notification-meta">
                        <div class="notification-type">
                            ${getTypeIcon(notification.type)}
                            ${getTypeLabel(notification.type)}
                            ${notification.urgent ? '<i class="fas fa-exclamation-triangle" style="color: #ef4444;"></i>' : ''}
                        </div>
                        <div class="notification-time">${formatTime(notification.timestamp)}</div>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-message">${notification.message}</div>
                    </div>
                    <div class="notification-actions">
                        <button class="action-btn-small primary" onclick="event.stopPropagation(); viewDetails(${notification.id})">
                            عرض التفاصيل
                        </button>
                        <button class="action-btn-small secondary" onclick="event.stopPropagation(); deleteNotification(${notification.id})">
                            حذف
                        </button>
                    </div>
                </div>
            `).join('');

            renderPagination();
        }

        // عرض ترقيم الصفحات
        function renderPagination() {
            const totalPages = Math.ceil(filteredNotifications.length / itemsPerPage);
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }

            pagination.style.display = 'flex';
            pagination.innerHTML = '';

            // زر السابق
            if (currentPage > 1) {
                pagination.innerHTML += `<button class="pagination-btn" onclick="changePage(${currentPage - 1})">السابق</button>`;
            }

            // أرقام الصفحات
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
                    pagination.innerHTML += `<button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
                } else if (i === currentPage - 2 || i === currentPage + 2) {
                    pagination.innerHTML += `<span>...</span>`;
                }
            }

            // زر التالي
            if (currentPage < totalPages) {
                pagination.innerHTML += `<button class="pagination-btn" onclick="changePage(${currentPage + 1})">التالي</button>`;
            }
        }

        // تغيير الصفحة
        function changePage(page) {
            currentPage = page;
            renderNotifications();
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('total-notifications').textContent = allNotifications.length;
            document.getElementById('unread-notifications').textContent = allNotifications.filter(n => !n.read).length;
            document.getElementById('today-notifications').textContent = allNotifications.filter(n => isToday(n.timestamp)).length;
            document.getElementById('urgent-notifications').textContent = allNotifications.filter(n => n.urgent).length;
        }

        // وظائف مساعدة
        function getTypeIcon(type) {
            const icons = {
                success: '<i class="fas fa-check-circle" style="color: #10b981;"></i>',
                error: '<i class="fas fa-times-circle" style="color: #ef4444;"></i>',
                warning: '<i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>',
                info: '<i class="fas fa-info-circle" style="color: #3b82f6;"></i>',
                appointment: '<i class="fas fa-calendar" style="color: #8b5cf6;"></i>'
            };
            return icons[type] || icons.info;
        }

        function getTypeLabel(type) {
            const labels = {
                success: 'نجاح',
                error: 'خطأ',
                warning: 'تحذير',
                info: 'معلومات',
                appointment: 'موعد'
            };
            return labels[type] || 'إشعار';
        }

        function formatTime(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 60) return `منذ ${minutes} دقيقة`;
            if (hours < 24) return `منذ ${hours} ساعة`;
            if (days < 7) return `منذ ${days} يوم`;
            return timestamp.toLocaleDateString('ar-EG');
        }

        function isToday(timestamp) {
            const today = new Date();
            return timestamp.toDateString() === today.toDateString();
        }

        // وظائف الإجراءات
        function markAsRead(id) {
            const notification = allNotifications.find(n => n.id === id);
            if (notification) {
                notification.read = true;
                renderNotifications();
                updateStats();
            }
        }

        function markAllAsRead() {
            allNotifications.forEach(n => n.read = true);
            renderNotifications();
            updateStats();
        }

        function clearAllNotifications() {
            if (confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
                allNotifications = [];
                filteredNotifications = [];
                renderNotifications();
                updateStats();
            }
        }

        function refreshNotifications() {
            loadNotifications();
            updateStats();
        }

        function viewDetails(id) {
            const notification = allNotifications.find(n => n.id === id);
            if (notification) {
                alert(`تفاصيل الإشعار:\n\nالعنوان: ${notification.title}\nالرسالة: ${notification.message}\nالوقت: ${notification.timestamp.toLocaleString('ar-EG')}`);
            }
        }

        function deleteNotification(id) {
            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                allNotifications = allNotifications.filter(n => n.id !== id);
                filteredNotifications = filteredNotifications.filter(n => n.id !== id);
                renderNotifications();
                updateStats();
            }
        }
    </script>
</body>
</html>
