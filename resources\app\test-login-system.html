<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام تسجيل الدخول - نظام إدارة الوقود</title>
    
    <!-- الأيقونات والخطوط -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-title {
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .test-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .test-content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #dee2e6;
        }

        .section-title {
            font-size: 20px;
            color: #2196F3;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .test-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .test-card-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .test-card-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .test-button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .test-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
        }

        .test-button.danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }

        .test-button.danger:hover {
            box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
        }

        .test-button.warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .test-button.warning:hover {
            box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
        }

        .test-results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 300px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success {
            background: #4CAF50;
        }

        .status-error {
            background: #f44336;
        }

        .status-warning {
            background: #ff9800;
        }

        .status-info {
            background: #2196F3;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .test-summary {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196F3;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }

        .summary-title {
            font-size: 18px;
            color: #1976D2;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1976D2;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .test-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .test-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- رأس الصفحة -->
        <div class="test-header">
            <h1 class="test-title">
                <i class="fas fa-vial"></i>
                اختبار نظام تسجيل الدخول
            </h1>
            <p class="test-subtitle">اختبار شامل لجميع مكونات نظام المصادقة والتراخيص الجديد</p>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="test-content">
            <!-- اختبارات أساسية -->
            <div class="test-section">
                <h2 class="section-title">
                    <i class="fas fa-check-circle"></i>
                    الاختبارات الأساسية
                </h2>
                <div class="test-grid">
                    <div class="test-card">
                        <div class="test-card-title">اختبار تحميل الصفحات</div>
                        <div class="test-card-description">فحص تحميل جميع صفحات النظام الجديد</div>
                        <button class="test-button" onclick="testPageLoading()">
                            <i class="fas fa-play"></i>
                            تشغيل الاختبار
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار ملفات JavaScript</div>
                        <div class="test-card-description">فحص تحميل وتشغيل ملفات JavaScript المطلوبة</div>
                        <button class="test-button" onclick="testJavaScriptFiles()">
                            <i class="fas fa-code"></i>
                            فحص الملفات
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار ملفات CSS</div>
                        <div class="test-card-description">فحص تحميل وتطبيق ملفات التنسيق</div>
                        <button class="test-button" onclick="testCSSFiles()">
                            <i class="fas fa-palette"></i>
                            فحص التنسيق
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار Local Storage</div>
                        <div class="test-card-description">فحص عمل نظام التخزين المحلي</div>
                        <button class="test-button" onclick="testLocalStorage()">
                            <i class="fas fa-database"></i>
                            فحص التخزين
                        </button>
                    </div>
                </div>
            </div>

            <!-- اختبارات نظام التراخيص -->
            <div class="test-section">
                <h2 class="section-title">
                    <i class="fas fa-key"></i>
                    اختبارات نظام التراخيص
                </h2>
                <div class="test-grid">
                    <div class="test-card">
                        <div class="test-card-title">اختبار إنشاء ترخيص تجريبي</div>
                        <div class="test-card-description">إنشاء ترخيص تجريبي واختبار صحته</div>
                        <button class="test-button" onclick="testTrialLicense()">
                            <i class="fas fa-clock"></i>
                            إنشاء ترخيص تجريبي
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار إنشاء ترخيص مدى الحياة</div>
                        <div class="test-card-description">إنشاء ترخيص دائم واختبار صحته</div>
                        <button class="test-button" onclick="testLifetimeLicense()">
                            <i class="fas fa-infinity"></i>
                            إنشاء ترخيص دائم
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار التحقق من الترخيص</div>
                        <div class="test-card-description">فحص آلية التحقق من صحة التراخيص</div>
                        <button class="test-button" onclick="testLicenseValidation()">
                            <i class="fas fa-shield-alt"></i>
                            فحص التحقق
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار انتهاء الصلاحية</div>
                        <div class="test-card-description">اختبار سلوك النظام عند انتهاء الترخيص</div>
                        <button class="test-button warning" onclick="testLicenseExpiry()">
                            <i class="fas fa-exclamation-triangle"></i>
                            اختبار الانتهاء
                        </button>
                    </div>
                </div>
            </div>

            <!-- اختبارات واجهة المستخدم -->
            <div class="test-section">
                <h2 class="section-title">
                    <i class="fas fa-desktop"></i>
                    اختبارات واجهة المستخدم
                </h2>
                <div class="test-grid">
                    <div class="test-card">
                        <div class="test-card-title">اختبار نموذج التفعيل</div>
                        <div class="test-card-description">فحص عمل نموذج طلب التفعيل</div>
                        <button class="test-button" onclick="testActivationForm()">
                            <i class="fas fa-wpforms"></i>
                            فحص النموذج
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار لوحة التحكم</div>
                        <div class="test-card-description">فحص عمل لوحة تحكم المطور</div>
                        <button class="test-button" onclick="testAdminDashboard()">
                            <i class="fas fa-tachometer-alt"></i>
                            فحص اللوحة
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار الإشعارات</div>
                        <div class="test-card-description">فحص نظام الإشعارات والرسائل</div>
                        <button class="test-button" onclick="testNotifications()">
                            <i class="fas fa-bell"></i>
                            فحص الإشعارات
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار التصميم المتجاوب</div>
                        <div class="test-card-description">فحص التصميم على أحجام شاشة مختلفة</div>
                        <button class="test-button" onclick="testResponsiveDesign()">
                            <i class="fas fa-mobile-alt"></i>
                            فحص التجاوب
                        </button>
                    </div>
                </div>
            </div>

            <!-- اختبارات الأمان -->
            <div class="test-section">
                <h2 class="section-title">
                    <i class="fas fa-lock"></i>
                    اختبارات الأمان
                </h2>
                <div class="test-grid">
                    <div class="test-card">
                        <div class="test-card-title">اختبار الحماية من التلاعب</div>
                        <div class="test-card-description">فحص مقاومة النظام للتلاعب</div>
                        <button class="test-button danger" onclick="testTamperProtection()">
                            <i class="fas fa-user-secret"></i>
                            اختبار الحماية
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار تشفير البيانات</div>
                        <div class="test-card-description">فحص تشفير بيانات التراخيص</div>
                        <button class="test-button danger" onclick="testDataEncryption()">
                            <i class="fas fa-lock"></i>
                            فحص التشفير
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار منع الوصول غير المصرح</div>
                        <div class="test-card-description">فحص منع الوصول بدون ترخيص صالح</div>
                        <button class="test-button danger" onclick="testUnauthorizedAccess()">
                            <i class="fas fa-ban"></i>
                            فحص المنع
                        </button>
                    </div>

                    <div class="test-card">
                        <div class="test-card-title">اختبار النسخ الاحتياطي</div>
                        <div class="test-card-description">فحص نظام النسخ الاحتياطي للتراخيص</div>
                        <button class="test-button" onclick="testBackupSystem()">
                            <i class="fas fa-save"></i>
                            فحص النسخ
                        </button>
                    </div>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="test-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-line"></i>
                    نتائج الاختبارات
                </h2>
                
                <!-- شريط التقدم -->
                <div class="progress-bar">
                    <div class="progress-fill" id="test-progress"></div>
                </div>
                
                <!-- منطقة النتائج -->
                <div class="test-results" id="test-results">
🔍 جاهز لبدء الاختبارات...

انقر على أي زر اختبار أعلاه لبدء فحص المكون المحدد.
سيتم عرض النتائج التفصيلية هنا.
                </div>
            </div>

            <!-- ملخص الاختبارات -->
            <div class="test-summary">
                <h3 class="summary-title">
                    <i class="fas fa-clipboard-check"></i>
                    ملخص الاختبارات
                </h3>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="total-tests">0</div>
                        <div class="stat-label">إجمالي الاختبارات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="passed-tests">0</div>
                        <div class="stat-label">اختبارات ناجحة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="failed-tests">0</div>
                        <div class="stat-label">اختبارات فاشلة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="success-rate">0%</div>
                        <div class="stat-label">معدل النجاح</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات الاختبار
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            tests: []
        };

        // دالة تسجيل النتائج
        function logResult(testName, success, message, details = '') {
            const timestamp = new Date().toLocaleString('ar-DZ');
            const status = success ? '✅' : '❌';
            const statusClass = success ? 'status-success' : 'status-error';
            
            testResults.total++;
            if (success) {
                testResults.passed++;
            } else {
                testResults.failed++;
            }
            
            testResults.tests.push({
                name: testName,
                success: success,
                message: message,
                details: details,
                timestamp: timestamp
            });
            
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML += `\n${status} [${timestamp}] ${testName}\n   ${message}\n${details ? '   التفاصيل: ' + details + '\n' : ''}`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            updateTestSummary();
            updateProgress();
        }

        // تحديث ملخص الاختبارات
        function updateTestSummary() {
            document.getElementById('total-tests').textContent = testResults.total;
            document.getElementById('passed-tests').textContent = testResults.passed;
            document.getElementById('failed-tests').textContent = testResults.failed;
            
            const successRate = testResults.total > 0 ? 
                Math.round((testResults.passed / testResults.total) * 100) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
        }

        // تحديث شريط التقدم
        function updateProgress() {
            const progressBar = document.getElementById('test-progress');
            const totalPossibleTests = 16; // عدد الاختبارات المتاحة
            const progress = Math.min((testResults.total / totalPossibleTests) * 100, 100);
            progressBar.style.width = progress + '%';
        }

        // اختبار تحميل الصفحات
        function testPageLoading() {
            logResult('اختبار تحميل الصفحات', false, 'بدء اختبار تحميل الصفحات...');

            const pages = [
                'activation.html',
                'admin-dashboard.html',
                'index.html'
            ];

            let loadedPages = 0;
            let failedPages = [];

            pages.forEach(page => {
                fetch(page)
                    .then(response => {
                        if (response.ok) {
                            loadedPages++;
                            logResult(`تحميل ${page}`, true, `تم تحميل الصفحة بنجاح`);
                        } else {
                            failedPages.push(page);
                            logResult(`تحميل ${page}`, false, `فشل في تحميل الصفحة - كود الخطأ: ${response.status}`);
                        }

                        if (loadedPages + failedPages.length === pages.length) {
                            const success = failedPages.length === 0;
                            logResult('اختبار تحميل الصفحات', success,
                                success ? 'تم تحميل جميع الصفحات بنجاح' : `فشل في تحميل ${failedPages.length} صفحة`,
                                failedPages.length > 0 ? `الصفحات الفاشلة: ${failedPages.join(', ')}` : ''
                            );
                        }
                    })
                    .catch(error => {
                        failedPages.push(page);
                        logResult(`تحميل ${page}`, false, `خطأ في الشبكة: ${error.message}`);
                    });
            });
        }

        // اختبار ملفات JavaScript
        function testJavaScriptFiles() {
            logResult('اختبار ملفات JavaScript', false, 'بدء فحص ملفات JavaScript...');

            const jsFiles = [
                'scripts/license-manager.js',
                'scripts/activation-system.js',
                'scripts/admin-dashboard.js'
            ];

            let checkedFiles = 0;
            let workingFiles = 0;

            jsFiles.forEach(file => {
                const script = document.createElement('script');
                script.src = file;
                script.onload = () => {
                    workingFiles++;
                    checkedFiles++;
                    logResult(`فحص ${file}`, true, 'تم تحميل الملف بنجاح');
                    checkJSComplete();
                };
                script.onerror = () => {
                    checkedFiles++;
                    logResult(`فحص ${file}`, false, 'فشل في تحميل الملف');
                    checkJSComplete();
                };
                document.head.appendChild(script);
            });

            function checkJSComplete() {
                if (checkedFiles === jsFiles.length) {
                    const success = workingFiles === jsFiles.length;
                    logResult('اختبار ملفات JavaScript', success,
                        `تم فحص ${jsFiles.length} ملف - نجح ${workingFiles} ملف`);
                }
            }
        }

        // اختبار ملفات CSS
        function testCSSFiles() {
            logResult('اختبار ملفات CSS', false, 'بدء فحص ملفات CSS...');

            const cssFiles = [
                'styles/activation.css',
                'styles/admin-dashboard.css',
                'styles/styles.css'
            ];

            let checkedFiles = 0;
            let workingFiles = 0;

            cssFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        checkedFiles++;
                        if (response.ok) {
                            workingFiles++;
                            logResult(`فحص ${file}`, true, 'ملف CSS متاح');
                        } else {
                            logResult(`فحص ${file}`, false, `ملف CSS غير متاح - كود: ${response.status}`);
                        }
                        checkCSSComplete();
                    })
                    .catch(error => {
                        checkedFiles++;
                        logResult(`فحص ${file}`, false, `خطأ في تحميل CSS: ${error.message}`);
                        checkCSSComplete();
                    });
            });

            function checkCSSComplete() {
                if (checkedFiles === cssFiles.length) {
                    const success = workingFiles === cssFiles.length;
                    logResult('اختبار ملفات CSS', success,
                        `تم فحص ${cssFiles.length} ملف - متاح ${workingFiles} ملف`);
                }
            }
        }

        // اختبار Local Storage
        function testLocalStorage() {
            logResult('اختبار Local Storage', false, 'بدء فحص نظام التخزين المحلي...');

            try {
                // اختبار الكتابة
                const testKey = 'test_storage_' + Date.now();
                const testValue = 'test_value_' + Math.random();
                localStorage.setItem(testKey, testValue);

                // اختبار القراءة
                const retrievedValue = localStorage.getItem(testKey);

                if (retrievedValue === testValue) {
                    // اختبار الحذف
                    localStorage.removeItem(testKey);
                    const deletedValue = localStorage.getItem(testKey);

                    if (deletedValue === null) {
                        logResult('اختبار Local Storage', true, 'نظام التخزين المحلي يعمل بشكل صحيح');
                    } else {
                        logResult('اختبار Local Storage', false, 'فشل في حذف البيانات من التخزين المحلي');
                    }
                } else {
                    logResult('اختبار Local Storage', false, 'فشل في قراءة البيانات من التخزين المحلي');
                }
            } catch (error) {
                logResult('اختبار Local Storage', false, `خطأ في التخزين المحلي: ${error.message}`);
            }
        }

        // اختبار إنشاء ترخيص تجريبي
        function testTrialLicense() {
            logResult('اختبار إنشاء ترخيص تجريبي', false, 'بدء إنشاء ترخيص تجريبي...');

            try {
                const trialLicense = {
                    id: 'trial_' + Date.now(),
                    type: 'trial',
                    userName: 'مستخدم تجريبي',
                    userPhone: '**********',
                    userState: 'الجزائر',
                    userMunicipality: 'الجزائر الوسطى',
                    issueDate: new Date().toISOString(),
                    expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 يوم
                    isActive: true,
                    features: ['basic_access', 'limited_data']
                };

                // حفظ الترخيص
                localStorage.setItem('trial_license', JSON.stringify(trialLicense));

                // التحقق من الحفظ
                const savedLicense = JSON.parse(localStorage.getItem('trial_license'));

                if (savedLicense && savedLicense.id === trialLicense.id) {
                    logResult('اختبار إنشاء ترخيص تجريبي', true,
                        'تم إنشاء وحفظ الترخيص التجريبي بنجاح',
                        `معرف الترخيص: ${trialLicense.id}, صالح حتى: ${new Date(trialLicense.expiryDate).toLocaleDateString('ar-DZ')}`);
                } else {
                    logResult('اختبار إنشاء ترخيص تجريبي', false, 'فشل في حفظ الترخيص التجريبي');
                }
            } catch (error) {
                logResult('اختبار إنشاء ترخيص تجريبي', false, `خطأ في إنشاء الترخيص: ${error.message}`);
            }
        }

        // اختبار إنشاء ترخيص مدى الحياة
        function testLifetimeLicense() {
            logResult('اختبار إنشاء ترخيص مدى الحياة', false, 'بدء إنشاء ترخيص دائم...');

            try {
                const lifetimeLicense = {
                    id: 'lifetime_' + Date.now(),
                    type: 'lifetime',
                    userName: 'مستخدم دائم',
                    userPhone: '**********',
                    userState: 'وهران',
                    userMunicipality: 'وهران',
                    issueDate: new Date().toISOString(),
                    expiryDate: 'مدى الحياة',
                    isActive: true,
                    features: ['full_access', 'unlimited_data', 'premium_support']
                };

                // حفظ الترخيص
                localStorage.setItem('lifetime_license', JSON.stringify(lifetimeLicense));

                // التحقق من الحفظ
                const savedLicense = JSON.parse(localStorage.getItem('lifetime_license'));

                if (savedLicense && savedLicense.id === lifetimeLicense.id) {
                    logResult('اختبار إنشاء ترخيص مدى الحياة', true,
                        'تم إنشاء وحفظ الترخيص الدائم بنجاح',
                        `معرف الترخيص: ${lifetimeLicense.id}, النوع: ${lifetimeLicense.expiryDate}`);
                } else {
                    logResult('اختبار إنشاء ترخيص مدى الحياة', false, 'فشل في حفظ الترخيص الدائم');
                }
            } catch (error) {
                logResult('اختبار إنشاء ترخيص مدى الحياة', false, `خطأ في إنشاء الترخيص: ${error.message}`);
            }
        }

        // اختبار التحقق من الترخيص
        function testLicenseValidation() {
            logResult('اختبار التحقق من الترخيص', false, 'بدء فحص آلية التحقق...');

            try {
                // إنشاء ترخيص صالح للاختبار
                const validLicense = {
                    id: 'valid_test_' + Date.now(),
                    type: 'trial',
                    isActive: true,
                    expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // غداً
                };

                // إنشاء ترخيص منتهي الصلاحية
                const expiredLicense = {
                    id: 'expired_test_' + Date.now(),
                    type: 'trial',
                    isActive: true,
                    expiryDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // أمس
                };

                // إنشاء ترخيص غير نشط
                const inactiveLicense = {
                    id: 'inactive_test_' + Date.now(),
                    type: 'trial',
                    isActive: false,
                    expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
                };

                // اختبار الترخيص الصالح
                const isValidLicenseValid = validateLicense(validLicense);
                const isExpiredLicenseValid = validateLicense(expiredLicense);
                const isInactiveLicenseValid = validateLicense(inactiveLicense);

                if (isValidLicenseValid && !isExpiredLicenseValid && !isInactiveLicenseValid) {
                    logResult('اختبار التحقق من الترخيص', true,
                        'آلية التحقق تعمل بشكل صحيح',
                        'تم التحقق من الترخيص الصالح وتم رفض التراخيص غير الصالحة');
                } else {
                    logResult('اختبار التحقق من الترخيص', false,
                        'خطأ في آلية التحقق',
                        `صالح: ${isValidLicenseValid}, منتهي: ${isExpiredLicenseValid}, غير نشط: ${isInactiveLicenseValid}`);
                }
            } catch (error) {
                logResult('اختبار التحقق من الترخيص', false, `خطأ في التحقق: ${error.message}`);
            }
        }

        // دالة التحقق من الترخيص
        function validateLicense(license) {
            if (!license || !license.isActive) {
                return false;
            }

            if (license.expiryDate !== 'مدى الحياة') {
                const expiryDate = new Date(license.expiryDate);
                const now = new Date();
                if (expiryDate <= now) {
                    return false;
                }
            }

            return true;
        }

        // اختبار انتهاء الصلاحية
        function testLicenseExpiry() {
            logResult('اختبار انتهاء الصلاحية', false, 'بدء اختبار سلوك النظام عند انتهاء الترخيص...');

            try {
                // إنشاء ترخيص منتهي الصلاحية
                const expiredLicense = {
                    id: 'expired_' + Date.now(),
                    type: 'trial',
                    isActive: true,
                    expiryDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // أمس
                };

                // حفظ الترخيص المنتهي
                localStorage.setItem('current_license', JSON.stringify(expiredLicense));

                // محاولة التحقق
                const isValid = validateLicense(expiredLicense);

                if (!isValid) {
                    logResult('اختبار انتهاء الصلاحية', true,
                        'النظام يتعامل بشكل صحيح مع التراخيص المنتهية الصلاحية',
                        'تم رفض الترخيص المنتهي الصلاحية كما هو متوقع');
                } else {
                    logResult('اختبار انتهاء الصلاحية', false,
                        'خطأ: النظام قبل ترخيص منتهي الصلاحية');
                }
            } catch (error) {
                logResult('اختبار انتهاء الصلاحية', false, `خطأ في الاختبار: ${error.message}`);
            }
        }

        // اختبار نموذج التفعيل
        function testActivationForm() {
            logResult('اختبار نموذج التفعيل', false, 'بدء فحص نموذج طلب التفعيل...');

            try {
                // محاولة فتح صفحة التفعيل
                const activationWindow = window.open('activation.html', '_blank', 'width=800,height=600');

                if (activationWindow) {
                    logResult('اختبار نموذج التفعيل', true,
                        'تم فتح نموذج التفعيل بنجاح',
                        'يمكن للمستخدمين الوصول إلى نموذج طلب التفعيل');

                    // إغلاق النافذة بعد ثانيتين
                    setTimeout(() => {
                        activationWindow.close();
                    }, 2000);
                } else {
                    logResult('اختبار نموذج التفعيل', false,
                        'فشل في فتح نموذج التفعيل',
                        'قد يكون هناك مشكلة في المسار أو الملف');
                }
            } catch (error) {
                logResult('اختبار نموذج التفعيل', false, `خطأ في فتح النموذج: ${error.message}`);
            }
        }

        // اختبار لوحة التحكم
        function testAdminDashboard() {
            logResult('اختبار لوحة التحكم', false, 'بدء فحص لوحة تحكم المطور...');

            try {
                // محاولة فتح لوحة التحكم
                const dashboardWindow = window.open('admin-dashboard.html', '_blank', 'width=1200,height=800');

                if (dashboardWindow) {
                    logResult('اختبار لوحة التحكم', true,
                        'تم فتح لوحة التحكم بنجاح',
                        'يمكن للمطورين الوصول إلى لوحة إدارة التراخيص');

                    // إغلاق النافذة بعد ثانيتين
                    setTimeout(() => {
                        dashboardWindow.close();
                    }, 2000);
                } else {
                    logResult('اختبار لوحة التحكم', false,
                        'فشل في فتح لوحة التحكم',
                        'قد يكون هناك مشكلة في المسار أو الملف');
                }
            } catch (error) {
                logResult('اختبار لوحة التحكم', false, `خطأ في فتح اللوحة: ${error.message}`);
            }
        }

        // اختبار الإشعارات
        function testNotifications() {
            logResult('اختبار الإشعارات', false, 'بدء فحص نظام الإشعارات...');

            try {
                // اختبار إشعار نجاح
                showTestNotification('نجاح', 'هذا إشعار اختبار للنجاح', 'success');

                setTimeout(() => {
                    // اختبار إشعار تحذير
                    showTestNotification('تحذير', 'هذا إشعار اختبار للتحذير', 'warning');

                    setTimeout(() => {
                        // اختبار إشعار خطأ
                        showTestNotification('خطأ', 'هذا إشعار اختبار للخطأ', 'error');

                        logResult('اختبار الإشعارات', true,
                            'تم اختبار جميع أنواع الإشعارات بنجاح',
                            'تم عرض إشعارات النجاح والتحذير والخطأ');
                    }, 1000);
                }, 1000);
            } catch (error) {
                logResult('اختبار الإشعارات', false, `خطأ في الإشعارات: ${error.message}`);
            }
        }

        // دالة عرض إشعار اختبار
        function showTestNotification(title, message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'warning' ? '#ff9800' : '#f44336'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                max-width: 300px;
                font-size: 14px;
                animation: slideIn 0.3s ease;
            `;

            notification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
                <div>${message}</div>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // اختبار التصميم المتجاوب
        function testResponsiveDesign() {
            logResult('اختبار التصميم المتجاوب', false, 'بدء فحص التصميم المتجاوب...');

            try {
                const originalWidth = window.innerWidth;
                const originalHeight = window.innerHeight;

                // اختبار أحجام شاشة مختلفة
                const screenSizes = [
                    { width: 320, height: 568, name: 'هاتف صغير' },
                    { width: 768, height: 1024, name: 'تابلت' },
                    { width: 1024, height: 768, name: 'تابلت أفقي' },
                    { width: 1920, height: 1080, name: 'سطح المكتب' }
                ];

                let testedSizes = 0;
                let responsiveIssues = [];

                screenSizes.forEach((size, index) => {
                    setTimeout(() => {
                        // محاكاة تغيير حجم الشاشة
                        const testResult = checkResponsiveLayout(size);

                        if (testResult.success) {
                            logResult(`تجاوب ${size.name}`, true, `التصميم متجاوب على ${size.name}`);
                        } else {
                            responsiveIssues.push(size.name);
                            logResult(`تجاوب ${size.name}`, false, `مشكلة في التجاوب على ${size.name}`);
                        }

                        testedSizes++;

                        if (testedSizes === screenSizes.length) {
                            const success = responsiveIssues.length === 0;
                            logResult('اختبار التصميم المتجاوب', success,
                                success ? 'التصميم متجاوب على جميع الأحجام' : `مشاكل في ${responsiveIssues.length} حجم`,
                                responsiveIssues.length > 0 ? `المشاكل في: ${responsiveIssues.join(', ')}` : '');
                        }
                    }, index * 500);
                });
            } catch (error) {
                logResult('اختبار التصميم المتجاوب', false, `خطأ في الاختبار: ${error.message}`);
            }
        }

        // فحص التخطيط المتجاوب
        function checkResponsiveLayout(size) {
            // محاكاة فحص التجاوب
            const container = document.querySelector('.test-container');
            const cards = document.querySelectorAll('.test-card');

            if (!container || cards.length === 0) {
                return { success: false, reason: 'عناصر غير موجودة' };
            }

            // فحص بسيط للتجاوب
            const containerWidth = container.offsetWidth;
            const isResponsive = containerWidth > 0 && containerWidth <= window.innerWidth;

            return { success: isResponsive, reason: isResponsive ? 'متجاوب' : 'غير متجاوب' };
        }

        // اختبار الحماية من التلاعب
        function testTamperProtection() {
            logResult('اختبار الحماية من التلاعب', false, 'بدء فحص مقاومة النظام للتلاعب...');

            try {
                // محاولة تعديل ترخيص في localStorage
                const originalLicense = localStorage.getItem('current_license');

                // محاولة تلاعب
                const tamperedLicense = {
                    id: 'hacked_license',
                    type: 'lifetime',
                    isActive: true,
                    expiryDate: 'مدى الحياة',
                    tampered: true
                };

                localStorage.setItem('current_license', JSON.stringify(tamperedLicense));

                // فحص إذا كان النظام يكتشف التلاعب
                const detectedTamper = detectTampering(tamperedLicense);

                if (detectedTamper) {
                    logResult('اختبار الحماية من التلاعب', true,
                        'النظام يكتشف محاولات التلاعب بنجاح',
                        'تم رفض الترخيص المُتلاعب به');
                } else {
                    logResult('اختبار الحماية من التلاعب', false,
                        'خطر: النظام لا يكتشف التلاعب',
                        'يمكن للمستخدمين تعديل التراخيص');
                }

                // استعادة الترخيص الأصلي
                if (originalLicense) {
                    localStorage.setItem('current_license', originalLicense);
                } else {
                    localStorage.removeItem('current_license');
                }
            } catch (error) {
                logResult('اختبار الحماية من التلاعب', false, `خطأ في الاختبار: ${error.message}`);
            }
        }

        // كشف التلاعب
        function detectTampering(license) {
            // فحص بسيط للتلاعب
            return license.hasOwnProperty('tampered') ||
                   license.id === 'hacked_license' ||
                   !license.hasOwnProperty('issueDate');
        }

        // اختبار تشفير البيانات
        function testDataEncryption() {
            logResult('اختبار تشفير البيانات', false, 'بدء فحص تشفير بيانات التراخيص...');

            try {
                const testData = 'بيانات حساسة للاختبار';

                // محاولة تشفير
                const encrypted = simpleEncrypt(testData);

                // محاولة فك التشفير
                const decrypted = simpleDecrypt(encrypted);

                if (decrypted === testData) {
                    logResult('اختبار تشفير البيانات', true,
                        'نظام التشفير يعمل بشكل صحيح',
                        'تم تشفير وفك تشفير البيانات بنجاح');
                } else {
                    logResult('اختبار تشفير البيانات', false,
                        'خطأ في نظام التشفير',
                        'فشل في فك تشفير البيانات بشكل صحيح');
                }
            } catch (error) {
                logResult('اختبار تشفير البيانات', false, `خطأ في التشفير: ${error.message}`);
            }
        }

        // تشفير بسيط للاختبار
        function simpleEncrypt(text) {
            return btoa(text); // Base64 encoding
        }

        // فك تشفير بسيط
        function simpleDecrypt(encrypted) {
            return atob(encrypted); // Base64 decoding
        }

        // اختبار منع الوصول غير المصرح
        function testUnauthorizedAccess() {
            logResult('اختبار منع الوصول غير المصرح', false, 'بدء فحص منع الوصول بدون ترخيص...');

            try {
                // إزالة أي ترخيص موجود
                const originalLicense = localStorage.getItem('current_license');
                localStorage.removeItem('current_license');

                // محاولة الوصول بدون ترخيص
                const hasAccess = checkAccess();

                if (!hasAccess) {
                    logResult('اختبار منع الوصول غير المصرح', true,
                        'النظام يمنع الوصول بدون ترخيص صالح',
                        'تم رفض الوصول كما هو متوقع');
                } else {
                    logResult('اختبار منع الوصول غير المصرح', false,
                        'خطر: النظام يسمح بالوصول بدون ترخيص',
                        'يمكن للمستخدمين الوصول بدون تفعيل');
                }

                // استعادة الترخيص الأصلي
                if (originalLicense) {
                    localStorage.setItem('current_license', originalLicense);
                }
            } catch (error) {
                logResult('اختبار منع الوصول غير المصرح', false, `خطأ في الاختبار: ${error.message}`);
            }
        }

        // فحص الوصول
        function checkAccess() {
            const license = localStorage.getItem('current_license');
            if (!license) return false;

            try {
                const parsedLicense = JSON.parse(license);
                return validateLicense(parsedLicense);
            } catch {
                return false;
            }
        }

        // اختبار النسخ الاحتياطي
        function testBackupSystem() {
            logResult('اختبار النسخ الاحتياطي', false, 'بدء فحص نظام النسخ الاحتياطي...');

            try {
                // إنشاء بيانات للنسخ الاحتياطي
                const testLicense = {
                    id: 'backup_test_' + Date.now(),
                    type: 'trial',
                    isActive: true,
                    expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
                };

                // حفظ البيانات
                localStorage.setItem('test_license', JSON.stringify(testLicense));

                // إنشاء نسخة احتياطية
                const backup = createBackup();

                // حذف البيانات الأصلية
                localStorage.removeItem('test_license');

                // استعادة من النسخة الاحتياطية
                const restored = restoreFromBackup(backup);

                if (restored && restored.test_license) {
                    const restoredLicense = JSON.parse(restored.test_license);
                    if (restoredLicense.id === testLicense.id) {
                        logResult('اختبار النسخ الاحتياطي', true,
                            'نظام النسخ الاحتياطي يعمل بشكل صحيح',
                            'تم إنشاء واستعادة النسخة الاحتياطية بنجاح');
                    } else {
                        logResult('اختبار النسخ الاحتياطي', false, 'خطأ في استعادة البيانات');
                    }
                } else {
                    logResult('اختبار النسخ الاحتياطي', false, 'فشل في استعادة النسخة الاحتياطية');
                }

                // تنظيف
                localStorage.removeItem('test_license');
            } catch (error) {
                logResult('اختبار النسخ الاحتياطي', false, `خطأ في النسخ الاحتياطي: ${error.message}`);
            }
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            const backup = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                backup[key] = localStorage.getItem(key);
            }
            return backup;
        }

        // استعادة من النسخة الاحتياطية
        function restoreFromBackup(backup) {
            try {
                for (const key in backup) {
                    localStorage.setItem(key, backup[key]);
                }
                return backup;
            } catch (error) {
                console.error('خطأ في الاستعادة:', error);
                return null;
            }
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            logResult('تشغيل جميع الاختبارات', false, 'بدء تشغيل جميع الاختبارات...');

            const tests = [
                testPageLoading,
                testJavaScriptFiles,
                testCSSFiles,
                testLocalStorage,
                testTrialLicense,
                testLifetimeLicense,
                testLicenseValidation,
                testLicenseExpiry,
                testActivationForm,
                testAdminDashboard,
                testNotifications,
                testResponsiveDesign,
                testTamperProtection,
                testDataEncryption,
                testUnauthorizedAccess,
                testBackupSystem
            ];

            let currentTest = 0;

            function runNextTest() {
                if (currentTest < tests.length) {
                    setTimeout(() => {
                        tests[currentTest]();
                        currentTest++;
                        runNextTest();
                    }, 1000);
                } else {
                    logResult('تشغيل جميع الاختبارات', true,
                        'تم الانتهاء من جميع الاختبارات',
                        `تم تشغيل ${tests.length} اختبار`);
                }
            }

            runNextTest();
        }

        // إضافة زر تشغيل جميع الاختبارات
        document.addEventListener('DOMContentLoaded', function() {
            const runAllButton = document.createElement('button');
            runAllButton.textContent = '🚀 تشغيل جميع الاختبارات';
            runAllButton.className = 'test-button';
            runAllButton.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
                font-size: 16px;
                padding: 15px 25px;
                width: auto;
            `;
            runAllButton.onclick = runAllTests;
            document.body.appendChild(runAllButton);
        });
    </script>
</body>
</html>
