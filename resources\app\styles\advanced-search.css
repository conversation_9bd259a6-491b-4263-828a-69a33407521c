/**
 * تنسيقات نظام البحث والفلترة المتقدم
 * Advanced Search and Filter System Styles
 */

/* الحاوي الرئيسي للبحث */
.advanced-search-container {
    background: var(--card-background, #ffffff);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 24px;
    margin: 20px 0;
    border: 1px solid var(--border-color, #e0e0e0);
}

/* رأس البحث */
.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid var(--primary-color, #2196F3);
}

.search-title {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color, #2196F3);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-title i {
    font-size: 28px;
}

/* شريط البحث الرئيسي */
.main-search-bar {
    position: relative;
    margin-bottom: 20px;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: 16px 50px 16px 20px;
    border: 2px solid var(--border-color, #e0e0e0);
    border-radius: 25px;
    font-size: 16px;
    background: var(--input-background, #ffffff);
    color: var(--text-color, #333333);
    transition: all 0.3s ease;
    direction: rtl;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color, #2196F3);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.search-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color, #2196F3);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.search-button:hover {
    background: var(--primary-dark, #1976D2);
    transform: translateY(-50%) scale(1.05);
}

/* أزرار البحث السريع */
.quick-search-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.quick-search-btn {
    padding: 8px 16px;
    background: var(--secondary-color, #f5f5f5);
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: var(--text-color, #333333);
}

.quick-search-btn:hover,
.quick-search-btn.active {
    background: var(--primary-color, #2196F3);
    color: white;
    border-color: var(--primary-color, #2196F3);
}

/* منطقة الفلاتر المتقدمة */
.advanced-filters {
    background: var(--secondary-background, #f8f9fa);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color, #e0e0e0);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.filters-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color, #333333);
    margin: 0;
}

.toggle-filters-btn {
    background: none;
    border: none;
    color: var(--primary-color, #2196F3);
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* شبكة الفلاتر */
.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-weight: 500;
    color: var(--text-color, #333333);
    font-size: 14px;
}

.filter-input,
.filter-select {
    padding: 10px 12px;
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 6px;
    background: var(--input-background, #ffffff);
    color: var(--text-color, #333333);
    font-size: 14px;
    transition: border-color 0.3s ease;
    direction: rtl;
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-color, #2196F3);
}

/* أزرار الفلاتر */
.filters-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 16px;
}

.filter-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-btn.primary {
    background: var(--primary-color, #2196F3);
    color: white;
}

.filter-btn.secondary {
    background: var(--secondary-color, #f5f5f5);
    color: var(--text-color, #333333);
    border: 1px solid var(--border-color, #e0e0e0);
}

.filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* نتائج البحث */
.search-results {
    margin-top: 24px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: var(--secondary-background, #f8f9fa);
    border-radius: 8px;
}

.results-count {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color, #333333);
}

.results-actions {
    display: flex;
    gap: 10px;
}

.results-btn {
    padding: 8px 16px;
    background: var(--primary-color, #2196F3);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.results-btn:hover {
    background: var(--primary-dark, #1976D2);
}

/* جداول النتائج */
.results-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-background, #ffffff);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.results-table th,
.results-table td {
    padding: 12px 16px;
    text-align: right;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.results-table th {
    background: var(--primary-color, #2196F3);
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.results-table tr:hover {
    background: var(--hover-background, #f5f5f5);
}

.results-table td {
    font-size: 14px;
    color: var(--text-color, #333333);
}

/* تبويبات النتائج */
.results-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-color, #e0e0e0);
    margin-bottom: 20px;
}

.results-tab {
    padding: 12px 24px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color, #666666);
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.results-tab.active {
    color: var(--primary-color, #2196F3);
    border-bottom-color: var(--primary-color, #2196F3);
}

.results-tab:hover {
    color: var(--primary-color, #2196F3);
}

/* تاريخ البحث */
.search-history {
    background: var(--card-background, #ffffff);
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    border: 1px solid var(--border-color, #e0e0e0);
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    cursor: pointer;
    transition: background 0.3s ease;
}

.history-item:hover {
    background: var(--hover-background, #f5f5f5);
}

.history-item:last-child {
    border-bottom: none;
}

.history-query {
    font-weight: 500;
    color: var(--text-color, #333333);
}

.history-meta {
    font-size: 12px;
    color: var(--text-secondary, #666666);
    display: flex;
    gap: 10px;
}

/* الفلاتر المحفوظة */
.saved-filters {
    background: var(--card-background, #ffffff);
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    border: 1px solid var(--border-color, #e0e0e0);
}

.saved-filter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 6px;
    margin-bottom: 10px;
    background: var(--secondary-background, #f8f9fa);
}

.saved-filter-name {
    font-weight: 500;
    color: var(--text-color, #333333);
}

.saved-filter-actions {
    display: flex;
    gap: 8px;
}

.saved-filter-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.saved-filter-btn.load {
    background: var(--success-color, #4CAF50);
    color: white;
}

.saved-filter-btn.delete {
    background: var(--danger-color, #f44336);
    color: white;
}

/* التحميل والرسائل */
.search-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: var(--text-secondary, #666666);
}

.search-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color, #e0e0e0);
    border-top: 3px solid var(--primary-color, #2196F3);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.search-message {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary, #666666);
    font-size: 16px;
}

.search-message.empty {
    color: var(--warning-color, #ff9800);
}

.search-message.error {
    color: var(--danger-color, #f44336);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .advanced-search-container {
        padding: 16px;
        margin: 10px 0;
    }
    
    .search-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-search-buttons {
        justify-content: center;
    }
    
    .results-header {
        flex-direction: column;
        gap: 12px;
    }
    
    .results-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .results-table {
        font-size: 12px;
    }
    
    .results-table th,
    .results-table td {
        padding: 8px 12px;
    }
}

@media (max-width: 480px) {
    .search-input {
        padding: 12px 40px 12px 16px;
        font-size: 14px;
    }
    
    .search-button {
        width: 32px;
        height: 32px;
        right: 6px;
    }
    
    .filter-btn,
    .results-btn {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --card-background: #2d2d2d;
        --input-background: #3d3d3d;
        --secondary-background: #3d3d3d;
        --text-color: #ffffff;
        --text-secondary: #cccccc;
        --border-color: #555555;
        --hover-background: #404040;
    }
}
