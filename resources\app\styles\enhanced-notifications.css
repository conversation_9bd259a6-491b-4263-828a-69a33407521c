/**
 * أنماط نظام الإشعارات التفاعلية المحسن
 * Enhanced Interactive Notifications Styles
 */

/* حاوي الإشعارات الرئيسي */
.enhanced-notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    pointer-events: none;
}

/* الإشعار الفردي */
.enhanced-notification {
    background: var(--card-bg, #ffffff);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    margin-bottom: 12px;
    overflow: hidden;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: auto;
    border-left: 4px solid var(--primary-color, #3b82f6);
    backdrop-filter: blur(10px);
    position: relative;
}

.enhanced-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.enhanced-notification.hide {
    transform: translateX(100%);
    opacity: 0;
    margin-bottom: 0;
    max-height: 0;
}

/* أنواع الإشعارات */
.enhanced-notification.success {
    border-left-color: #10b981;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
}

.enhanced-notification.error {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%);
}

.enhanced-notification.warning {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
}

.enhanced-notification.info {
    border-left-color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
}

.enhanced-notification.appointment {
    border-left-color: #8b5cf6;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(139, 92, 246, 0.02) 100%);
}

/* أولوية الإشعارات */
.enhanced-notification.priority-high {
    animation: pulse 2s infinite;
    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.2);
}

.enhanced-notification.priority-urgent {
    animation: shake 0.5s infinite;
    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
}

/* رأس الإشعار */
.notification-header {
    display: flex;
    align-items: center;
    padding: 16px 20px 12px;
    gap: 12px;
}

.notification-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.notification-title {
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary, #1f2937);
    flex: 1;
}

.notification-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.persistent-indicator {
    font-size: 14px;
    opacity: 0.7;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-secondary, #6b7280);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-primary, #1f2937);
}

/* محتوى الإشعار */
.notification-content {
    padding: 0 20px 16px;
}

.notification-message {
    color: var(--text-secondary, #6b7280);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
}

/* قسم البيانات المرتبطة */
.related-data-section {
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.related-data-title {
    font-weight: 600;
    font-size: 13px;
    color: var(--text-primary, #1f2937);
    margin-bottom: 8px;
}

.related-data-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.data-item {
    display: flex;
    font-size: 12px;
}

.data-key {
    font-weight: 500;
    color: var(--text-secondary, #6b7280);
    min-width: 60px;
}

.data-value {
    color: var(--text-primary, #1f2937);
    flex: 1;
}

/* أزرار الإجراءات */
.notification-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn.primary {
    background: var(--primary-color, #3b82f6);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-dark, #2563eb);
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: var(--gray-100, #f3f4f6);
    color: var(--text-primary, #1f2937);
    border: 1px solid var(--gray-200, #e5e7eb);
}

.action-btn.secondary:hover {
    background: var(--gray-200, #e5e7eb);
    transform: translateY(-1px);
}

/* قسم التنقل */
.navigation-section {
    margin-top: 12px;
}

.navigate-btn {
    width: 100%;
    padding: 10px 12px;
    background: linear-gradient(135deg, var(--primary-color, #3b82f6) 0%, var(--primary-dark, #2563eb) 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.navigate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* شريط التقدم */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color, #3b82f6), var(--primary-light, #60a5fa));
    width: 100%;
    transform: translateX(-100%);
}

.progress-bar.animate {
    animation: progress linear;
    animation-fill-mode: forwards;
}

/* الرسوم المتحركة */
@keyframes progress {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-2px);
    }
    75% {
        transform: translateX(2px);
    }
}

/* تمييز الصفوف */
.highlight-row {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, transparent 100%);
    animation: highlightFade 3s ease-out;
}

@keyframes highlightFade {
    0% {
        background: rgba(59, 130, 246, 0.2);
    }
    100% {
        background: transparent;
    }
}

/* تمييز الأخطاء */
.error-highlight {
    border: 2px solid #ef4444 !important;
    background: rgba(239, 68, 68, 0.05) !important;
    animation: errorPulse 1s ease-in-out 3;
}

@keyframes errorPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
    }
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .enhanced-notification {
        background: var(--dark-card-bg, #1f2937);
        color: var(--dark-text-primary, #f9fafb);
    }
    
    .notification-title {
        color: var(--dark-text-primary, #f9fafb);
    }
    
    .notification-message {
        color: var(--dark-text-secondary, #d1d5db);
    }
    
    .related-data-section {
        background: rgba(255, 255, 255, 0.05);
    }
    
    .data-key {
        color: var(--dark-text-secondary, #d1d5db);
    }
    
    .data-value {
        color: var(--dark-text-primary, #f9fafb);
    }
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .enhanced-notifications-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .enhanced-notification {
        margin-bottom: 8px;
    }
    
    .notification-header {
        padding: 12px 16px 8px;
    }
    
    .notification-content {
        padding: 0 16px 12px;
    }
    
    .notification-actions {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
        justify-content: center;
    }
}
