<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الجديد</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            color: #2d3748;
        }

        .test-section {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            border-right: 4px solid #4299e1;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .test-btn {
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-decoration: none;
            color: white;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #4299e1; }
        .btn-success { background: #48bb78; }
        .btn-warning { background: #ed8936; }
        .btn-info { background: #667eea; }

        .log-section {
            background: #1a202c;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 2rem;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success { background: #48bb78; }
        .status-error { background: #f56565; }
        .status-warning { background: #ed8936; }
        .status-info { background: #4299e1; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e2e8f0;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #718096;
            font-weight: 500;
        }

        .iframe-container {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> اختبار النظام الجديد</h1>
            <p>اختبار شامل لنظام إدارة التراخيص الجديد</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div id="totalRequests" class="stat-number" style="color: #4299e1;">0</div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>
            <div class="stat-card">
                <div id="pendingRequests" class="stat-number" style="color: #ed8936;">0</div>
                <div class="stat-label">طلبات معلقة</div>
            </div>
            <div class="stat-card">
                <div id="approvedRequests" class="stat-number" style="color: #48bb78;">0</div>
                <div class="stat-label">طلبات موافق عليها</div>
            </div>
            <div class="stat-card">
                <div id="activeLicenses" class="stat-number" style="color: #667eea;">0</div>
                <div class="stat-label">تراخيص مفعلة</div>
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="test-section">
            <h3><i class="fas fa-play"></i> اختبار النظام</h3>
            <div class="test-buttons">
                <a href="resources/app/src/new-login.html" target="_blank" class="test-btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> فتح واجهة تسجيل الدخول
                </a>
                <a href="resources/app/src/license-management/new-dashboard.html" target="_blank" class="test-btn btn-success">
                    <i class="fas fa-tachometer-alt"></i> فتح لوحة التراخيص
                </a>
                <button class="test-btn btn-info" onclick="sendTestRequest()">
                    <i class="fas fa-paper-plane"></i> إرسال طلب تفعيل تجريبي
                </button>
                <button class="test-btn btn-info" onclick="createSampleLicenses()">
                    <i class="fas fa-key"></i> إنشاء تراخيص تجريبية
                </button>
                <button class="test-btn btn-warning" onclick="clearAllData()">
                    <i class="fas fa-trash"></i> مسح جميع البيانات
                </button>
            </div>
        </div>

        <!-- اختبار إرسال طلبات متعددة -->
        <div class="test-section">
            <h3><i class="fas fa-layer-group"></i> اختبار الطلبات المتعددة</h3>
            <div class="test-buttons">
                <button class="test-btn btn-success" onclick="sendMultipleRequests(3)">
                    <i class="fas fa-plus"></i> إرسال 3 طلبات
                </button>
                <button class="test-btn btn-success" onclick="sendMultipleRequests(5)">
                    <i class="fas fa-plus-circle"></i> إرسال 5 طلبات
                </button>
                <button class="test-btn btn-info" onclick="checkDataStatus()">
                    <i class="fas fa-search"></i> فحص حالة البيانات
                </button>
                <button class="test-btn btn-primary" onclick="updateStats()">
                    <i class="fas fa-sync-alt"></i> تحديث الإحصائيات
                </button>
            </div>
        </div>

        <!-- سجل الأحداث -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> سجل الأحداث</h3>
            <div id="logOutput" class="log-section">
                <div>🚀 نظام الاختبار جاهز...</div>
            </div>
        </div>

        <!-- واجهة تسجيل الدخول المدمجة -->
        <div class="test-section">
            <h3><i class="fas fa-sign-in-alt"></i> واجهة تسجيل الدخول</h3>
            <div class="iframe-container">
                <iframe src="resources/app/src/new-login.html"></iframe>
            </div>
        </div>
    </div>

    <script>
        let requestCounter = 1;

        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const statusClass = `status-${type}`;
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `
                <span class="status-indicator ${statusClass}"></span>
                [${timestamp}] ${message}
            `;
            
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }

        function generateRandomRequest() {
            const names = ['أحمد محمد علي', 'فاطمة حسن أحمد', 'محمد عبدالله حسن', 'عائشة سالم محمد', 'علي محمود أحمد', 'خديجة عبدالرحمن'];
            const wilayas = ['الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'باتنة', 'سطيف', 'سيدي بلعباس', 'بسكرة', 'تلمسان', 'ورقلة'];
            const municipalities = ['المدينة المركزية', 'الحي الشعبي', 'المنطقة الصناعية', 'الضاحية الجنوبية', 'المنطقة التجارية'];
            const companies = ['شركة الوقود المتقدم', 'مؤسسة الطاقة الجزائرية', 'شركة البترول الوطنية', 'مؤسسة الغاز الطبيعي', ''];

            return {
                id: Date.now() + Math.random(),
                fullName: names[Math.floor(Math.random() * names.length)],
                phone: '0' + Math.floor(Math.random() * ********* + *********),
                wilaya: wilayas[Math.floor(Math.random() * wilayas.length)],
                municipality: municipalities[Math.floor(Math.random() * municipalities.length)],
                companyName: companies[Math.floor(Math.random() * companies.length)],
                status: 'pending',
                submittedAt: new Date().toISOString(),
                requestNumber: `REQ-${Date.now()}-${requestCounter++}`
            };
        }

        function sendTestRequest() {
            const request = generateRandomRequest();
            
            try {
                let requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                requests.push(request);
                localStorage.setItem('activationRequests', JSON.stringify(requests));
                
                log(`✅ تم إرسال طلب من: ${request.fullName} (${request.phone})`, 'success');
                log(`📍 الموقع: ${request.wilaya} - ${request.municipality}`, 'info');
                
                if (request.companyName) {
                    log(`🏢 المؤسسة: ${request.companyName}`, 'info');
                }
                
                updateStats();
                
            } catch (error) {
                log(`❌ خطأ في إرسال الطلب: ${error.message}`, 'error');
            }
        }

        function sendMultipleRequests(count) {
            log(`📤 إرسال ${count} طلبات تفعيل...`, 'info');
            
            for (let i = 0; i < count; i++) {
                setTimeout(() => {
                    sendTestRequest();
                    if (i === count - 1) {
                        log(`✅ تم إرسال جميع الطلبات (${count} طلب)`, 'success');
                    }
                }, i * 500);
            }
        }

        function updateStats() {
            try {
                const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                const licenses = JSON.parse(localStorage.getItem('activeLicenses') || '[]');
                
                const totalRequests = requests.length;
                const pendingRequests = requests.filter(r => r.status === 'pending').length;
                const approvedRequests = requests.filter(r => r.status === 'approved').length;
                const activeLicenses = licenses.length;

                document.getElementById('totalRequests').textContent = totalRequests;
                document.getElementById('pendingRequests').textContent = pendingRequests;
                document.getElementById('approvedRequests').textContent = approvedRequests;
                document.getElementById('activeLicenses').textContent = activeLicenses;
                
                log(`📊 الإحصائيات: ${totalRequests} طلب، ${pendingRequests} معلق، ${approvedRequests} موافق عليه، ${activeLicenses} ترخيص مفعل`, 'info');
                
            } catch (error) {
                log(`❌ خطأ في تحديث الإحصائيات: ${error.message}`, 'error');
            }
        }

        function checkDataStatus() {
            try {
                const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                const licenses = JSON.parse(localStorage.getItem('activeLicenses') || '[]');
                
                log(`📋 فحص البيانات:`, 'info');
                log(`   - طلبات التفعيل: ${requests.length}`, 'info');
                log(`   - التراخيص المفعلة: ${licenses.length}`, 'info');
                
                if (requests.length > 0) {
                    const latestRequest = requests[requests.length - 1];
                    log(`   - آخر طلب: ${latestRequest.fullName} (${latestRequest.requestNumber})`, 'info');
                }
                
                // فحص حجم البيانات
                const requestsSize = JSON.stringify(requests).length;
                const licensesSize = JSON.stringify(licenses).length;
                log(`   - حجم بيانات الطلبات: ${requestsSize} حرف`, 'info');
                log(`   - حجم بيانات التراخيص: ${licensesSize} حرف`, 'info');
                
            } catch (error) {
                log(`❌ خطأ في فحص البيانات: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟\n\nسيتم حذف:\n- جميع طلبات التفعيل\n- جميع التراخيص المفعلة')) {
                localStorage.removeItem('activationRequests');
                localStorage.removeItem('activeLicenses');

                log('🗑️ تم مسح جميع البيانات', 'warning');
                updateStats();
            }
        }

        function createSampleLicenses() {
            const sampleLicenses = [
                {
                    id: Date.now() + 1,
                    clientName: 'أحمد محمد علي',
                    clientPhone: '**********',
                    licenseKey: 'LIC-DEMO-ADMIN-001',
                    type: 'lifetime',
                    activatedAt: new Date().toISOString(),
                    expiresAt: null,
                    notes: 'ترخيص تجريبي للمطور',
                    createdBy: 'system'
                },
                {
                    id: Date.now() + 2,
                    clientName: 'فاطمة حسن أحمد',
                    clientPhone: '**********',
                    licenseKey: 'LIC-DEMO-USER-002',
                    type: 'yearly',
                    activatedAt: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                    notes: 'ترخيص سنوي تجريبي',
                    createdBy: 'system'
                },
                {
                    id: Date.now() + 3,
                    clientName: 'محمد عبدالله حسن',
                    clientPhone: '**********',
                    licenseKey: 'LIC-DEMO-TRIAL-003',
                    type: 'trial',
                    activatedAt: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    notes: 'ترخيص تجريبي 30 يوم',
                    createdBy: 'system'
                }
            ];

            localStorage.setItem('activeLicenses', JSON.stringify(sampleLicenses));
            log('✅ تم إنشاء تراخيص تجريبية', 'success');
            log('🔑 التراخيص المتاحة:', 'info');
            sampleLicenses.forEach(license => {
                log(`   - ${license.licenseKey} (${license.clientName})`, 'info');
            });
            updateStats();
        }

        // تحديث دوري للإحصائيات
        setInterval(() => {
            updateStats();
        }, 5000);

        // تحديث الإحصائيات عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            log('✅ تم تحميل نظام الاختبار بنجاح', 'success');
            log('📝 استخدم الأزرار أعلاه لاختبار النظام', 'info');
            log('🔑 بيانات الدخول: admin / admin123', 'info');
        });

        // مراقبة تغييرات localStorage
        window.addEventListener('storage', function(e) {
            if (e.key === 'activationRequests' || e.key === 'activeLicenses') {
                log('🔄 تم رصد تغيير في البيانات', 'info');
                updateStats();
            }
        });
    </script>
</body>
</html>
