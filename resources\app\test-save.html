<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظيفة الحفظ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار وظيفة الحفظ</h1>
        
        <form id="test-form">
            <div class="form-group">
                <label for="customer-name">اسم العميل:</label>
                <input type="text" id="customer-name" value="عميل تجريبي" required>
            </div>
            
            <div class="form-group">
                <label for="customer-phone">رقم الهاتف:</label>
                <input type="text" id="customer-phone" value="0123456789" required>
            </div>
            
            <div class="form-group">
                <label for="customer-address">العنوان:</label>
                <textarea id="customer-address">عنوان تجريبي</textarea>
            </div>
            
            <button type="submit">حفظ البيانات</button>
            <button type="button" id="load-data">تحميل البيانات</button>
            <button type="button" id="clear-log">مسح السجل</button>
        </form>
        
        <div id="message"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        // متغير البيانات التجريبي
        let testData = {
            customers: [],
            vehicles: [],
            gasTanks: [],
            gasCards: [],
            appointments: [],
            settings: {
                shopName: 'مؤسسة وقود المستقبل',
                reminderDays: 30
            }
        };

        // دالة إضافة رسالة إلى السجل
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // دالة إظهار الرسائل
        function showMessage(text, isSuccess = true) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = isSuccess ? 'success' : 'error';
            messageDiv.textContent = text;
            setTimeout(() => {
                messageDiv.textContent = '';
                messageDiv.className = '';
            }, 3000);
        }

        // دالة حفظ البيانات (محاكاة)
        async function saveTestData() {
            try {
                addLog('🔄 بدء عملية حفظ البيانات...');
                
                // محاكاة حفظ في Electron
                if (window.electronAPI) {
                    addLog('📱 استخدام Electron API للحفظ');
                    const result = window.electronAPI.saveData(testData);
                    if (result) {
                        addLog('✅ تم حفظ البيانات بنجاح في Electron', 'success');
                        return true;
                    } else {
                        addLog('❌ فشل الحفظ في Electron، محاولة استخدام localStorage', 'error');
                    }
                }
                
                // حفظ في localStorage
                addLog('💾 استخدام localStorage للحفظ');
                localStorage.setItem('testData', JSON.stringify(testData));
                addLog('✅ تم حفظ البيانات بنجاح في localStorage', 'success');
                return true;
                
            } catch (error) {
                addLog(`❌ خطأ في حفظ البيانات: ${error.message}`, 'error');
                return false;
            }
        }

        // دالة تحميل البيانات
        function loadTestData() {
            try {
                addLog('🔄 بدء عملية تحميل البيانات...');
                
                // محاولة تحميل من Electron
                if (window.electronAPI) {
                    addLog('📱 محاولة تحميل من Electron API');
                    const data = window.electronAPI.loadData();
                    if (data) {
                        testData = data;
                        addLog('✅ تم تحميل البيانات من Electron', 'success');
                        addLog(`📊 عدد العملاء: ${testData.customers?.length || 0}`);
                        return;
                    } else {
                        addLog('⚠️ لا توجد بيانات في Electron، محاولة localStorage');
                    }
                }
                
                // تحميل من localStorage
                const savedData = localStorage.getItem('testData');
                if (savedData) {
                    testData = JSON.parse(savedData);
                    addLog('✅ تم تحميل البيانات من localStorage', 'success');
                    addLog(`📊 عدد العملاء: ${testData.customers?.length || 0}`);
                } else {
                    addLog('⚠️ لا توجد بيانات محفوظة');
                }
                
            } catch (error) {
                addLog(`❌ خطأ في تحميل البيانات: ${error.message}`, 'error');
            }
        }

        // معالج النموذج
        document.getElementById('test-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('customer-name').value;
            const phone = document.getElementById('customer-phone').value;
            const address = document.getElementById('customer-address').value;
            
            // إضافة عميل جديد
            const newCustomer = {
                id: 'customer-' + Date.now(),
                name,
                phone,
                address,
                createdAt: new Date().toISOString()
            };
            
            testData.customers.push(newCustomer);
            addLog(`➕ تم إضافة عميل جديد: ${name}`);
            
            // حفظ البيانات
            const saveResult = await saveTestData();
            if (saveResult) {
                showMessage('تم حفظ البيانات بنجاح!', true);
                // مسح النموذج
                document.getElementById('test-form').reset();
            } else {
                showMessage('فشل في حفظ البيانات!', false);
            }
        });

        // زر تحميل البيانات
        document.getElementById('load-data').addEventListener('click', () => {
            loadTestData();
            showMessage('تم تحميل البيانات');
        });

        // زر مسح السجل
        document.getElementById('clear-log').addEventListener('click', () => {
            document.getElementById('log').innerHTML = '';
        });

        // تحميل البيانات عند بدء الصفحة
        window.addEventListener('load', () => {
            addLog('🚀 تم تحميل صفحة الاختبار');
            loadTestData();
        });
    </script>
</body>
</html>
