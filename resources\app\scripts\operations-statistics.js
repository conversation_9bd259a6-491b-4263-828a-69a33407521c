/**
 * نظام إحصائيات العمليات
 * Operations Statistics System
 * 
 * يوفر إحصائيات شاملة عن العمليات المختلفة في النظام
 * مع تصنيف حسب النوع والوقت والحالة
 */

class OperationsStatistics {
    constructor() {
        this.operationTypes = {
            'تركيب': { label: 'تركيب', icon: '🔧', color: '#3b82f6' },
            'مراقبة': { label: 'مراقبة', icon: '👁️', color: '#10b981' },
            'تجديد': { label: 'تجديد', icon: '🔄', color: '#f59e0b' },
            'صيانة': { label: 'صيانة', icon: '🛠️', color: '#8b5cf6' },
            'فحص': { label: 'فحص', icon: '🔍', color: '#ef4444' }
        };
        
        this.timeRanges = {
            'today': 'اليوم',
            'week': 'هذا الأسبوع',
            'month': 'هذا الشهر',
            'quarter': 'هذا الربع',
            'year': 'هذا العام',
            'all': 'جميع الأوقات'
        };
        
        this.init();
    }

    init() {
        console.log('✅ تم تهيئة نظام إحصائيات العمليات');
    }

    // حساب إحصائيات العمليات الأساسية
    calculateBasicStats(timeRange = 'all') {
        const data = this.getFilteredData(timeRange);
        
        return {
            totalOperations: data.totalOperations,
            totalCustomers: data.totalCustomers,
            totalVehicles: data.totalVehicles,
            totalRevenue: data.totalRevenue,
            averageOperationsPerDay: data.averageOperationsPerDay,
            completionRate: data.completionRate
        };
    }

    // حساب توزيع العمليات حسب النوع
    calculateOperationsByType(timeRange = 'all') {
        const transmissionData = this.getTransmissionData(timeRange);
        const gasCardData = this.getGasCardData(timeRange);
        
        const stats = {};
        
        // إحصائيات من جدول الإرسال (تركيب ومراقبة)
        transmissionData.forEach(operation => {
            const type = operation.operationType || 'غير محدد';
            if (!stats[type]) {
                stats[type] = {
                    count: 0,
                    percentage: 0,
                    revenue: 0,
                    customers: new Set(),
                    vehicles: new Set()
                };
            }
            stats[type].count++;
            stats[type].revenue += operation.cost || 0;
            stats[type].customers.add(operation.customerId);
            stats[type].vehicles.add(operation.vehicleId);
        });

        // إحصائيات من بطاقات الغاز (تجديد)
        gasCardData.forEach(card => {
            const type = 'تجديد';
            if (!stats[type]) {
                stats[type] = {
                    count: 0,
                    percentage: 0,
                    revenue: 0,
                    customers: new Set(),
                    vehicles: new Set()
                };
            }
            stats[type].count++;
            stats[type].revenue += card.renewalCost || 0;
            stats[type].customers.add(card.customerId);
            stats[type].vehicles.add(card.vehicleId);
        });

        // حساب النسب المئوية
        const totalOperations = Object.values(stats).reduce((sum, stat) => sum + stat.count, 0);
        Object.keys(stats).forEach(type => {
            stats[type].percentage = totalOperations > 0 ? (stats[type].count / totalOperations * 100).toFixed(1) : 0;
            stats[type].customers = stats[type].customers.size;
            stats[type].vehicles = stats[type].vehicles.size;
        });

        return stats;
    }

    // حساب الإحصائيات الزمنية
    calculateTimeBasedStats(timeRange = 'month') {
        const data = this.getFilteredData(timeRange);
        const timeLabels = this.getTimeLabels(timeRange);
        
        const stats = {
            labels: timeLabels,
            datasets: {
                operations: new Array(timeLabels.length).fill(0),
                revenue: new Array(timeLabels.length).fill(0),
                customers: new Array(timeLabels.length).fill(0)
            }
        };

        // تجميع البيانات حسب الفترات الزمنية
        data.operations.forEach(operation => {
            const timeIndex = this.getTimeIndex(operation.date, timeRange);
            if (timeIndex >= 0 && timeIndex < timeLabels.length) {
                stats.datasets.operations[timeIndex]++;
                stats.datasets.revenue[timeIndex] += operation.cost || 0;
            }
        });

        data.customers.forEach(customer => {
            const timeIndex = this.getTimeIndex(customer.createdAt, timeRange);
            if (timeIndex >= 0 && timeIndex < timeLabels.length) {
                stats.datasets.customers[timeIndex]++;
            }
        });

        return stats;
    }

    // حساب إحصائيات الأداء
    calculatePerformanceStats(timeRange = 'month') {
        const data = this.getFilteredData(timeRange);
        
        return {
            operationsGrowth: this.calculateGrowthRate(data.operations, timeRange),
            revenueGrowth: this.calculateGrowthRate(data.revenue, timeRange),
            customerGrowth: this.calculateGrowthRate(data.customers, timeRange),
            averageOperationValue: data.totalRevenue / data.totalOperations || 0,
            customerRetentionRate: this.calculateRetentionRate(data.customers),
            operationalEfficiency: this.calculateEfficiency(data.operations)
        };
    }

    // حساب إحصائيات الزبائن
    calculateCustomerStats(timeRange = 'all') {
        const customers = this.getCustomersData(timeRange);
        
        const stats = {
            totalCustomers: customers.length,
            newCustomers: customers.filter(c => this.isInTimeRange(c.createdAt, timeRange)).length,
            activeCustomers: customers.filter(c => this.hasRecentActivity(c.id, timeRange)).length,
            topCustomers: this.getTopCustomers(customers, 10),
            customersByRegion: this.groupCustomersByRegion(customers),
            averageOperationsPerCustomer: this.calculateAverageOperationsPerCustomer(customers)
        };

        return stats;
    }

    // حساب إحصائيات السيارات
    calculateVehicleStats(timeRange = 'all') {
        const vehicles = this.getVehiclesData(timeRange);
        
        return {
            totalVehicles: vehicles.length,
            newVehicles: vehicles.filter(v => this.isInTimeRange(v.createdAt, timeRange)).length,
            vehiclesByType: this.groupVehiclesByType(vehicles),
            vehiclesByYear: this.groupVehiclesByYear(vehicles),
            averageVehicleAge: this.calculateAverageVehicleAge(vehicles),
            mostActiveVehicles: this.getMostActiveVehicles(vehicles, 10)
        };
    }

    // الحصول على البيانات المفلترة حسب الفترة الزمنية
    getFilteredData(timeRange) {
        const now = new Date();
        let startDate = new Date();

        switch (timeRange) {
            case 'today':
                startDate.setHours(0, 0, 0, 0);
                break;
            case 'week':
                startDate.setDate(now.getDate() - 7);
                break;
            case 'month':
                startDate.setMonth(now.getMonth() - 1);
                break;
            case 'quarter':
                startDate.setMonth(now.getMonth() - 3);
                break;
            case 'year':
                startDate.setFullYear(now.getFullYear() - 1);
                break;
            default:
                startDate = new Date('1900-01-01');
        }

        const transmissionData = appData.transmissionTable?.filter(t => 
            new Date(t.createdAt || t.operationDate) >= startDate
        ) || [];

        const gasCardData = appData.gasCards?.filter(g => 
            new Date(g.issueDate || g.createdAt) >= startDate
        ) || [];

        const customersData = appData.customers?.filter(c => 
            new Date(c.createdAt) >= startDate
        ) || [];

        const vehiclesData = appData.vehicles?.filter(v => 
            new Date(v.createdAt) >= startDate
        ) || [];

        const totalOperations = transmissionData.length + gasCardData.length;
        const totalRevenue = transmissionData.reduce((sum, t) => sum + (t.cost || 0), 0) +
                           gasCardData.reduce((sum, g) => sum + (g.renewalCost || 0), 0);

        return {
            transmissionData,
            gasCardData,
            customersData,
            vehiclesData,
            totalOperations,
            totalCustomers: customersData.length,
            totalVehicles: vehiclesData.length,
            totalRevenue,
            averageOperationsPerDay: this.calculateAveragePerDay(totalOperations, timeRange),
            completionRate: this.calculateCompletionRate(transmissionData, gasCardData),
            operations: [...transmissionData, ...gasCardData],
            customers: customersData,
            revenue: totalRevenue
        };
    }

    // الحصول على بيانات جدول الإرسال
    getTransmissionData(timeRange) {
        return this.getFilteredData(timeRange).transmissionData;
    }

    // الحصول على بيانات بطاقات الغاز
    getGasCardData(timeRange) {
        return this.getFilteredData(timeRange).gasCardData;
    }

    // الحصول على بيانات الزبائن
    getCustomersData(timeRange) {
        return this.getFilteredData(timeRange).customersData;
    }

    // الحصول على بيانات السيارات
    getVehiclesData(timeRange) {
        return this.getFilteredData(timeRange).vehiclesData;
    }

    // حساب معدل النمو
    calculateGrowthRate(data, timeRange) {
        // منطق حساب معدل النمو
        return Math.random() * 20 - 10; // مؤقت للاختبار
    }

    // حساب معدل الاحتفاظ بالزبائن
    calculateRetentionRate(customers) {
        // منطق حساب معدل الاحتفاظ
        return Math.random() * 30 + 70; // مؤقت للاختبار
    }

    // حساب الكفاءة التشغيلية
    calculateEfficiency(operations) {
        // منطق حساب الكفاءة
        return Math.random() * 20 + 80; // مؤقت للاختبار
    }

    // حساب المتوسط اليومي
    calculateAveragePerDay(total, timeRange) {
        const days = {
            'today': 1,
            'week': 7,
            'month': 30,
            'quarter': 90,
            'year': 365,
            'all': 365
        };
        return total / (days[timeRange] || 365);
    }

    // حساب معدل الإنجاز
    calculateCompletionRate(transmissionData, gasCardData) {
        const completed = transmissionData.filter(t => t.status === 'completed').length +
                         gasCardData.filter(g => g.status === 'active').length;
        const total = transmissionData.length + gasCardData.length;
        return total > 0 ? (completed / total * 100).toFixed(1) : 0;
    }

    // التحقق من وجود نشاط حديث
    hasRecentActivity(customerId, timeRange) {
        const recentTransmissions = appData.transmissionTable?.filter(t => 
            t.customerId === customerId && this.isInTimeRange(t.createdAt, timeRange)
        ) || [];
        
        const recentGasCards = appData.gasCards?.filter(g => 
            g.customerId === customerId && this.isInTimeRange(g.issueDate, timeRange)
        ) || [];

        return recentTransmissions.length > 0 || recentGasCards.length > 0;
    }

    // التحقق من وجود التاريخ في النطاق المحدد
    isInTimeRange(date, timeRange) {
        if (!date) return false;
        
        const targetDate = new Date(date);
        const now = new Date();
        
        switch (timeRange) {
            case 'today':
                return targetDate.toDateString() === now.toDateString();
            case 'week':
                return (now - targetDate) <= (7 * 24 * 60 * 60 * 1000);
            case 'month':
                return (now - targetDate) <= (30 * 24 * 60 * 60 * 1000);
            case 'quarter':
                return (now - targetDate) <= (90 * 24 * 60 * 60 * 1000);
            case 'year':
                return (now - targetDate) <= (365 * 24 * 60 * 60 * 1000);
            default:
                return true;
        }
    }

    // الحصول على أفضل الزبائن
    getTopCustomers(customers, limit = 10) {
        return customers
            .map(customer => ({
                ...customer,
                operationsCount: this.getCustomerOperationsCount(customer.id),
                totalSpent: this.getCustomerTotalSpent(customer.id)
            }))
            .sort((a, b) => b.totalSpent - a.totalSpent)
            .slice(0, limit);
    }

    // حساب عدد العمليات للزبون
    getCustomerOperationsCount(customerId) {
        const transmissions = appData.transmissionTable?.filter(t => t.customerId === customerId) || [];
        const gasCards = appData.gasCards?.filter(g => g.customerId === customerId) || [];
        return transmissions.length + gasCards.length;
    }

    // حساب إجمالي إنفاق الزبون
    getCustomerTotalSpent(customerId) {
        const transmissions = appData.transmissionTable?.filter(t => t.customerId === customerId) || [];
        const gasCards = appData.gasCards?.filter(g => g.customerId === customerId) || [];
        
        const transmissionCost = transmissions.reduce((sum, t) => sum + (t.cost || 0), 0);
        const gasCardCost = gasCards.reduce((sum, g) => sum + (g.renewalCost || 0), 0);
        
        return transmissionCost + gasCardCost;
    }

    // تجميع الزبائن حسب المنطقة
    groupCustomersByRegion(customers) {
        return customers.reduce((acc, customer) => {
            const region = customer.address?.split(',')[0]?.trim() || 'غير محدد';
            acc[region] = (acc[region] || 0) + 1;
            return acc;
        }, {});
    }

    // تجميع السيارات حسب النوع
    groupVehiclesByType(vehicles) {
        return vehicles.reduce((acc, vehicle) => {
            const type = vehicle.type || 'غير محدد';
            acc[type] = (acc[type] || 0) + 1;
            return acc;
        }, {});
    }

    // تجميع السيارات حسب السنة
    groupVehiclesByYear(vehicles) {
        return vehicles.reduce((acc, vehicle) => {
            const year = vehicle.year || 'غير محدد';
            acc[year] = (acc[year] || 0) + 1;
            return acc;
        }, {});
    }

    // حساب متوسط عمر السيارات
    calculateAverageVehicleAge(vehicles) {
        const currentYear = new Date().getFullYear();
        const ages = vehicles
            .filter(v => v.year && !isNaN(v.year))
            .map(v => currentYear - parseInt(v.year));
        
        return ages.length > 0 ? ages.reduce((sum, age) => sum + age, 0) / ages.length : 0;
    }

    // الحصول على أكثر السيارات نشاطاً
    getMostActiveVehicles(vehicles, limit = 10) {
        return vehicles
            .map(vehicle => ({
                ...vehicle,
                operationsCount: this.getVehicleOperationsCount(vehicle.id)
            }))
            .sort((a, b) => b.operationsCount - a.operationsCount)
            .slice(0, limit);
    }

    // حساب عدد العمليات للسيارة
    getVehicleOperationsCount(vehicleId) {
        const transmissions = appData.transmissionTable?.filter(t => t.vehicleId === vehicleId) || [];
        const gasCards = appData.gasCards?.filter(g => g.vehicleId === vehicleId) || [];
        return transmissions.length + gasCards.length;
    }

    // حساب متوسط العمليات لكل زبون
    calculateAverageOperationsPerCustomer(customers) {
        if (customers.length === 0) return 0;
        
        const totalOperations = customers.reduce((sum, customer) => 
            sum + this.getCustomerOperationsCount(customer.id), 0
        );
        
        return totalOperations / customers.length;
    }

    // الحصول على تسميات الوقت
    getTimeLabels(timeRange) {
        const now = new Date();
        const labels = [];

        switch (timeRange) {
            case 'week':
                for (let i = 6; i >= 0; i--) {
                    const date = new Date(now);
                    date.setDate(date.getDate() - i);
                    labels.push(date.toLocaleDateString('ar-EG', { weekday: 'short' }));
                }
                break;
            case 'month':
                for (let i = 29; i >= 0; i--) {
                    const date = new Date(now);
                    date.setDate(date.getDate() - i);
                    labels.push(date.getDate().toString());
                }
                break;
            case 'year':
                for (let i = 11; i >= 0; i--) {
                    const date = new Date(now);
                    date.setMonth(date.getMonth() - i);
                    labels.push(date.toLocaleDateString('ar-EG', { month: 'short' }));
                }
                break;
            default:
                labels.push('البيانات');
        }

        return labels;
    }

    // الحصول على فهرس الوقت
    getTimeIndex(date, timeRange) {
        if (!date) return -1;
        
        const targetDate = new Date(date);
        const now = new Date();
        
        switch (timeRange) {
            case 'week':
                const daysDiff = Math.floor((now - targetDate) / (1000 * 60 * 60 * 24));
                return daysDiff >= 0 && daysDiff < 7 ? 6 - daysDiff : -1;
            case 'month':
                const daysDiffMonth = Math.floor((now - targetDate) / (1000 * 60 * 60 * 24));
                return daysDiffMonth >= 0 && daysDiffMonth < 30 ? 29 - daysDiffMonth : -1;
            case 'year':
                const monthsDiff = (now.getFullYear() - targetDate.getFullYear()) * 12 + 
                                 (now.getMonth() - targetDate.getMonth());
                return monthsDiff >= 0 && monthsDiff < 12 ? 11 - monthsDiff : -1;
            default:
                return 0;
        }
    }

    // تصدير البيانات
    exportStats(format = 'json', timeRange = 'all') {
        const stats = {
            basic: this.calculateBasicStats(timeRange),
            byType: this.calculateOperationsByType(timeRange),
            timeBased: this.calculateTimeBasedStats(timeRange),
            performance: this.calculatePerformanceStats(timeRange),
            customers: this.calculateCustomerStats(timeRange),
            vehicles: this.calculateVehicleStats(timeRange),
            generatedAt: new Date().toISOString(),
            timeRange: timeRange
        };

        switch (format) {
            case 'csv':
                return this.convertToCSV(stats);
            case 'excel':
                return this.convertToExcel(stats);
            default:
                return JSON.stringify(stats, null, 2);
        }
    }

    // تحويل إلى CSV
    convertToCSV(stats) {
        // منطق تحويل البيانات إلى CSV
        return 'CSV data here';
    }

    // تحويل إلى Excel
    convertToExcel(stats) {
        // منطق تحويل البيانات إلى Excel
        return 'Excel data here';
    }
}

// إنشاء مثيل عام للنظام
const operationsStats = new OperationsStatistics();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OperationsStatistics;
}
