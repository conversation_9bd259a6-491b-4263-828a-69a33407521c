/* تصميم لوحة تحكم إدارة التراخيص */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --dark-bg: #343a40;
    --sidebar-bg: #2c3e50;
    --card-bg: #ffffff;
    --border-color: #dee2e6;
    --text-dark: #212529;
    --text-light: #6c757d;
    --shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    --shadow-lg: 0 1rem 3rem rgba(0,0,0,0.175);
    --border-radius: 0.375rem;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--light-bg);
    direction: rtl;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--sidebar-bg);
    color: white;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    right: 0;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar .logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar .logo i {
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.sidebar .logo h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.nav-item:hover {
    background: rgba(255,255,255,0.1);
}

.nav-item.active {
    background: var(--secondary-color);
    border-right: 3px solid var(--warning-color);
}

.nav-item i {
    width: 1.25rem;
    margin-left: 0.75rem;
    font-size: 1rem;
}

.nav-item span {
    flex: 1;
}

.badge {
    background: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    min-width: 1.5rem;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-right: 280px;
    padding: 2rem;
    background: var(--light-bg);
}

/* Content Header */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.header-left h1 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.header-left p {
    color: var(--text-light);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
}

.user-info i {
    font-size: 1.5rem;
    color: var(--secondary-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn-primary {
    background: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--text-light);
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #219a52;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #138496;
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: var(--warning-color);
}

.stat-card:nth-child(2) .stat-icon {
    background: var(--success-color);
}

.stat-card:nth-child(3) .stat-icon {
    background: var(--info-color);
}

.stat-card:nth-child(4) .stat-icon {
    background: var(--primary-color);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--text-light);
    font-size: 0.875rem;
}

/* Recent Activity */
.recent-activity {
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.recent-activity h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
}

.activity-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.activity-content small {
    color: var(--text-light);
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    background: var(--card-bg);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.section-header h3 {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.section-actions {
    display: flex;
    gap: 0.75rem;
}

/* Requests and Licenses Lists */
.requests-container,
.licenses-container {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.request-item,
.license-item {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.request-item:hover,
.license-item:hover {
    background: var(--light-bg);
}

.request-item:last-child,
.license-item:last-child {
    border-bottom: none;
}

.item-info {
    flex: 1;
}

.item-info h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.item-info p {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.status-active {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.status-expired {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

/* Generate Form */
.generate-form-container {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.generate-form {
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Statistics */
.statistics-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.chart-container {
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.stats-details {
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.stats-table table {
    width: 100%;
    border-collapse: collapse;
}

.stats-table th,
.stats-table td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.stats-table th {
    background: var(--light-bg);
    font-weight: 600;
    color: var(--primary-color);
}

/* Settings */
.settings-container {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.setting-group {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.setting-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-group h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.setting-item label {
    font-weight: 500;
    color: var(--text-dark);
}

.setting-item input {
    max-width: 200px;
}

.setting-actions {
    margin-top: 1.5rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--primary-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* Loading and Notifications */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-spinner {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--card-bg);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 3001;
    animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification.success {
    border-right: 4px solid var(--success-color);
}

.notification.error {
    border-right: 4px solid var(--danger-color);
}

.notification.warning {
    border-right: 4px solid var(--warning-color);
}

.notification.info {
    border-right: 4px solid var(--info-color);
}

/* Request and License Cards */
.request-card, .license-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.request-card:hover, .license-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.request-header, .license-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.request-info h4, .license-info h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.request-info p, .license-info p {
    margin: 0.25rem 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.license-key {
    font-family: 'Courier New', monospace;
    background: var(--light-bg);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    border: 1px solid var(--border-color);
}

.status-badge, .type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-approved {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.type-lifetime {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.type-trial {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.request-details, .license-details {
    margin: 1rem 0;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.detail-row span:first-child {
    color: var(--text-muted);
    font-weight: 500;
}

.detail-row span:last-child {
    color: var(--text-color);
}

.lifetime-text {
    color: var(--success-color);
    font-weight: 600;
}

.expiring-soon {
    color: var(--warning-color);
    font-weight: 600;
}

.request-actions, .license-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 1rem 0 0.5rem 0;
    color: var(--text-color);
}

/* Modal Enhancements */
.request-details-full .detail-section {
    margin-bottom: 2rem;
}

.request-details-full .detail-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.detail-item span {
    color: var(--text-color);
    font-size: 1rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.close-btn:hover {
    color: var(--text-color);
    background: var(--light-bg);
}

/* Activity Items */
.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: var(--light-bg);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 0.25rem 0;
    color: var(--text-color);
    font-size: 0.9rem;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.no-activity {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        width: 250px;
    }

    .main-content {
        margin-right: 250px;
    }

    .statistics-container {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: var(--transition);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        padding: 1rem;
    }
    
    .content-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .item-info,
    .item-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
}
