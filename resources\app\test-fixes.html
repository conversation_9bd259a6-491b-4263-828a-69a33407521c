<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاحات - نظام توجيه العمليات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-good { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        #console-output {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار الإصلاحات - نظام توجيه العمليات</h1>
        
        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <div id="system-status">جاري التحقق...</div>
            <button onclick="checkSystemStatus()">🔍 فحص حالة النظام</button>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبارات سريعة</h3>
            <button onclick="testAppDataInit()">📋 اختبار تهيئة appData</button>
            <button onclick="testTransmissionManagerInit()">⚙️ اختبار تهيئة transmissionManager</button>
            <button onclick="testOperationRouting()">🔄 اختبار توجيه العمليات</button>
            <button onclick="clearConsole()">🧹 مسح وحدة التحكم</button>
        </div>
        
        <div class="test-section">
            <h3>📝 وحدة التحكم</h3>
            <div id="console-output"></div>
        </div>
    </div>

    <!-- تحميل الملفات بالترتيب الصحيح -->
    <script src="scripts/script.js"></script>
    <script src="scripts/transmission-manager.js"></script>
    
    <script>
        // إعادة توجيه console.log إلى العرض
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        function checkSystemStatus() {
            console.log('🔍 فحص حالة النظام...');
            
            const statusDiv = document.getElementById('system-status');
            let status = '';
            
            // فحص appData
            if (typeof appData !== 'undefined') {
                status += '<span class="status-good">✅ appData متاح</span><br>';
                status += `📊 عدد الزبائن: ${appData.customers?.length || 0}<br>`;
                
                if (Array.isArray(appData.transmissionTable)) {
                    status += '<span class="status-good">✅ appData.transmissionTable مُهيأ</span><br>';
                    status += `📋 عدد عمليات الإرسال: ${appData.transmissionTable.length}<br>`;
                } else {
                    status += '<span class="status-error">❌ appData.transmissionTable غير مُهيأ</span><br>';
                }
                
                if (Array.isArray(appData.gasCards)) {
                    status += '<span class="status-good">✅ appData.gasCards مُهيأ</span><br>';
                    status += `🎫 عدد بطاقات الغاز: ${appData.gasCards.length}<br>`;
                } else {
                    status += '<span class="status-error">❌ appData.gasCards غير مُهيأ</span><br>';
                }
            } else {
                status += '<span class="status-error">❌ appData غير متاح</span><br>';
            }
            
            // فحص transmissionManager
            if (typeof transmissionManager !== 'undefined' && transmissionManager !== null) {
                status += '<span class="status-good">✅ transmissionManager متاح</span><br>';
                status += `📊 عدد العمليات في transmissionManager: ${transmissionManager.transmissionData?.length || 0}<br>`;
            } else {
                status += '<span class="status-warning">⚠️ transmissionManager غير متاح</span><br>';
            }
            
            statusDiv.innerHTML = status;
        }
        
        function testAppDataInit() {
            console.log('🧪 اختبار تهيئة appData...');
            
            if (typeof appData === 'undefined') {
                console.error('❌ appData غير متاح');
                return;
            }
            
            console.log('✅ appData متاح');
            console.log('📋 الخصائص المتاحة:', Object.keys(appData));
            
            // فحص المصفوفات المطلوبة
            const requiredArrays = ['customers', 'gasCards', 'transmissionTable'];
            requiredArrays.forEach(arrayName => {
                if (Array.isArray(appData[arrayName])) {
                    console.log(`✅ ${arrayName}: مُهيأ بشكل صحيح (${appData[arrayName].length} عنصر)`);
                } else {
                    console.error(`❌ ${arrayName}: غير مُهيأ أو ليس مصفوفة`);
                }
            });
        }
        
        function testTransmissionManagerInit() {
            console.log('🧪 اختبار تهيئة transmissionManager...');
            
            if (typeof transmissionManager === 'undefined' || transmissionManager === null) {
                console.error('❌ transmissionManager غير متاح');
                console.log('💡 تحقق من تحميل transmission-manager.js بعد script.js');
                return;
            }
            
            console.log('✅ transmissionManager متاح');
            console.log('📋 النوع:', typeof transmissionManager);
            console.log('📋 الوظائف المتاحة:', Object.getOwnPropertyNames(transmissionManager));
            
            if (Array.isArray(transmissionManager.transmissionData)) {
                console.log(`✅ transmissionData مُهيأ (${transmissionManager.transmissionData.length} عنصر)`);
            } else {
                console.error('❌ transmissionData غير مُهيأ');
            }
        }
        
        function testOperationRouting() {
            console.log('🧪 اختبار توجيه العمليات...');
            
            // اختبار عملية تركيب
            console.log('🔧 اختبار عملية تركيب...');
            testOperation('تركيب');
            
            // اختبار عملية مراقبة
            console.log('👁️ اختبار عملية مراقبة...');
            testOperation('مراقبة');
            
            // اختبار عملية تجديد
            console.log('🔄 اختبار عملية تجديد...');
            testOperation('تجديد');
        }
        
        function testOperation(operationType) {
            const testData = {
                type: operationType,
                tankNumber: 'TEST-' + Date.now(),
                carType: 'سيارة اختبار',
                serialNumber: 'SN-' + Date.now(),
                registrationNumber: 'REG-' + Date.now(),
                ownerName: 'زبون اختبار',
                phoneNumber: '0555000000',
                operationDate: new Date().toISOString().split('T')[0],
                notes: 'اختبار من صفحة الإصلاحات',
                source: 'test_fixes'
            };
            
            if (operationType === 'تجديد') {
                // اختبار إضافة بطاقة غاز
                if (typeof appData !== 'undefined' && Array.isArray(appData.gasCards)) {
                    const beforeCount = appData.gasCards.length;
                    const cardData = {
                        id: 'test-card-' + Date.now(),
                        customerName: testData.ownerName,
                        operationType: 'تجديد بطاقة',
                        phoneNumber: testData.phoneNumber,
                        createdAt: new Date().toISOString()
                    };
                    appData.gasCards.push(cardData);
                    console.log(`✅ تم إضافة بطاقة غاز (${beforeCount} → ${appData.gasCards.length})`);
                } else {
                    console.error('❌ لا يمكن إضافة بطاقة الغاز');
                }
            } else {
                // اختبار إضافة عملية إرسال
                if (typeof transmissionManager !== 'undefined' && transmissionManager !== null) {
                    try {
                        const beforeCount = transmissionManager.transmissionData?.length || 0;
                        const result = transmissionManager.addEntry(testData);
                        const afterCount = transmissionManager.transmissionData?.length || 0;
                        console.log(`✅ تم إضافة عملية ${operationType} عبر transmissionManager (${beforeCount} → ${afterCount})`);
                    } catch (error) {
                        console.error(`❌ خطأ في إضافة عملية ${operationType}:`, error.message);
                    }
                } else if (typeof appData !== 'undefined' && Array.isArray(appData.transmissionTable)) {
                    const beforeCount = appData.transmissionTable.length;
                    const entryData = {
                        ...testData,
                        id: 'test-entry-' + Date.now(),
                        createdAt: new Date().toISOString()
                    };
                    appData.transmissionTable.push(entryData);
                    console.log(`✅ تم إضافة عملية ${operationType} مباشرة إلى appData (${beforeCount} → ${appData.transmissionTable.length})`);
                } else {
                    console.error(`❌ لا يمكن إضافة عملية ${operationType}`);
                }
            }
        }
        
        // تشغيل فحص أولي
        window.addEventListener('load', function() {
            console.log('🚀 تم تحميل صفحة اختبار الإصلاحات');
            setTimeout(() => {
                checkSystemStatus();
                console.log('📋 جاهز للاختبار!');
            }, 1000);
        });
    </script>
</body>
</html>
