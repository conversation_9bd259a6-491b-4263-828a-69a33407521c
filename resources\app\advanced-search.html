<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث والفلترة المتقدمة - نظام إدارة الوقود</title>

    <!-- الأيقونات والخطوط -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- ملفات التنسيق -->
    <link rel="stylesheet" href="styles/styles.css">
    <link rel="stylesheet" href="styles/advanced-search.css">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header-title {
            font-size: 32px;
            margin: 0 0 10px 0;
            font-weight: bold;
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }

        .content-section {
            padding: 30px;
        }

        .search-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- رأس الصفحة -->
        <div class="header-section">
            <h1 class="header-title">
                <i class="fas fa-search"></i>
                البحث والفلترة المتقدمة
            </h1>
            <p class="header-subtitle">ابحث في جميع بيانات النظام باستخدام فلاتر متقدمة ومرنة</p>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-section">
            <!-- إحصائيات البحث -->
            <div class="search-stats">
                <div class="stat-card">
                    <div class="stat-number" id="total-records">0</div>
                    <div class="stat-label">إجمالي السجلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="search-count">0</div>
                    <div class="stat-label">عمليات البحث</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="saved-filters-count">0</div>
                    <div class="stat-label">الفلاتر المحفوظة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="results-count">0</div>
                    <div class="stat-label">النتائج الحالية</div>
                </div>
            </div>

            <!-- نظام البحث المتقدم -->
            <div class="advanced-search-container">
                <!-- رأس البحث -->
                <div class="search-header">
                    <h2 class="search-title">
                        <i class="fas fa-filter"></i>
                        البحث المتقدم
                    </h2>
                    <div class="search-actions">
                        <button class="filter-btn secondary" onclick="clearAllFilters()">
                            <i class="fas fa-eraser"></i>
                            مسح الكل
                        </button>
                        <button class="filter-btn primary" onclick="saveCurrentFilter()">
                            <i class="fas fa-save"></i>
                            حفظ الفلتر
                        </button>
                    </div>
                </div>

                <!-- شريط البحث الرئيسي -->
                <div class="main-search-bar">
                    <div class="search-input-container">
                        <input type="text"
                               id="main-search-input"
                               class="search-input"
                               placeholder="ابحث في جميع البيانات..."
                               onkeypress="handleSearchKeyPress(event)">
                        <button class="search-button" onclick="performGlobalSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- أزرار البحث السريع -->
                <div class="quick-search-buttons">
                    <button class="quick-search-btn" onclick="quickSearch('customers')">
                        <i class="fas fa-users"></i> الزبائن
                    </button>
                    <button class="quick-search-btn" onclick="quickSearch('vehicles')">
                        <i class="fas fa-car"></i> السيارات
                    </button>
                    <button class="quick-search-btn" onclick="quickSearch('transmissionTable')">
                        <i class="fas fa-table"></i> جدول الإرسال
                    </button>
                    <button class="quick-search-btn" onclick="quickSearch('gasCards')">
                        <i class="fas fa-credit-card"></i> بطاقات الغاز
                    </button>
                    <button class="quick-search-btn" onclick="quickSearch('suppliers')">
                        <i class="fas fa-truck"></i> الموردين
                    </button>
                    <button class="quick-search-btn" onclick="quickSearch('inventory')">
                        <i class="fas fa-boxes"></i> المخزون
                    </button>
                </div>

                <!-- الفلاتر المتقدمة -->
                <div class="advanced-filters">
                    <div class="filters-header">
                        <h3 class="filters-title">الفلاتر المتقدمة</h3>
                        <button class="toggle-filters-btn" onclick="toggleAdvancedFilters()">
                            <i class="fas fa-chevron-down" id="filters-toggle-icon"></i>
                            <span id="filters-toggle-text">إظهار الفلاتر</span>
                        </button>
                    </div>

                    <div class="filters-content" id="filters-content" style="display: none;">
                        <!-- فلاتر الجدول -->
                        <div class="filters-grid">
                            <div class="filter-group">
                                <label class="filter-label">الجدول المستهدف</label>
                                <select class="filter-select" id="target-table" onchange="updateFieldOptions()">
                                    <option value="all">جميع الجداول</option>
                                    <option value="customers">الزبائن</option>
                                    <option value="vehicles">السيارات</option>
                                    <option value="transmissionTable">جدول الإرسال</option>
                                    <option value="gasCards">بطاقات الغاز</option>
                                    <option value="suppliers">الموردين</option>
                                    <option value="inventory">المخزون</option>
                                    <option value="sales">المبيعات</option>
                                    <option value="purchases">المشتريات</option>
                                    <option value="debts">الديون</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">الحقل</label>
                                <select class="filter-select" id="search-field">
                                    <option value="all">جميع الحقول</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">نوع الفلتر</label>
                                <select class="filter-select" id="filter-type" onchange="updateFilterOperators()">
                                    <option value="text">نص</option>
                                    <option value="date">تاريخ</option>
                                    <option value="number">رقم</option>
                                    <option value="select">اختيار</option>
                                    <option value="range">نطاق</option>
                                    <option value="boolean">منطقي</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">المشغل</label>
                                <select class="filter-select" id="filter-operator">
                                    <option value="contains">يحتوي على</option>
                                    <option value="equals">يساوي</option>
                                    <option value="startsWith">يبدأ بـ</option>
                                    <option value="endsWith">ينتهي بـ</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">القيمة</label>
                                <input type="text" class="filter-input" id="filter-value" placeholder="أدخل القيمة...">
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">من تاريخ</label>
                                <input type="date" class="filter-input" id="date-from">
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">إلى تاريخ</label>
                                <input type="date" class="filter-input" id="date-to">
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">الترتيب حسب</label>
                                <select class="filter-select" id="sort-field">
                                    <option value="createdAt">تاريخ الإنشاء</option>
                                    <option value="name">الاسم</option>
                                    <option value="date">التاريخ</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">اتجاه الترتيب</label>
                                <select class="filter-select" id="sort-order">
                                    <option value="desc">تنازلي</option>
                                    <option value="asc">تصاعدي</option>
                                </select>
                            </div>
                        </div>

                        <!-- أزرار الفلاتر -->
                        <div class="filters-actions">
                            <button class="filter-btn secondary" onclick="resetFilters()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                            <button class="filter-btn primary" onclick="applyAdvancedFilters()">
                                <i class="fas fa-filter"></i>
                                تطبيق الفلاتر
                            </button>
                        </div>
                    </div>
                </div>

                <!-- نتائج البحث -->
                <div class="search-results" id="search-results" style="display: none;">
                    <div class="results-header">
                        <div class="results-count" id="results-summary">
                            لم يتم العثور على نتائج
                        </div>
                        <div class="results-actions">
                            <button class="results-btn" onclick="exportResults('csv')">
                                <i class="fas fa-file-csv"></i>
                                تصدير CSV
                            </button>
                            <button class="results-btn" onclick="exportResults('excel')">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="results-btn" onclick="printResults()">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                        </div>
                    </div>

                    <!-- تبويبات النتائج -->
                    <div class="results-tabs" id="results-tabs">
                        <!-- سيتم إنشاؤها ديناميكياً -->
                    </div>

                    <!-- محتوى النتائج -->
                    <div class="results-content" id="results-content">
                        <!-- سيتم إنشاؤها ديناميكياً -->
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 30px; margin-top: 30px;">
                <!-- تاريخ البحث -->
                <div class="search-history">
                    <h3 style="margin-top: 0; color: #2196F3;">
                        <i class="fas fa-history"></i>
                        تاريخ البحث
                    </h3>
                    <div id="search-history-list">
                        <div class="search-message">لا يوجد تاريخ بحث حتى الآن</div>
                    </div>
                    <div style="text-align: center; margin-top: 16px;">
                        <button class="filter-btn secondary" onclick="clearSearchHistory()">
                            <i class="fas fa-trash"></i>
                            مسح التاريخ
                        </button>
                    </div>
                </div>

                <!-- الفلاتر المحفوظة -->
                <div class="saved-filters">
                    <h3 style="margin-top: 0; color: #2196F3;">
                        <i class="fas fa-bookmark"></i>
                        الفلاتر المحفوظة
                    </h3>
                    <div id="saved-filters-list">
                        <div class="search-message">لا توجد فلاتر محفوظة</div>
                    </div>
                    <div style="text-align: center; margin-top: 16px;">
                        <button class="filter-btn secondary" onclick="clearSavedFilters()">
                            <i class="fas fa-trash"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات المطلوبة -->
    <script src="scripts/advanced-search.js"></script>

    <script>
        // بيانات اختبار شاملة
        const mockData = {
            customers: [
                { id: 'cust-1', name: 'أحمد محمد علي', phone: '0555123456', address: 'الجزائر العاصمة', createdAt: '2024-01-15T10:00:00Z' },
                { id: 'cust-2', name: 'فاطمة عبد الرحمن', phone: '0666789012', address: 'وهران', createdAt: '2024-01-20T14:00:00Z' },
                { id: 'cust-3', name: 'محمد الطاهر', phone: '0777456789', address: 'قسنطينة', createdAt: '2024-02-01T09:00:00Z' }
            ],
            vehicles: [
                { id: 'veh-1', plateNumber: '123-456-78', type: 'سيارة خاصة', year: 2020, customerId: 'cust-1', createdAt: '2024-01-15T11:00:00Z' },
                { id: 'veh-2', plateNumber: '987-654-32', type: 'شاحنة', year: 2018, customerId: 'cust-2', createdAt: '2024-01-20T15:00:00Z' }
            ],
            transmissionTable: [
                { id: 'trans-1', customerName: 'أحمد محمد علي', vehiclePlate: '123-456-78', operationType: 'تركيب', operationDate: '2024-01-15', cost: 5000, status: 'completed', createdAt: '2024-01-15T10:00:00Z' },
                { id: 'trans-2', customerName: 'فاطمة عبد الرحمن', vehiclePlate: '987-654-32', operationType: 'مراقبة', operationDate: '2024-01-20', cost: 3000, status: 'pending', createdAt: '2024-01-20T14:00:00Z' }
            ],
            gasCards: [
                { id: 'card-1', customerName: 'أحمد محمد علي', vehiclePlate: '123-456-78', cardNumber: 'GC-12345', issueDate: '2024-01-10', status: 'active', createdAt: '2024-01-10T09:00:00Z' }
            ]
        };

        // تعيين البيانات للاختبار
        window.appData = mockData;

        // متغيرات عامة
        let currentResults = {};
        let filtersVisible = false;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatistics();
            updateFieldOptions();
            loadSearchHistory();
            loadSavedFilters();
        });

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalRecords = Object.values(appData).reduce((total, table) => total + table.length, 0);
            const searchCount = advancedSearch.searchHistory.length;
            const savedFiltersCount = advancedSearch.savedFilters.length;
            const resultsCount = Object.values(currentResults).reduce((total, table) => total + table.length, 0);

            document.getElementById('total-records').textContent = totalRecords;
            document.getElementById('search-count').textContent = searchCount;
            document.getElementById('saved-filters-count').textContent = savedFiltersCount;
            document.getElementById('results-count').textContent = resultsCount;
        }

        // البحث العام
        function performGlobalSearch() {
            const query = document.getElementById('main-search-input').value.trim();
            if (!query) return;

            const results = advancedSearch.globalSearch(query);
            currentResults = results;
            displayResults(results);
            updateStatistics();
        }

        // البحث السريع
        function quickSearch(table) {
            document.querySelectorAll('.quick-search-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            document.getElementById('target-table').value = table;
            updateFieldOptions();

            const query = document.getElementById('main-search-input').value.trim();
            if (query) {
                const results = advancedSearch.globalSearch(query, { tables: [table] });
                currentResults = results;
                displayResults(results);
                updateStatistics();
            }
        }

        // تطبيق الفلاتر المتقدمة
        function applyAdvancedFilters() {
            const filters = collectFilters();
            const options = collectOptions();

            const results = advancedSearch.advancedSearch(filters, options);
            currentResults = results;
            displayResults(results);
            updateStatistics();
        }

        // جمع الفلاتر
        function collectFilters() {
            const filters = {};
            const field = document.getElementById('search-field').value;
            const value = document.getElementById('filter-value').value.trim();

            if (field !== 'all' && value) {
                filters[field] = {
                    type: 'text',
                    operator: 'contains',
                    value: value
                };
            }

            return filters;
        }

        // جمع الخيارات
        function collectOptions() {
            return {
                table: document.getElementById('target-table').value,
                sortBy: document.getElementById('sort-field').value,
                sortOrder: document.getElementById('sort-order').value
            };
        }

        // عرض النتائج
        function displayResults(results) {
            const resultsContainer = document.getElementById('search-results');

            if (Object.keys(results).length === 0) {
                resultsContainer.style.display = 'none';
                return;
            }

            resultsContainer.style.display = 'block';

            const totalResults = Object.values(results).reduce((total, table) => total + table.length, 0);
            document.getElementById('results-summary').textContent = `تم العثور على ${totalResults} نتيجة`;

            createResultTabs(results);
            displayFirstTable(results);
        }

        // إنشاء التبويبات
        function createResultTabs(results) {
            const container = document.getElementById('results-tabs');
            container.innerHTML = '';

            Object.keys(results).forEach((tableName, index) => {
                const tab = document.createElement('button');
                tab.className = `results-tab ${index === 0 ? 'active' : ''}`;
                tab.textContent = `${getTableDisplayName(tableName)} (${results[tableName].length})`;
                tab.onclick = () => showTableResults(tableName, results);
                container.appendChild(tab);
            });
        }

        // عرض أول جدول
        function displayFirstTable(results) {
            const firstTable = Object.keys(results)[0];
            if (firstTable) {
                displayTableData(results[firstTable], firstTable);
            }
        }

        // عرض نتائج جدول
        function showTableResults(tableName, results) {
            document.querySelectorAll('.results-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            displayTableData(results[tableName], tableName);
        }

        // عرض بيانات الجدول
        function displayTableData(data, tableName) {
            const container = document.getElementById('results-content');

            if (!data || data.length === 0) {
                container.innerHTML = '<div class="search-message empty">لا توجد بيانات</div>';
                return;
            }

            const table = document.createElement('table');
            table.className = 'results-table';

            const headers = Object.keys(data[0]);
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');

            headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = getFieldDisplayName(header);
                headerRow.appendChild(th);
            });

            thead.appendChild(headerRow);
            table.appendChild(thead);

            const tbody = document.createElement('tbody');
            data.forEach(row => {
                const tr = document.createElement('tr');
                headers.forEach(header => {
                    const td = document.createElement('td');
                    td.textContent = formatValue(row[header]);
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });

            table.appendChild(tbody);
            container.innerHTML = '';
            container.appendChild(table);
        }

        // تحديث خيارات الحقول
        function updateFieldOptions() {
            const table = document.getElementById('target-table').value;
            const fieldSelect = document.getElementById('search-field');

            fieldSelect.innerHTML = '<option value="all">جميع الحقول</option>';

            if (table !== 'all' && appData[table] && appData[table].length > 0) {
                const fields = Object.keys(appData[table][0]);
                fields.forEach(field => {
                    const option = document.createElement('option');
                    option.value = field;
                    option.textContent = getFieldDisplayName(field);
                    fieldSelect.appendChild(option);
                });
            }
        }

        // تحديث مشغلات الفلتر
        function updateFilterOperators() {
            // يمكن إضافة منطق لتحديث المشغلات حسب نوع الفلتر
        }

        // إظهار/إخفاء الفلاتر المتقدمة
        function toggleAdvancedFilters() {
            const content = document.getElementById('filters-content');
            const icon = document.getElementById('filters-toggle-icon');
            const text = document.getElementById('filters-toggle-text');

            filtersVisible = !filtersVisible;

            if (filtersVisible) {
                content.style.display = 'block';
                icon.className = 'fas fa-chevron-up';
                text.textContent = 'إخفاء الفلاتر';
            } else {
                content.style.display = 'none';
                icon.className = 'fas fa-chevron-down';
                text.textContent = 'إظهار الفلاتر';
            }
        }

        // مسح جميع الفلاتر
        function clearAllFilters() {
            document.getElementById('main-search-input').value = '';
            document.getElementById('target-table').value = 'all';
            document.getElementById('search-field').value = 'all';
            document.getElementById('filter-value').value = '';
            document.getElementById('date-from').value = '';
            document.getElementById('date-to').value = '';

            document.querySelectorAll('.quick-search-btn').forEach(btn => btn.classList.remove('active'));

            currentResults = {};
            document.getElementById('search-results').style.display = 'none';
            updateStatistics();
        }

        // إعادة تعيين الفلاتر
        function resetFilters() {
            clearAllFilters();
        }

        // حفظ الفلتر الحالي
        function saveCurrentFilter() {
            const name = prompt('أدخل اسم الفلتر:');
            if (name) {
                const filters = collectFilters();
                const options = collectOptions();
                advancedSearch.saveFilter(name, filters, options);
                loadSavedFilters();
                updateStatistics();
            }
        }

        // تحميل تاريخ البحث
        function loadSearchHistory() {
            const container = document.getElementById('search-history-list');
            const history = advancedSearch.searchHistory.slice(0, 10);

            if (history.length === 0) {
                container.innerHTML = '<div class="search-message">لا يوجد تاريخ بحث</div>';
                return;
            }

            container.innerHTML = '';
            history.forEach(item => {
                const div = document.createElement('div');
                div.className = 'history-item';
                div.innerHTML = `
                    <div class="history-query">${item.query}</div>
                    <div class="history-meta">
                        <span>${item.resultCount} نتيجة</span>
                        <span>${new Date(item.timestamp).toLocaleDateString('ar-DZ')}</span>
                    </div>
                `;
                div.onclick = () => {
                    document.getElementById('main-search-input').value = item.query;
                    performGlobalSearch();
                };
                container.appendChild(div);
            });
        }

        // تحميل الفلاتر المحفوظة
        function loadSavedFilters() {
            const container = document.getElementById('saved-filters-list');
            const filters = advancedSearch.savedFilters;

            if (filters.length === 0) {
                container.innerHTML = '<div class="search-message">لا توجد فلاتر محفوظة</div>';
                return;
            }

            container.innerHTML = '';
            filters.forEach(filter => {
                const div = document.createElement('div');
                div.className = 'saved-filter-item';
                div.innerHTML = `
                    <div class="saved-filter-name">${filter.name}</div>
                    <div class="saved-filter-actions">
                        <button class="saved-filter-btn load" onclick="loadFilter('${filter.id}')">تحميل</button>
                        <button class="saved-filter-btn delete" onclick="deleteFilter('${filter.id}')">حذف</button>
                    </div>
                `;
                container.appendChild(div);
            });
        }

        // تحميل فلتر محفوظ
        function loadFilter(filterId) {
            const filter = advancedSearch.loadFilter(filterId);
            if (filter) {
                // تطبيق الفلتر المحفوظ
                applyAdvancedFilters();
            }
        }

        // حذف فلتر محفوظ
        function deleteFilter(filterId) {
            if (confirm('هل تريد حذف هذا الفلتر؟')) {
                advancedSearch.deleteFilter(filterId);
                loadSavedFilters();
                updateStatistics();
            }
        }

        // مسح تاريخ البحث
        function clearSearchHistory() {
            if (confirm('هل تريد مسح تاريخ البحث؟')) {
                advancedSearch.clearSearchHistory();
                loadSearchHistory();
                updateStatistics();
            }
        }

        // مسح الفلاتر المحفوظة
        function clearSavedFilters() {
            if (confirm('هل تريد مسح جميع الفلاتر المحفوظة؟')) {
                advancedSearch.clearSavedFilters();
                loadSavedFilters();
                updateStatistics();
            }
        }

        // تصدير النتائج
        function exportResults(format) {
            if (Object.keys(currentResults).length === 0) {
                alert('لا توجد نتائج للتصدير');
                return;
            }

            const data = advancedSearch.exportSearchResults(currentResults, format);
            downloadFile(data, `search-results.${format}`, format);
        }

        // تحميل ملف
        function downloadFile(content, filename, type) {
            const blob = new Blob([content], { type: `text/${type}` });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
        }

        // طباعة النتائج
        function printResults() {
            window.print();
        }

        // التعامل مع ضغط Enter في البحث
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                performGlobalSearch();
            }
        }

        // تنسيق القيم
        function formatValue(value) {
            if (value === null || value === undefined) return '-';
            if (typeof value === 'string' && value.includes('T')) {
                return new Date(value).toLocaleDateString('ar-DZ');
            }
            return value.toString();
        }

        // أسماء الجداول للعرض
        function getTableDisplayName(tableName) {
            const names = {
                customers: 'الزبائن',
                vehicles: 'السيارات',
                transmissionTable: 'جدول الإرسال',
                gasCards: 'بطاقات الغاز',
                suppliers: 'الموردين',
                inventory: 'المخزون'
            };
            return names[tableName] || tableName;
        }

        // أسماء الحقول للعرض
        function getFieldDisplayName(fieldName) {
            const names = {
                id: 'المعرف',
                name: 'الاسم',
                phone: 'الهاتف',
                address: 'العنوان',
                plateNumber: 'رقم اللوحة',
                type: 'النوع',
                year: 'السنة',
                operationType: 'نوع العملية',
                operationDate: 'تاريخ العملية',
                cost: 'التكلفة',
                status: 'الحالة',
                cardNumber: 'رقم البطاقة',
                issueDate: 'تاريخ الإصدار',
                createdAt: 'تاريخ الإنشاء',
                customerName: 'اسم الزبون',
                vehiclePlate: 'لوحة السيارة'
            };
            return names[fieldName] || fieldName;
        }
    </script>
</body>
</html>

    <script>
        // بيانات اختبار شاملة
        const mockData = {
            customers: [
                {
                    id: 'cust-1',
                    name: 'أحمد محمد علي',
                    phone: '0555123456',
                    address: 'الجزائر العاصمة, الجزائر',
                    nationalId: '1234567890123',
                    email: '<EMAIL>',
                    createdAt: '2024-01-15T10:00:00Z'
                },
                {
                    id: 'cust-2',
                    name: 'فاطمة عبد الرحمن',
                    phone: '0666789012',
                    address: 'وهران, الجزائر',
                    nationalId: '9876543210987',
                    email: '<EMAIL>',
                    createdAt: '2024-01-20T14:00:00Z'
                },
                {
                    id: 'cust-3',
                    name: 'محمد الطاهر',
                    phone: '0777456789',
                    address: 'قسنطينة, الجزائر',
                    nationalId: '5555666677778',
                    email: '<EMAIL>',
                    createdAt: '2024-02-01T09:00:00Z'
                }
            ],
            vehicles: [
                {
                    id: 'veh-1',
                    plateNumber: '123-456-78',
                    type: 'سيارة خاصة',
                    model: 'تويوتا كامري',
                    year: 2020,
                    chassisNumber: 'CH123456789',
                    customerId: 'cust-1',
                    createdAt: '2024-01-15T11:00:00Z'
                },
                {
                    id: 'veh-2',
                    plateNumber: '987-654-32',
                    type: 'شاحنة',
                    model: 'مرسيدس أكتروس',
                    year: 2018,
                    chassisNumber: 'CH987654321',
                    customerId: 'cust-2',
                    createdAt: '2024-01-20T15:00:00Z'
                }
            ],
            transmissionTable: [
                {
                    id: 'trans-1',
                    customerId: 'cust-1',
                    customerName: 'أحمد محمد علي',
                    vehicleId: 'veh-1',
                    vehiclePlate: '123-456-78',
                    operationType: 'تركيب',
                    operationDate: '2024-01-15',
                    cost: 5000,
                    status: 'completed',
                    notes: 'تم التركيب بنجاح',
                    createdAt: '2024-01-15T10:00:00Z'
                },
                {
                    id: 'trans-2',
                    customerId: 'cust-2',
                    customerName: 'فاطمة عبد الرحمن',
                    vehicleId: 'veh-2',
                    vehiclePlate: '987-654-32',
                    operationType: 'مراقبة',
                    operationDate: '2024-01-20',
                    cost: 3000,
                    status: 'pending',
                    notes: 'في انتظار المراقبة',
                    createdAt: '2024-01-20T14:00:00Z'
                }
            ],
            gasCards: [
                {
                    id: 'card-1',
                    customerId: 'cust-1',
                    customerName: 'أحمد محمد علي',
                    vehicleId: 'veh-1',
                    vehiclePlate: '123-456-78',
                    cardNumber: 'GC-12345',
                    issueDate: '2024-01-10',
                    renewalCost: 2000,
                    status: 'active',
                    createdAt: '2024-01-10T09:00:00Z'
                }
            ],
            suppliers: [
                {
                    id: 'sup-1',
                    name: 'شركة الوقود المتقدم',
                    phone: '0213123456',
                    address: 'الجزائر العاصمة',
                    email: '<EMAIL>',
                    category: 'وقود',
                    createdAt: '2024-01-01T08:00:00Z'
                }
            ],
            inventory: [
                {
                    id: 'inv-1',
                    itemName: 'جهاز GPS',
                    category: 'أجهزة',
                    supplier: 'شركة الوقود المتقدم',
                    location: 'المخزن الرئيسي',
                    barcode: 'BC123456',
                    quantity: 50,
                    price: 15000,
                    createdAt: '2024-01-05T10:00:00Z'
                }
            ]
        };

        // تعيين البيانات للاختبار
        window.appData = mockData;

        // متغيرات عامة
        let currentResults = {};
        let currentFilters = {};
        let filtersVisible = false;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            updateStatistics();
            loadSearchHistory();
            loadSavedFilters();
            updateFieldOptions();
        });

        // تهيئة الصفحة
        function initializePage() {
            console.log('🔍 تم تحميل صفحة البحث المتقدم');

            // تحديث خيارات الحقول
            updateFieldOptions();
            updateFilterOperators();

            // تحديث الإحصائيات
            updateStatistics();
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalRecords = Object.values(appData).reduce((total, table) => total + table.length, 0);
            const searchCount = advancedSearch.searchHistory.length;
            const savedFiltersCount = advancedSearch.savedFilters.length;
            const resultsCount = Object.values(currentResults).reduce((total, table) => total + table.length, 0);

            document.getElementById('total-records').textContent = totalRecords;
            document.getElementById('search-count').textContent = searchCount;
            document.getElementById('saved-filters-count').textContent = savedFiltersCount;
            document.getElementById('results-count').textContent = resultsCount;
        }

        // البحث العام
        function performGlobalSearch() {
            const query = document.getElementById('main-search-input').value.trim();

            if (!query) {
                showMessage('يرجى إدخال كلمة البحث', 'warning');
                return;
            }

            showLoading();

            setTimeout(() => {
                try {
                    const results = advancedSearch.globalSearch(query, {
                        caseSensitive: false,
                        exactMatch: false,
                        includeArchived: false
                    });

                    currentResults = results;
                    displayResults(results);
                    updateStatistics();

                } catch (error) {
                    console.error('خطأ في البحث:', error);
                    showMessage('حدث خطأ أثناء البحث', 'error');
                } finally {
                    hideLoading();
                }
            }, 500);
        }

        // البحث السريع
        function quickSearch(table) {
            // إزالة التحديد من جميع الأزرار
            document.querySelectorAll('.quick-search-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // تحديد الزر المضغوط
            event.target.classList.add('active');

            // تحديث الجدول المستهدف
            document.getElementById('target-table').value = table;
            updateFieldOptions();

            // تطبيق البحث إذا كان هناك نص
            const query = document.getElementById('main-search-input').value.trim();
            if (query) {
                performTableSearch(table, query);
            }
        }

        // البحث في جدول محدد
        function performTableSearch(table, query) {
            showLoading();

            setTimeout(() => {
                try {
                    const results = advancedSearch.globalSearch(query, {
                        tables: [table],
                        caseSensitive: false,
                        exactMatch: false
                    });

                    currentResults = results;
                    displayResults(results);
                    updateStatistics();

                } catch (error) {
                    console.error('خطأ في البحث:', error);
                    showMessage('حدث خطأ أثناء البحث', 'error');
                } finally {
                    hideLoading();
                }
            }, 300);
        }

        // تطبيق الفلاتر المتقدمة
        function applyAdvancedFilters() {
            const filters = collectFilters();
            const options = collectOptions();

            if (Object.keys(filters).length === 0) {
                showMessage('يرجى تحديد فلتر واحد على الأقل', 'warning');
                return;
            }

            showLoading();

            setTimeout(() => {
                try {
                    const results = advancedSearch.advancedSearch(filters, options);

                    currentResults = results;
                    currentFilters = filters;
                    displayResults(results);
                    updateStatistics();

                } catch (error) {
                    console.error('خطأ في تطبيق الفلاتر:', error);
                    showMessage('حدث خطأ أثناء تطبيق الفلاتر', 'error');
                } finally {
                    hideLoading();
                }
            }, 500);
        }

        // جمع الفلاتر من النموذج
        function collectFilters() {
            const filters = {};

            const field = document.getElementById('search-field').value;
            const filterType = document.getElementById('filter-type').value;
            const operator = document.getElementById('filter-operator').value;
            const value = document.getElementById('filter-value').value.trim();
            const dateFrom = document.getElementById('date-from').value;
            const dateTo = document.getElementById('date-to').value;

            if (field !== 'all' && value) {
                filters[field] = {
                    type: filterType,
                    operator: operator,
                    value: value
                };
            }

            if (dateFrom || dateTo) {
                filters['createdAt'] = {
                    type: 'date',
                    operator: 'between',
                    value: {
                        start: dateFrom || '2020-01-01',
                        end: dateTo || new Date().toISOString().split('T')[0]
                    }
                };
            }

            return filters;
        }

        // جمع خيارات البحث
        function collectOptions() {
            return {
                table: document.getElementById('target-table').value,
                sortBy: document.getElementById('sort-field').value,
                sortOrder: document.getElementById('sort-order').value,
                limit: null,
                offset: 0
            };
        }

        // عرض النتائج
        function displayResults(results) {
            const resultsContainer = document.getElementById('search-results');
            const tabsContainer = document.getElementById('results-tabs');
            const contentContainer = document.getElementById('results-content');

            if (Object.keys(results).length === 0) {
                resultsContainer.style.display = 'none';
                showMessage('لم يتم العثور على نتائج', 'empty');
                return;
            }

            resultsContainer.style.display = 'block';

            // تحديث ملخص النتائج
            const totalResults = Object.values(results).reduce((total, table) => total + table.length, 0);
            document.getElementById('results-summary').textContent =
                `تم العثور على ${totalResults} نتيجة في ${Object.keys(results).length} جدول`;

            // إنشاء التبويبات
            createResultTabs(results, tabsContainer);

            // عرض النتائج
            displayResultsContent(results, contentContainer);
        }

        // إنشاء تبويبات النتائج
        function createResultTabs(results, container) {
            container.innerHTML = '';

            Object.keys(results).forEach((tableName, index) => {
                const tab = document.createElement('button');
                tab.className = `results-tab ${index === 0 ? 'active' : ''}`;
                tab.textContent = `${getTableDisplayName(tableName)} (${results[tableName].length})`;
                tab.onclick = () => showTableResults(tableName, results);
                container.appendChild(tab);
            });
        }

        // عرض نتائج جدول محدد
        function showTableResults(tableName, results) {
            // تحديث التبويبات
            document.querySelectorAll('.results-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // عرض النتائج
            const contentContainer = document.getElementById('results-content');
            displayTableData(results[tableName], tableName, contentContainer);
        }

        // عرض محتوى النتائج
        function displayResultsContent(results, container) {
            container.innerHTML = '';

            // عرض أول جدول افتراضياً
            const firstTable = Object.keys(results)[0];
            if (firstTable) {
                displayTableData(results[firstTable], firstTable, container);
            }
        }

        // عرض بيانات الجدول
        function displayTableData(data, tableName, container) {
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="search-message empty">لا توجد بيانات في هذا الجدول</div>';
                return;
            }

            const table = document.createElement('table');
            table.className = 'results-table';

            // إنشاء رأس الجدول
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');

            const headers = Object.keys(data[0]);
            headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = getFieldDisplayName(header);
                headerRow.appendChild(th);
            });

            thead.appendChild(headerRow);
            table.appendChild(thead);

            // إنشاء جسم الجدول
            const tbody = document.createElement('tbody');

            data.forEach(row => {
                const tr = document.createElement('tr');

                headers.forEach(header => {
                    const td = document.createElement('td');
                    td.textContent = formatCellValue(row[header], header);
                    tr.appendChild(td);
                });

                tbody.appendChild(tr);
            });

            table.appendChild(tbody);
            container.innerHTML = '';
            container.appendChild(table);
        }

        // تنسيق قيم الخلايا
        function formatCellValue(value, fieldName) {
            if (value === null || value === undefined) return '-';

            if (fieldName.includes('Date') || fieldName.includes('At')) {
                return new Date(value).toLocaleDateString('ar-DZ');
            }

            if (fieldName.includes('cost') || fieldName.includes('price')) {
                return `${value.toLocaleString()} د.ج`;
            }

            return value.toString();
        }

        // الحصول على اسم الجدول للعرض
        function getTableDisplayName(tableName) {
            const names = {
                customers: 'الزبائن',
                vehicles: 'السيارات',
                transmissionTable: 'جدول الإرسال',
                gasCards: 'بطاقات الغاز',
                suppliers: 'الموردين',
                inventory: 'المخزون',
                sales: 'المبيعات',
                purchases: 'المشتريات',
                debts: 'الديون'
            };
            return names[tableName] || tableName;
        }

        // الحصول على اسم الحقل للعرض
        function getFieldDisplayName(fieldName) {
            const names = {
                id: 'المعرف',
                name: 'الاسم',
                phone: 'الهاتف',
                address: 'العنوان',
                email: 'البريد الإلكتروني',
                plateNumber: 'رقم اللوحة',
                type: 'النوع',
                model: 'الموديل',
                year: 'السنة',
                operationType: 'نوع العملية',
                operationDate: 'تاريخ العملية',
                cost: 'التكلفة',
                status: 'الحالة',
                cardNumber: 'رقم البطاقة',
                issueDate: 'تاريخ الإصدار',
                createdAt: 'تاريخ الإنشاء'
            };
            return names[fieldName] || fieldName;
        }