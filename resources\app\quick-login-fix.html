<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح سريع - تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: #667eea;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 30px;
        }
        h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .subtitle {
            color: #666;
            margin: 5px 0 0;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .fix-section h3 {
            margin: 0 0 15px;
            color: #28a745;
        }
        button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            width: 100%;
        }
        button:hover {
            background: #218838;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔧</div>
            <h1>إصلاح سريع لتسجيل الدخول</h1>
            <p class="subtitle">مؤسسة وقود المستقبل</p>
        </div>

        <div class="info">
            <strong>الهدف:</strong> إصلاح مشكلة تسجيل الدخول وضمان عمل النظام بشكل صحيح
        </div>

        <div class="fix-section">
            <h3>🔧 الإصلاحات التلقائية</h3>
            <button onclick="runAutoFix()">🚀 تشغيل الإصلاح التلقائي</button>
            <div class="progress" id="progress-container" style="display: none;">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
        </div>

        <div class="fix-section">
            <h3>🔐 اختبار تسجيل الدخول</h3>
            <button onclick="testLogin()">🧪 اختبار تسجيل الدخول</button>
            <button onclick="createTestSession()">👤 إنشاء جلسة اختبار</button>
        </div>

        <div class="fix-section">
            <h3>🚀 الوصول السريع</h3>
            <button onclick="goToMainApp()">🏠 الذهاب للتطبيق الرئيسي</button>
            <button onclick="goToLoginPage()">🔐 الذهاب لصفحة تسجيل الدخول</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');
        const progressContainer = document.getElementById('progress-container');
        const progressBar = document.getElementById('progress-bar');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        function updateProgress(percent) {
            progressBar.style.width = percent + '%';
        }

        function showProgress() {
            progressContainer.style.display = 'block';
            updateProgress(0);
        }

        function hideProgress() {
            progressContainer.style.display = 'none';
        }

        async function runAutoFix() {
            clearResults();
            showProgress();
            addResult('🔧 بدء الإصلاح التلقائي...', 'info');
            
            try {
                // الخطوة 1: مسح البيانات القديمة
                updateProgress(20);
                addResult('🧹 مسح البيانات القديمة...', 'info');
                localStorage.removeItem('userSession');
                localStorage.removeItem('rememberedUser');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // الخطوة 2: إنشاء بيانات افتراضية
                updateProgress(40);
                addResult('📊 إنشاء بيانات افتراضية...', 'info');
                const defaultAppData = {
                    customers: [],
                    gasCards: [],
                    transmissions: [],
                    settings: {
                        theme: 'light',
                        language: 'ar',
                        autoSave: true
                    }
                };
                localStorage.setItem('appData', JSON.stringify(defaultAppData));
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // الخطوة 3: إنشاء جلسة صالحة
                updateProgress(60);
                addResult('👤 إنشاء جلسة صالحة...', 'info');
                const sessionData = {
                    username: 'admin',
                    loginTime: new Date().toISOString(),
                    expiryDate: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
                    isValid: true,
                    role: 'admin'
                };
                localStorage.setItem('userSession', JSON.stringify(sessionData));
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // الخطوة 4: اختبار النظام
                updateProgress(80);
                addResult('🧪 اختبار النظام...', 'info');
                
                // فحص localStorage
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    addResult('✅ localStorage يعمل بشكل صحيح', 'success');
                } catch (error) {
                    addResult('❌ مشكلة في localStorage', 'error');
                }
                
                // فحص الجلسة
                const session = localStorage.getItem('userSession');
                if (session) {
                    addResult('✅ الجلسة تم إنشاؤها بنجاح', 'success');
                } else {
                    addResult('❌ فشل في إنشاء الجلسة', 'error');
                }
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // الخطوة 5: الانتهاء
                updateProgress(100);
                addResult('🎉 تم الإصلاح بنجاح!', 'success');
                addResult('💡 يمكنك الآن الذهاب للتطبيق الرئيسي', 'info');
                
                setTimeout(() => {
                    hideProgress();
                }, 1000);
                
            } catch (error) {
                addResult('❌ خطأ في الإصلاح التلقائي: ' + error.message, 'error');
                hideProgress();
            }
        }

        async function testLogin() {
            clearResults();
            addResult('🧪 اختبار تسجيل الدخول...', 'info');
            
            const credentials = [
                { username: 'admin', password: 'admin123' },
                { username: 'user', password: 'user123' },
                { username: 'manager', password: 'manager123' }
            ];
            
            for (const cred of credentials) {
                try {
                    addResult(`🔍 اختبار: ${cred.username}`, 'info');
                    
                    // محاكاة التحقق
                    const isValid = credentials.some(c => 
                        c.username === cred.username && c.password === cred.password
                    );
                    
                    if (isValid) {
                        addResult(`✅ ${cred.username} - صحيح`, 'success');
                    } else {
                        addResult(`❌ ${cred.username} - خطأ`, 'error');
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, 300));
                    
                } catch (error) {
                    addResult(`❌ خطأ في اختبار ${cred.username}: ${error.message}`, 'error');
                }
            }
            
            addResult('🎯 انتهى اختبار بيانات الاعتماد', 'info');
        }

        function createTestSession() {
            clearResults();
            addResult('👤 إنشاء جلسة اختبار...', 'info');
            
            try {
                const sessionData = {
                    username: 'admin',
                    loginTime: new Date().toISOString(),
                    expiryDate: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
                    isValid: true,
                    role: 'admin',
                    testSession: true
                };
                
                localStorage.setItem('userSession', JSON.stringify(sessionData));
                addResult('✅ تم إنشاء جلسة اختبار بنجاح', 'success');
                addResult('👤 المستخدم: admin', 'info');
                addResult('⏰ صالحة لمدة 8 ساعات', 'info');
                addResult('🚀 يمكنك الآن الذهاب للتطبيق الرئيسي', 'success');
                
            } catch (error) {
                addResult('❌ خطأ في إنشاء الجلسة: ' + error.message, 'error');
            }
        }

        function goToMainApp() {
            clearResults();
            addResult('🚀 الانتقال للتطبيق الرئيسي...', 'info');
            
            // التأكد من وجود جلسة صالحة
            const session = localStorage.getItem('userSession');
            if (!session) {
                addResult('⚠️ لا توجد جلسة صالحة، سيتم إنشاء واحدة...', 'warning');
                createTestSession();
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            } else {
                window.location.href = 'index.html';
            }
        }

        function goToLoginPage() {
            clearResults();
            addResult('🔐 الانتقال لصفحة تسجيل الدخول...', 'info');
            window.location.href = 'src/auth/login.html';
        }

        // فحص أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addResult('🔧 أداة الإصلاح السريع جاهزة', 'info');
            
            // فحص الجلسة الحالية
            const session = localStorage.getItem('userSession');
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    const now = new Date();
                    const expiry = new Date(sessionData.expiryDate);
                    
                    if (now < expiry && sessionData.isValid) {
                        addResult('✅ توجد جلسة صالحة للمستخدم: ' + sessionData.username, 'success');
                    } else {
                        addResult('⚠️ الجلسة منتهية الصلاحية', 'warning');
                    }
                } catch (error) {
                    addResult('❌ خطأ في قراءة الجلسة', 'error');
                }
            } else {
                addResult('ℹ️ لا توجد جلسة محفوظة', 'info');
            }
        });
    </script>
</body>
</html>
