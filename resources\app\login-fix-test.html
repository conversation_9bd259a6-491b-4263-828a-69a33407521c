<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .credentials-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .file-link {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .file-link:hover {
            background: #218838;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح نظام تسجيل الدخول</h1>
        
        <div class="info">
            <strong>الهدف:</strong> التأكد من أن نظام تسجيل الدخول يعمل بشكل صحيح بعد الإصلاحات
        </div>

        <div class="credentials-box">
            <h3>🔑 بيانات الاعتماد للاختبار:</h3>
            <ul>
                <li><strong>admin</strong> / admin123</li>
                <li><strong>user</strong> / user123</li>
                <li><strong>manager</strong> / manager123</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📁 ملفات الاختبار:</h3>
            <a href="test-login.html" class="file-link" target="_blank">🧪 صفحة اختبار تسجيل الدخول</a>
            <a href="src/auth/login.html" class="file-link" target="_blank">🔐 صفحة تسجيل الدخول الأصلية</a>
            <a href="index.html" class="file-link" target="_blank">🏠 التطبيق الرئيسي</a>
        </div>

        <div class="test-section">
            <h3>🔍 اختبارات النظام:</h3>
            <button onclick="runFullDiagnostics()">🔬 تشخيص شامل</button>
            <button onclick="testLoginFiles()">📂 فحص ملفات تسجيل الدخول</button>
            <button onclick="testElectronAPI()">⚡ فحص Electron API</button>
            <button onclick="testSessionManagement()">🔄 فحص إدارة الجلسات</button>
            <button onclick="simulateLogin()">🔐 محاكاة تسجيل الدخول</button>
            <button onclick="clearAllData()">🧹 مسح جميع البيانات</button>
        </div>

        <div id="results"></div>
    </div>

    <!-- تحميل أداة التشخيص -->
    <script src="diagnose-login-issue.js"></script>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function runFullDiagnostics() {
            clearResults();
            addResult('🔬 بدء التشخيص الشامل...', 'info');

            try {
                if (typeof LoginDiagnostics !== 'undefined') {
                    const diagnostics = new LoginDiagnostics();

                    // إعادة توجيه النتائج إلى واجهة المستخدم
                    const originalLog = diagnostics.log;
                    diagnostics.log = (message, type) => {
                        originalLog.call(diagnostics, message, type);
                        addResult(message, type);
                    };

                    const report = await diagnostics.runFullDiagnostics();

                    // عرض ملخص التقرير
                    addResult('📊 تم الانتهاء من التشخيص الشامل', 'success');
                    addResult(`📈 النتائج: ${report.successCount} نجح، ${report.warningCount} تحذير، ${report.errorCount} خطأ`, 'info');

                } else {
                    addResult('❌ أداة التشخيص غير متاحة', 'error');
                }
            } catch (error) {
                addResult('❌ خطأ في التشخيص الشامل: ' + error.message, 'error');
            }
        }

        function testLoginFiles() {
            clearResults();
            addResult('📂 فحص ملفات تسجيل الدخول...', 'info');
            
            // قائمة الملفات المطلوبة
            const requiredFiles = [
                'src/auth/login.html',
                'src/auth/login.css', 
                'src/auth/login.js',
                'src/auth/license.js',
                'src/data/algeria-data.js'
            ];
            
            let allFilesExist = true;
            
            requiredFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            addResult(`✅ ${file} - موجود`, 'success');
                        } else {
                            addResult(`❌ ${file} - غير موجود (${response.status})`, 'error');
                            allFilesExist = false;
                        }
                    })
                    .catch(error => {
                        addResult(`❌ ${file} - خطأ في التحميل: ${error.message}`, 'error');
                        allFilesExist = false;
                    });
            });
            
            setTimeout(() => {
                if (allFilesExist) {
                    addResult('🎉 جميع ملفات تسجيل الدخول موجودة!', 'success');
                }
            }, 2000);
        }

        function testElectronAPI() {
            clearResults();
            addResult('⚡ فحص Electron API...', 'info');
            
            if (typeof window.electronAPI !== 'undefined') {
                addResult('✅ electronAPI متاح', 'success');
                
                // فحص الوظائف المطلوبة
                const requiredMethods = ['login', 'logout', 'reloadAfterLogin'];
                requiredMethods.forEach(method => {
                    if (typeof window.electronAPI[method] === 'function') {
                        addResult(`✅ electronAPI.${method} متاح`, 'success');
                    } else {
                        addResult(`❌ electronAPI.${method} غير متاح`, 'error');
                    }
                });
            } else {
                addResult('⚠️ electronAPI غير متاح - التطبيق يعمل في المتصفح', 'warning');
                addResult('ℹ️ سيتم استخدام localStorage بدلاً من Electron', 'info');
            }
        }

        function testSessionManagement() {
            clearResults();
            addResult('🔄 فحص إدارة الجلسات...', 'info');
            
            try {
                // اختبار localStorage
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                addResult('✅ localStorage يعمل بشكل صحيح', 'success');
                
                // فحص الجلسة الحالية
                const session = localStorage.getItem('userSession');
                if (session) {
                    try {
                        const sessionData = JSON.parse(session);
                        const now = new Date();
                        const expiry = new Date(sessionData.expiryDate);
                        
                        if (now < expiry && sessionData.isValid) {
                            addResult(`✅ جلسة صالحة للمستخدم: ${sessionData.username}`, 'success');
                            addResult(`📅 تنتهي في: ${expiry.toLocaleString('ar-DZ')}`, 'info');
                        } else {
                            addResult('⚠️ الجلسة منتهية الصلاحية', 'warning');
                        }
                    } catch (error) {
                        addResult('❌ خطأ في قراءة بيانات الجلسة', 'error');
                    }
                } else {
                    addResult('ℹ️ لا توجد جلسة محفوظة', 'info');
                }
                
                // فحص بيانات التطبيق
                const appData = localStorage.getItem('appData');
                if (appData) {
                    try {
                        const data = JSON.parse(appData);
                        addResult(`📊 بيانات التطبيق محفوظة (${Object.keys(data).length} مفاتيح)`, 'success');
                    } catch (error) {
                        addResult('❌ خطأ في قراءة بيانات التطبيق', 'error');
                    }
                } else {
                    addResult('ℹ️ لا توجد بيانات تطبيق محفوظة', 'info');
                }
                
            } catch (error) {
                addResult('❌ خطأ في فحص إدارة الجلسات: ' + error.message, 'error');
            }
        }

        async function simulateLogin() {
            clearResults();
            addResult('🔐 محاكاة عملية تسجيل الدخول...', 'info');
            
            const credentials = { username: 'admin', password: 'admin123' };
            
            try {
                // محاكاة التحقق من بيانات الاعتماد
                addResult('🔍 التحقق من بيانات الاعتماد...', 'info');
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const validCredentials = [
                    { username: 'admin', password: 'admin123' },
                    { username: 'user', password: 'user123' },
                    { username: 'manager', password: 'manager123' }
                ];
                
                const isValid = validCredentials.some(cred => 
                    cred.username === credentials.username && cred.password === credentials.password
                );
                
                if (isValid) {
                    addResult('✅ بيانات الاعتماد صحيحة', 'success');
                    
                    // إنشاء جلسة
                    const sessionData = {
                        username: credentials.username,
                        loginTime: new Date().toISOString(),
                        expiryDate: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
                        isValid: true
                    };
                    
                    localStorage.setItem('userSession', JSON.stringify(sessionData));
                    addResult('✅ تم إنشاء الجلسة بنجاح', 'success');
                    
                    // اختبار إعادة التوجيه
                    if (typeof window.electronAPI !== 'undefined') {
                        addResult('🔄 استخدام Electron API لإعادة التحميل...', 'info');
                        // window.electronAPI.reloadAfterLogin();
                        addResult('✅ تم استدعاء reloadAfterLogin', 'success');
                    } else {
                        addResult('🔄 استخدام إعادة التوجيه العادية...', 'info');
                        addResult('✅ سيتم إعادة التوجيه إلى index.html', 'success');
                    }
                    
                } else {
                    addResult('❌ بيانات الاعتماد غير صحيحة', 'error');
                }
                
            } catch (error) {
                addResult('❌ خطأ في محاكاة تسجيل الدخول: ' + error.message, 'error');
            }
        }

        function clearAllData() {
            clearResults();
            addResult('🧹 مسح جميع البيانات...', 'info');
            
            try {
                // مسح جميع بيانات localStorage
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    localStorage.removeItem(key);
                    addResult(`🗑️ تم مسح: ${key}`, 'info');
                });
                
                addResult('✅ تم مسح جميع البيانات المحلية', 'success');
                
                // مسح جلسة Electron إذا كانت متاحة
                if (typeof window.electronAPI !== 'undefined') {
                    window.electronAPI.logout().then(() => {
                        addResult('✅ تم مسح جلسة Electron', 'success');
                    }).catch(error => {
                        addResult('⚠️ خطأ في مسح جلسة Electron: ' + error.message, 'warning');
                    });
                }
                
            } catch (error) {
                addResult('❌ خطأ في مسح البيانات: ' + error.message, 'error');
            }
        }

        // فحص أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addResult('🚀 تم تحميل صفحة اختبار إصلاح تسجيل الدخول', 'info');
            addResult('📋 اختر أحد الاختبارات أعلاه للبدء', 'info');
            
            // فحص سريع للبيئة
            if (typeof window.electronAPI !== 'undefined') {
                addResult('⚡ البيئة: Electron Application', 'success');
            } else {
                addResult('🌐 البيئة: Web Browser', 'info');
            }
        });
    </script>
</body>
</html>
