<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص أخطاء التحديث</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            direction: rtl;
            padding: 2rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .debug-header {
            background: #dc3545;
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .debug-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .log-area {
            background: #212529;
            color: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 500px;
            overflow-y: auto;
            margin-top: 1rem;
            white-space: pre-wrap;
        }

        .dashboard-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .debug-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-success {
            background: #28a745;
        }

        .status-error {
            background: #dc3545;
        }

        .status-warning {
            background: #ffc107;
        }

        .status-info {
            background: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="debug-header">
            <h1><i class="fas fa-bug"></i> تشخيص أخطاء التحديث</h1>
            <p>أداة تشخيص متقدمة لحل مشاكل أزرار التحديث</p>
        </div>

        <div class="debug-grid">
            <div class="debug-section">
                <h2><i class="fas fa-search"></i> فحص النظام</h2>
                
                <button class="btn btn-success" onclick="checkDashboardStatus()">
                    <i class="fas fa-heartbeat"></i> فحص حالة اللوحة
                </button>
                
                <button class="btn btn-warning" onclick="checkRefreshButtons()">
                    <i class="fas fa-sync-alt"></i> فحص أزرار التحديث
                </button>
                
                <button class="btn" onclick="checkJavaScriptErrors()">
                    <i class="fas fa-code"></i> فحص أخطاء JavaScript
                </button>
                
                <button class="btn btn-danger" onclick="clearDebugLog()">
                    <i class="fas fa-trash"></i> مسح السجل
                </button>

                <div id="debugLog" class="log-area"></div>
            </div>

            <div class="debug-section">
                <h2><i class="fas fa-external-link-alt"></i> لوحة التراخيص</h2>
                <iframe id="dashboardFrame" class="dashboard-frame" src="resources/app/src/license-management/admin-dashboard.html"></iframe>
            </div>
        </div>

        <div class="debug-section">
            <h2><i class="fas fa-tools"></i> اختبارات التحديث المباشرة</h2>
            
            <button class="btn btn-success" onclick="directRefreshTest()">
                <i class="fas fa-play"></i> اختبار التحديث المباشر
            </button>
            
            <button class="btn btn-warning" onclick="manualRefreshTest()">
                <i class="fas fa-hand-pointer"></i> اختبار التحديث اليدوي
            </button>
            
            <button class="btn" onclick="simulateRefreshError()">
                <i class="fas fa-exclamation-triangle"></i> محاكاة خطأ التحديث
            </button>
        </div>
    </div>

    <script>
        let debugLogElement = document.getElementById('debugLog');

        function debugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icons = {
                'info': '🔵',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            };
            
            const logEntry = `${icons[type]} [${timestamp}] ${message}\n`;
            debugLogElement.textContent += logEntry;
            debugLogElement.scrollTop = debugLogElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearDebugLog() {
            debugLogElement.textContent = '';
        }

        function getFrame() {
            return document.getElementById('dashboardFrame');
        }

        function getDashboard() {
            const frame = getFrame();
            return frame.contentWindow.dashboard;
        }

        async function checkDashboardStatus() {
            debugLog('بدء فحص حالة اللوحة...', 'info');
            
            try {
                const frame = getFrame();
                
                // Check frame loading
                if (!frame.contentDocument) {
                    debugLog('الإطار لم يحمل بعد', 'warning');
                    return;
                }
                
                debugLog('✓ الإطار محمل بنجاح', 'success');
                
                // Check dashboard object
                const dashboard = getDashboard();
                if (dashboard) {
                    debugLog('✓ كائن اللوحة متاح', 'success');
                    debugLog(`القسم الحالي: ${dashboard.currentSection}`, 'info');
                    
                    // Check methods
                    const methods = ['refreshData', 'refreshRequests', 'refreshLicenses', 'switchSection'];
                    methods.forEach(method => {
                        if (typeof dashboard[method] === 'function') {
                            debugLog(`✓ دالة ${method} متاحة`, 'success');
                        } else {
                            debugLog(`✗ دالة ${method} غير متاحة`, 'error');
                        }
                    });
                    
                } else {
                    debugLog('✗ كائن اللوحة غير متاح', 'error');
                }
                
            } catch (error) {
                debugLog(`خطأ في فحص اللوحة: ${error.message}`, 'error');
            }
        }

        async function checkRefreshButtons() {
            debugLog('بدء فحص أزرار التحديث...', 'info');
            
            try {
                const frame = getFrame();
                const frameDoc = frame.contentDocument;
                
                if (!frameDoc) {
                    debugLog('لا يمكن الوصول لمحتوى الإطار', 'error');
                    return;
                }
                
                // Check buttons
                const buttons = [
                    { id: 'refreshBtn', name: 'زر التحديث العام' },
                    { id: 'refreshRequestsBtn', name: 'زر تحديث الطلبات' },
                    { id: 'refreshLicensesBtn', name: 'زر تحديث التراخيص' },
                    { id: 'exportLicensesBtn', name: 'زر تصدير التراخيص' }
                ];
                
                buttons.forEach(button => {
                    const element = frameDoc.getElementById(button.id);
                    if (element) {
                        debugLog(`✓ ${button.name} موجود`, 'success');
                        debugLog(`  - النص: ${element.textContent.trim()}`, 'info');
                        debugLog(`  - مفعل: ${!element.disabled}`, element.disabled ? 'warning' : 'success');
                    } else {
                        debugLog(`✗ ${button.name} غير موجود`, 'error');
                    }
                });
                
            } catch (error) {
                debugLog(`خطأ في فحص الأزرار: ${error.message}`, 'error');
            }
        }

        async function checkJavaScriptErrors() {
            debugLog('بدء فحص أخطاء JavaScript...', 'info');
            
            try {
                const frame = getFrame();
                const frameWindow = frame.contentWindow;
                
                // Check for console errors
                const originalError = frameWindow.console.error;
                let errorCount = 0;
                
                frameWindow.console.error = function(...args) {
                    errorCount++;
                    debugLog(`خطأ JavaScript: ${args.join(' ')}`, 'error');
                    originalError.apply(frameWindow.console, args);
                };
                
                debugLog('تم تفعيل مراقبة أخطاء JavaScript', 'info');
                
                // Test a simple operation
                const dashboard = getDashboard();
                if (dashboard) {
                    debugLog('اختبار عملية بسيطة...', 'info');
                    dashboard.updateStats();
                    debugLog('✓ العملية البسيطة نجحت', 'success');
                }
                
                setTimeout(() => {
                    debugLog(`تم رصد ${errorCount} خطأ JavaScript`, errorCount > 0 ? 'warning' : 'success');
                }, 2000);
                
            } catch (error) {
                debugLog(`خطأ في فحص JavaScript: ${error.message}`, 'error');
            }
        }

        async function directRefreshTest() {
            debugLog('بدء اختبار التحديث المباشر...', 'info');
            
            try {
                const dashboard = getDashboard();
                
                if (!dashboard) {
                    debugLog('✗ كائن اللوحة غير متاح', 'error');
                    return;
                }
                
                debugLog('تنفيذ refreshData()...', 'info');
                await dashboard.refreshData();
                debugLog('✅ تم تنفيذ refreshData() بنجاح', 'success');
                
                debugLog('تنفيذ refreshRequests()...', 'info');
                await dashboard.refreshRequests();
                debugLog('✅ تم تنفيذ refreshRequests() بنجاح', 'success');
                
                debugLog('تنفيذ refreshLicenses()...', 'info');
                await dashboard.refreshLicenses();
                debugLog('✅ تم تنفيذ refreshLicenses() بنجاح', 'success');
                
                debugLog('🎉 جميع اختبارات التحديث المباشر نجحت!', 'success');
                
            } catch (error) {
                debugLog(`❌ فشل في اختبار التحديث المباشر: ${error.message}`, 'error');
            }
        }

        async function manualRefreshTest() {
            debugLog('بدء اختبار التحديث اليدوي...', 'info');
            
            try {
                const frame = getFrame();
                const frameDoc = frame.contentDocument;
                
                // Test main refresh button
                const refreshBtn = frameDoc.getElementById('refreshBtn');
                if (refreshBtn) {
                    debugLog('النقر على زر التحديث العام...', 'info');
                    refreshBtn.click();
                    debugLog('✅ تم النقر على زر التحديث العام', 'success');
                } else {
                    debugLog('✗ زر التحديث العام غير موجود', 'error');
                }
                
                // Wait and test requests refresh
                setTimeout(() => {
                    const dashboard = getDashboard();
                    if (dashboard) {
                        dashboard.switchSection('requests');
                        debugLog('تم التبديل إلى قسم الطلبات', 'info');
                        
                        setTimeout(() => {
                            const refreshRequestsBtn = frameDoc.getElementById('refreshRequestsBtn');
                            if (refreshRequestsBtn) {
                                debugLog('النقر على زر تحديث الطلبات...', 'info');
                                refreshRequestsBtn.click();
                                debugLog('✅ تم النقر على زر تحديث الطلبات', 'success');
                            } else {
                                debugLog('✗ زر تحديث الطلبات غير موجود', 'error');
                            }
                        }, 1000);
                    }
                }, 2000);
                
            } catch (error) {
                debugLog(`❌ فشل في اختبار التحديث اليدوي: ${error.message}`, 'error');
            }
        }

        async function simulateRefreshError() {
            debugLog('محاكاة خطأ في التحديث...', 'warning');
            
            try {
                const dashboard = getDashboard();
                
                if (dashboard) {
                    // Temporarily break a method
                    const originalMethod = dashboard.showNotification;
                    dashboard.showNotification = function() {
                        throw new Error('خطأ محاكى في الإشعارات');
                    };
                    
                    debugLog('تم كسر دالة الإشعارات مؤقتاً', 'warning');
                    
                    try {
                        await dashboard.refreshData();
                        debugLog('✗ لم يحدث خطأ كما متوقع', 'warning');
                    } catch (error) {
                        debugLog(`✅ تم رصد الخطأ المحاكى: ${error.message}`, 'success');
                    }
                    
                    // Restore method
                    dashboard.showNotification = originalMethod;
                    debugLog('تم استعادة دالة الإشعارات', 'info');
                }
                
            } catch (error) {
                debugLog(`خطأ في محاكاة الخطأ: ${error.message}`, 'error');
            }
        }

        // Auto-start diagnostics
        window.addEventListener('load', () => {
            setTimeout(() => {
                debugLog('🚀 بدء التشخيص التلقائي...', 'info');
                
                setTimeout(() => {
                    checkDashboardStatus();
                }, 2000);
                
                setTimeout(() => {
                    checkRefreshButtons();
                }, 4000);
                
            }, 1000);
        });
    </script>
</body>
</html>
