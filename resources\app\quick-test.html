<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - نظام توجيه العمليات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار سريع - نظام توجيه العمليات</h1>
        
        <div class="info">
            <strong>الهدف:</strong> التحقق من أن الإصلاحات تعمل بشكل صحيح
        </div>
        
        <button onclick="runQuickTest()">🚀 تشغيل الاختبار السريع</button>
        <button onclick="testOperationRouting()">🔄 اختبار توجيه العمليات</button>
        <button onclick="clearResults()">🧹 مسح النتائج</button>
        
        <div id="results"></div>
    </div>

    <!-- تحميل الملفات بالترتيب الصحيح -->
    <script src="scripts/script.js"></script>
    <script src="scripts/transmission-manager.js"></script>
    
    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        function runQuickTest() {
            clearResults();
            addResult('🔍 بدء الاختبار السريع...', 'info');
            
            // فحص appData
            if (typeof appData !== 'undefined') {
                addResult('✅ appData متاح', 'success');
                
                if (Array.isArray(appData.transmissionTable)) {
                    addResult(`✅ appData.transmissionTable مُهيأ (${appData.transmissionTable.length} عنصر)`, 'success');
                } else {
                    addResult('❌ appData.transmissionTable غير مُهيأ', 'error');
                }
                
                if (Array.isArray(appData.gasCards)) {
                    addResult(`✅ appData.gasCards مُهيأ (${appData.gasCards.length} عنصر)`, 'success');
                } else {
                    addResult('❌ appData.gasCards غير مُهيأ', 'error');
                }
            } else {
                addResult('❌ appData غير متاح', 'error');
                addResult('💡 تأكد من تحميل script.js بشكل صحيح', 'warning');
                return;
            }
            
            // فحص transmissionManager
            if (typeof transmissionManager !== 'undefined' && transmissionManager !== null) {
                addResult('✅ transmissionManager متاح', 'success');
                
                if (Array.isArray(transmissionManager.transmissionData)) {
                    addResult(`✅ transmissionManager.transmissionData مُهيأ (${transmissionManager.transmissionData.length} عنصر)`, 'success');
                } else {
                    addResult('❌ transmissionManager.transmissionData غير مُهيأ', 'error');
                }
            } else {
                addResult('❌ transmissionManager غير متاح', 'error');
                addResult('💡 تأكد من تحميل transmission-manager.js بعد script.js', 'warning');
            }
            
            addResult('🎯 اكتمل الاختبار السريع', 'info');
        }
        
        function testOperationRouting() {
            addResult('🧪 اختبار توجيه العمليات...', 'info');
            
            if (typeof appData === 'undefined') {
                addResult('❌ لا يمكن اختبار التوجيه - appData غير متاح', 'error');
                return;
            }
            
            // اختبار عملية تركيب
            testSingleOperation('تركيب');
            
            // اختبار عملية مراقبة  
            testSingleOperation('مراقبة');
            
            // اختبار عملية تجديد
            testSingleOperation('تجديد');
            
            addResult('✅ اكتمل اختبار توجيه العمليات', 'success');
        }
        
        function testSingleOperation(operationType) {
            const testData = {
                type: operationType,
                tankNumber: 'TEST-' + Date.now(),
                carType: 'سيارة اختبار',
                serialNumber: 'SN-' + Date.now(),
                registrationNumber: 'REG-' + Date.now(),
                ownerName: 'زبون اختبار',
                phoneNumber: '0555000000',
                operationDate: new Date().toISOString().split('T')[0],
                notes: 'اختبار من الصفحة السريعة',
                source: 'quick_test'
            };
            
            if (operationType === 'تجديد') {
                // اختبار بطاقة الغاز
                if (Array.isArray(appData.gasCards)) {
                    const beforeCount = appData.gasCards.length;
                    const cardData = {
                        id: 'test-card-' + Date.now(),
                        customerName: testData.ownerName,
                        operationType: 'تجديد بطاقة',
                        phoneNumber: testData.phoneNumber,
                        createdAt: new Date().toISOString()
                    };
                    appData.gasCards.push(cardData);
                    addResult(`✅ تجديد: تم إضافة بطاقة غاز (${beforeCount} → ${appData.gasCards.length})`, 'success');
                } else {
                    addResult('❌ تجديد: لا يمكن إضافة بطاقة الغاز', 'error');
                }
            } else {
                // اختبار عملية إرسال
                if (typeof transmissionManager !== 'undefined' && transmissionManager !== null) {
                    try {
                        const beforeCount = transmissionManager.transmissionData?.length || 0;
                        const result = transmissionManager.addEntry(testData);
                        const afterCount = transmissionManager.transmissionData?.length || 0;
                        addResult(`✅ ${operationType}: تم إضافة العملية عبر transmissionManager (${beforeCount} → ${afterCount})`, 'success');
                    } catch (error) {
                        addResult(`❌ ${operationType}: خطأ في transmissionManager - ${error.message}`, 'error');
                    }
                } else if (Array.isArray(appData.transmissionTable)) {
                    const beforeCount = appData.transmissionTable.length;
                    const entryData = {
                        ...testData,
                        id: 'test-entry-' + Date.now(),
                        createdAt: new Date().toISOString()
                    };
                    appData.transmissionTable.push(entryData);
                    addResult(`⚠️ ${operationType}: تم إضافة العملية مباشرة إلى appData (${beforeCount} → ${appData.transmissionTable.length})`, 'warning');
                } else {
                    addResult(`❌ ${operationType}: لا يمكن إضافة العملية`, 'error');
                }
            }
        }
        
        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                addResult('🚀 تم تحميل صفحة الاختبار السريع', 'info');
                addResult('📋 اضغط على "تشغيل الاختبار السريع" للبدء', 'info');
            }, 500);
        });
    </script>
</body>
</html>
