<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم إدارة التراخيص - نظام وقود المستقبل</title>
    <link rel="stylesheet" href="admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-gas-pump"></i>
                    <h2>إدارة التراخيص</h2>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active" data-section="overview">
                        <i class="fas fa-chart-dashboard"></i>
                        <span>نظرة عامة</span>
                    </li>
                    <li class="nav-item" data-section="requests">
                        <i class="fas fa-inbox"></i>
                        <span>طلبات التفعيل</span>
                        <span class="badge" id="requestsBadge">0</span>
                    </li>
                    <li class="nav-item" data-section="licenses">
                        <i class="fas fa-key"></i>
                        <span>التراخيص المفعلة</span>
                    </li>
                    <li class="nav-item" data-section="generate">
                        <i class="fas fa-plus-circle"></i>
                        <span>إنشاء ترخيص</span>
                    </li>
                    <li class="nav-item" data-section="statistics">
                        <i class="fas fa-chart-bar"></i>
                        <span>الإحصائيات</span>
                    </li>
                    <li class="nav-item" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <button class="btn btn-danger" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="content-header">
                <div class="header-left">
                    <h1 id="pageTitle">نظرة عامة</h1>
                    <p id="pageDescription">إدارة شاملة لتراخيص النظام</p>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <div class="user-info">
                        <span>مرحباً، المدير</span>
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </div>

            <!-- Overview Section -->
            <div class="content-section active" id="overview-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pendingRequestsCount">0</h3>
                            <p>طلبات معلقة</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeLicensesCount">0</h3>
                            <p>تراخيص مفعلة</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="trialLicensesCount">0</h3>
                            <p>تراخيص تجريبية</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-infinity"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="lifetimeLicensesCount">0</h3>
                            <p>تراخيص مدى الحياة</p>
                        </div>
                    </div>
                </div>

                <div class="recent-activity">
                    <h3>النشاط الأخير</h3>
                    <div class="activity-list" id="recentActivity">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Activation Requests Section -->
            <div class="content-section" id="requests-section">
                <div class="section-header">
                    <h3>طلبات التفعيل</h3>
                    <div class="section-actions">
                        <button class="btn btn-secondary" id="refreshRequestsBtn">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                    </div>
                </div>
                
                <div class="requests-container">
                    <div class="requests-list" id="requestsList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Active Licenses Section -->
            <div class="content-section" id="licenses-section">
                <div class="section-header">
                    <h3>التراخيص المفعلة</h3>
                    <div class="section-actions">
                        <button class="btn btn-secondary" id="refreshLicensesBtn">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                        <button class="btn btn-info" id="exportLicensesBtn">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>
                
                <div class="licenses-container">
                    <div class="licenses-list" id="licensesList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Generate License Section -->
            <div class="content-section" id="generate-section">
                <div class="section-header">
                    <h3>إنشاء ترخيص جديد</h3>
                </div>
                
                <div class="generate-form-container">
                    <form id="generateLicenseForm" class="generate-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="clientName">اسم العميل *</label>
                                <input type="text" id="clientName" name="clientName" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="clientPhone">رقم الهاتف *</label>
                                <input type="tel" id="clientPhone" name="clientPhone" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="licenseTypeGenerate">نوع الترخيص *</label>
                                <select id="licenseTypeGenerate" name="licenseType" required>
                                    <option value="">اختر نوع الترخيص</option>
                                    <option value="trial">تجريبي (30 يوم)</option>
                                    <option value="lifetime">مدى الحياة</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="customDuration">مدة مخصصة (أيام)</label>
                                <input type="number" id="customDuration" name="customDuration" min="1" max="365">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="generateNotes">ملاحظات</label>
                            <textarea id="generateNotes" name="notes" rows="3"></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i>
                                إنشاء الترخيص
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="content-section" id="statistics-section">
                <div class="section-header">
                    <h3>الإحصائيات والتقارير</h3>
                </div>
                
                <div class="statistics-container">
                    <div class="chart-container">
                        <canvas id="licensesChart"></canvas>
                    </div>
                    
                    <div class="stats-details">
                        <div class="stats-table">
                            <h4>إحصائيات مفصلة</h4>
                            <table id="statsTable">
                                <thead>
                                    <tr>
                                        <th>النوع</th>
                                        <th>العدد</th>
                                        <th>النسبة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div class="content-section" id="settings-section">
                <div class="section-header">
                    <h3>إعدادات النظام</h3>
                </div>
                
                <div class="settings-container">
                    <div class="settings-form">
                        <div class="setting-group">
                            <h4>إعدادات التراخيص</h4>
                            <div class="setting-item">
                                <label for="defaultTrialDays">مدة الترخيص التجريبي الافتراضية (أيام)</label>
                                <input type="number" id="defaultTrialDays" value="30" min="1" max="365">
                            </div>
                            
                            <div class="setting-item">
                                <label for="autoApproval">الموافقة التلقائية على الطلبات</label>
                                <input type="checkbox" id="autoApproval">
                            </div>
                        </div>
                        
                        <div class="setting-group">
                            <h4>إعدادات الإشعارات</h4>
                            <div class="setting-item">
                                <label for="emailNotifications">إشعارات البريد الإلكتروني</label>
                                <input type="checkbox" id="emailNotifications">
                            </div>
                            
                            <div class="setting-item">
                                <label for="expiryWarning">تحذير انتهاء الصلاحية (أيام قبل الانتهاء)</label>
                                <input type="number" id="expiryWarning" value="7" min="1" max="30">
                            </div>
                        </div>
                        
                        <div class="setting-actions">
                            <button class="btn btn-primary" id="saveSettingsBtn">
                                <i class="fas fa-save"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Request Details Modal -->
    <div class="modal" id="requestModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل طلب التفعيل</h3>
                <button class="close-btn" id="closeRequestModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="requestModalBody">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" id="approveRequestBtn">
                    <i class="fas fa-check"></i>
                    موافقة
                </button>
                <button class="btn btn-danger" id="rejectRequestBtn">
                    <i class="fas fa-times"></i>
                    رفض
                </button>
                <button class="btn btn-secondary" id="cancelRequestModal">
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري المعالجة...</p>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification" style="display: none;">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="admin-dashboard.js"></script>

    <!-- Navigation Fix Script -->
    <script>
        // Backup navigation function
        function manualSwitchSection(section) {
            console.log('Manual switch to:', section);

            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            const navItem = document.querySelector(`[data-section="${section}"]`);
            if (navItem) {
                navItem.classList.add('active');
            }

            // Update content
            document.querySelectorAll('.content-section').forEach(contentSection => {
                contentSection.classList.remove('active');
            });
            const targetSection = document.getElementById(`${section}-section`);
            if (targetSection) {
                targetSection.classList.add('active');
                console.log('Successfully switched to:', section);
            } else {
                console.error('Section not found:', `${section}-section`);
            }
        }

        // Manual event binding as backup
        function bindNavigationEvents() {
            console.log('Binding navigation events manually...');

            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.dataset.section;
                    console.log('Nav clicked:', section);

                    // Try dashboard method first, fallback to manual
                    if (window.dashboard && typeof window.dashboard.switchSection === 'function') {
                        window.dashboard.switchSection(section);
                    } else {
                        manualSwitchSection(section);
                    }
                });
            });

            console.log('Navigation events bound successfully');
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM loaded, initializing navigation...');
                setTimeout(bindNavigationEvents, 500);
            });
        } else {
            console.log('DOM already loaded, initializing navigation...');
            setTimeout(bindNavigationEvents, 500);
        }

        // Test function
        function testNavigation() {
            console.log('Testing navigation...');
            const sections = ['overview', 'requests', 'licenses', 'generate', 'statistics', 'settings'];

            sections.forEach((section, index) => {
                setTimeout(() => {
                    console.log(`Testing section: ${section}`);
                    manualSwitchSection(section);
                }, index * 1000);
            });
        }

        // Add test button
        window.addEventListener('load', () => {
            setTimeout(() => {
                const header = document.querySelector('.content-header');
                if (header) {
                    const testBtn = document.createElement('button');
                    testBtn.textContent = 'اختبار التنقل';
                    testBtn.className = 'btn btn-secondary';
                    testBtn.onclick = testNavigation;
                    testBtn.style.marginLeft = '10px';
                    header.appendChild(testBtn);
                }
            }, 1000);
        });
    </script>
</body>
</html>
