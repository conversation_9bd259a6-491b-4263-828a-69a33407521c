<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الحفظ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.primary {
            background-color: #28a745;
        }
        .btn.primary:hover {
            background-color: #218838;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            position: relative;
        }
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
        .form-buttons {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار زر الحفظ</h1>
        
        <!-- زر فتح النموذج -->
        <button type="button" id="add-customer-btn" class="btn primary">
            <i class="fas fa-plus"></i> إضافة زبون جديد
        </button>
        
        <button type="button" id="clear-log" class="btn">مسح السجل</button>
        
        <div id="log" class="log"></div>
    </div>

    <!-- نافذة النموذج -->
    <div class="modal" id="customer-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="customer-modal-title">إضافة زبون جديد</h2>
            
            <form id="customer-form">
                <input type="hidden" id="customer-id">
                
                <div class="form-group">
                    <label for="customer-name">اسم العميل: <span style="color: red;">*</span></label>
                    <input type="text" id="customer-name" required>
                </div>
                
                <div class="form-group">
                    <label for="customer-phone">رقم الهاتف: <span style="color: red;">*</span></label>
                    <input type="text" id="customer-phone" required>
                </div>
                
                <div class="form-group">
                    <label for="customer-address">العنوان: <span style="color: red;">*</span></label>
                    <textarea id="customer-address" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="customer-notes">ملاحظات:</label>
                    <textarea id="customer-notes"></textarea>
                </div>
                
                <!-- بيانات السيارة المبسطة -->
                <div class="form-group">
                    <label for="vehicle-plate-number">رقم اللوحة: <span style="color: red;">*</span></label>
                    <input type="text" id="vehicle-plate-number" required>
                </div>
                
                <div class="form-group">
                    <label for="vehicle-brand">ماركة السيارة: <span style="color: red;">*</span></label>
                    <input type="text" id="vehicle-brand" required>
                </div>
                
                <div class="form-group">
                    <label for="vehicle-model">موديل السيارة: <span style="color: red;">*</span></label>
                    <input type="text" id="vehicle-model" required>
                </div>
                
                <!-- بيانات الخزان المبسطة -->
                <div class="form-group">
                    <label for="tank-type">نوع الخزان: <span style="color: red;">*</span></label>
                    <select id="tank-type" required>
                        <option value="">اختر نوع الخزان</option>
                        <option value="أسطواني">أسطواني</option>
                        <option value="دائري">دائري</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="tank-brand">ماركة الخزان: <span style="color: red;">*</span></label>
                    <input type="text" id="tank-brand" required>
                </div>
                
                <div class="form-group">
                    <label for="tank-serial-number">الرقم التسلسلي: <span style="color: red;">*</span></label>
                    <input type="text" id="tank-serial-number" required>
                </div>
                
                <div class="form-group">
                    <label for="tank-capacity">سعة الخزان (لتر): <span style="color: red;">*</span></label>
                    <input type="number" id="tank-capacity" required>
                </div>
                
                <div class="form-buttons">
                    <button type="submit" class="btn primary">
                        <i class="fas fa-save"></i> حفظ البيانات
                    </button>
                    <button type="button" class="btn" id="cancel-customer">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // متغير البيانات التجريبي
        let appData = {
            customers: [],
            vehicles: [],
            gasTanks: []
        };

        // دالة إضافة رسالة إلى السجل
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // دالة توليد معرف فريد
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // دالة حفظ البيانات (محاكاة)
        async function saveData() {
            try {
                addLog('🔄 بدء عملية حفظ البيانات...');
                
                // محاكاة تأخير الحفظ
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // حفظ في localStorage
                localStorage.setItem('testAppData', JSON.stringify(appData));
                addLog('✅ تم حفظ البيانات بنجاح', 'success');
                return true;
                
            } catch (error) {
                addLog(`❌ خطأ في حفظ البيانات: ${error.message}`, 'error');
                return false;
            }
        }

        // دالة إظهار رسالة
        function showToast(message, success = true) {
            addLog(success ? `✅ ${message}` : `❌ ${message}`, success ? 'success' : 'error');
        }

        // إعداد النموذج
        function setupCustomerForm() {
            addLog('🔧 بدء إعداد نموذج الزبون...');
            
            const addCustomerBtn = document.getElementById('add-customer-btn');
            const customerModal = document.getElementById('customer-modal');
            const customerForm = document.getElementById('customer-form');
            const cancelCustomerBtn = document.getElementById('cancel-customer');
            const closeBtn = customerModal?.querySelector('.close');
            
            // التحقق من وجود العناصر
            addLog('🔍 فحص العناصر:');
            addLog(`  addCustomerBtn: ${addCustomerBtn ? '✅ موجود' : '❌ غير موجود'}`);
            addLog(`  customerModal: ${customerModal ? '✅ موجود' : '❌ غير موجود'}`);
            addLog(`  customerForm: ${customerForm ? '✅ موجود' : '❌ غير موجود'}`);
            addLog(`  cancelCustomerBtn: ${cancelCustomerBtn ? '✅ موجود' : '❌ غير موجود'}`);
            addLog(`  closeBtn: ${closeBtn ? '✅ موجود' : '❌ غير موجود'}`);
            
            if (!addCustomerBtn || !customerModal || !customerForm) {
                addLog('❌ عناصر مطلوبة غير موجودة في نموذج الزبون', 'error');
                return;
            }

            // فتح النموذج عند النقر على زر الإضافة
            addCustomerBtn.addEventListener('click', () => {
                addLog('🖱️ تم النقر على زر إضافة زبون جديد');
                document.getElementById('customer-modal-title').textContent = 'إضافة زبون جديد';
                customerForm.reset();
                document.getElementById('customer-id').value = '';
                customerModal.style.display = 'block';
            });

            // إغلاق النموذج
            if (cancelCustomerBtn) {
                cancelCustomerBtn.addEventListener('click', () => {
                    addLog('🖱️ تم النقر على زر الإلغاء');
                    customerModal.style.display = 'none';
                });
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    addLog('🖱️ تم النقر على زر الإغلاق');
                    customerModal.style.display = 'none';
                });
            }

            // معالجة تقديم النموذج
            addLog('📝 تم تسجيل معالج تقديم نموذج الزبون');
            customerForm.addEventListener('submit', async (e) => {
                addLog('🚀 تم تقديم نموذج الزبون!');
                e.preventDefault();

                const name = document.getElementById('customer-name').value;
                const phone = document.getElementById('customer-phone').value;
                const address = document.getElementById('customer-address').value;
                const notes = document.getElementById('customer-notes').value;

                // بيانات السيارة
                const plateNumber = document.getElementById('vehicle-plate-number').value;
                const brand = document.getElementById('vehicle-brand').value;
                const model = document.getElementById('vehicle-model').value;

                // بيانات الخزان
                const tankType = document.getElementById('tank-type').value;
                const tankBrand = document.getElementById('tank-brand').value;
                const tankSerialNumber = document.getElementById('tank-serial-number').value;
                const tankCapacity = document.getElementById('tank-capacity').value;

                if (!name || !phone || !address || !plateNumber || !brand || !model || !tankType || !tankBrand || !tankSerialNumber || !tankCapacity) {
                    showToast('يرجى ملء جميع الحقول المطلوبة المميزة بـ *', false);
                    return;
                }

                // إنشاء بيانات العميل
                const customerId = generateId();
                const vehicleId = generateId();
                const tankId = generateId();

                const customer = {
                    id: customerId,
                    name,
                    phone,
                    address,
                    notes,
                    createdAt: new Date().toISOString()
                };

                const vehicle = {
                    id: vehicleId,
                    plateNumber,
                    brand,
                    model,
                    customerId,
                    createdAt: new Date().toISOString()
                };

                const gasTank = {
                    id: tankId,
                    vehicleId,
                    type: tankType,
                    brand: tankBrand,
                    serialNumber: tankSerialNumber,
                    capacity: parseFloat(tankCapacity),
                    createdAt: new Date().toISOString()
                };

                // إضافة البيانات
                appData.customers.push(customer);
                appData.vehicles.push(vehicle);
                appData.gasTanks.push(gasTank);

                addLog(`➕ تم إضافة عميل جديد: ${name}`);
                addLog(`🚗 تم إضافة سيارة: ${plateNumber}`);
                addLog(`⛽ تم إضافة خزان: ${tankSerialNumber}`);

                // حفظ البيانات
                const saveResult = await saveData();
                if (saveResult) {
                    showToast('تم حفظ البيانات بنجاح!', true);
                    addLog(`📊 إجمالي العملاء: ${appData.customers.length}`);
                    // إغلاق النموذج
                    customerModal.style.display = 'none';
                } else {
                    showToast('فشل في حفظ البيانات. يرجى المحاولة مرة أخرى.', false);
                }
            });
            
            addLog('✅ تم إعداد نموذج الزبون بنجاح');
        }

        // زر مسح السجل
        document.getElementById('clear-log').addEventListener('click', () => {
            document.getElementById('log').innerHTML = '';
        });

        // تحميل البيانات عند بدء الصفحة
        window.addEventListener('load', () => {
            addLog('🚀 تم تحميل صفحة الاختبار');
            setupCustomerForm();
        });
    </script>
</body>
</html>
