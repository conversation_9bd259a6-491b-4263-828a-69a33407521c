<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الجديد</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار النظام الجديد - نظام التفعيل والترخيص</h1>
        
        <div class="test-section">
            <h3>1. اختبار تحميل الملفات الأساسية</h3>
            <button onclick="testFileLoading()">اختبار تحميل الملفات</button>
            <div id="fileLoadingResults"></div>
        </div>
        
        <div class="test-section">
            <h3>2. اختبار نظام التفعيل</h3>
            <button onclick="testActivationSystem()">اختبار نظام التفعيل</button>
            <div id="activationResults"></div>
        </div>
        
        <div class="test-section">
            <h3>3. اختبار إدارة التراخيص</h3>
            <button onclick="testLicenseManagement()">اختبار إدارة التراخيص</button>
            <div id="licenseResults"></div>
        </div>
        
        <div class="test-section">
            <h3>4. اختبار البيانات الجزائرية</h3>
            <button onclick="testAlgerianData()">اختبار البيانات الجزائرية</button>
            <div id="algerianDataResults"></div>
        </div>
        
        <div class="test-section">
            <h3>5. اختبار التكامل مع Electron</h3>
            <button onclick="testElectronIntegration()">اختبار التكامل</button>
            <div id="electronResults"></div>
        </div>
    </div>

    <script>
        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testFileLoading() {
            clearResults('fileLoadingResults');
            
            const files = [
                'src/new-login.html',
                'src/license-management/new-dashboard.html',
                'src/license-management/new-dashboard.js',
                'src/data/algeria-states-municipalities.js',
                'test-new-system.html'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        showResult('fileLoadingResults', `✅ ${file} - تم التحميل بنجاح`, 'success');
                    } else {
                        showResult('fileLoadingResults', `❌ ${file} - فشل التحميل (${response.status})`, 'error');
                    }
                } catch (error) {
                    showResult('fileLoadingResults', `❌ ${file} - خطأ: ${error.message}`, 'error');
                }
            }
        }

        function testActivationSystem() {
            clearResults('activationResults');
            
            try {
                // اختبار تخزين طلب التفعيل
                const testRequest = {
                    fullName: 'اختبار النظام',
                    phone: '**********',
                    state: 'الجزائر',
                    municipality: 'الجزائر الوسطى',
                    licenseType: 'trial'
                };
                
                localStorage.setItem('testActivationRequest', JSON.stringify(testRequest));
                const stored = localStorage.getItem('testActivationRequest');
                
                if (stored) {
                    showResult('activationResults', '✅ تخزين طلب التفعيل يعمل بشكل صحيح', 'success');
                } else {
                    showResult('activationResults', '❌ فشل في تخزين طلب التفعيل', 'error');
                }
                
                // اختبار تفعيل ترخيص تجريبي
                const testLicense = {
                    key: 'TRIAL-TEST1234-ABCD',
                    isActive: true,
                    activationDate: new Date().toISOString(),
                    expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    type: 'trial'
                };
                
                localStorage.setItem('activeLicense', JSON.stringify(testLicense));
                showResult('activationResults', '✅ تفعيل الترخيص التجريبي يعمل بشكل صحيح', 'success');
                
            } catch (error) {
                showResult('activationResults', `❌ خطأ في نظام التفعيل: ${error.message}`, 'error');
            }
        }

        function testLicenseManagement() {
            clearResults('licenseResults');
            
            try {
                // اختبار إنشاء ترخيص
                const testLicenses = [
                    {
                        id: 'test1',
                        key: 'LIFE-ADMIN123-XYZ',
                        clientName: 'عميل تجريبي 1',
                        type: 'lifetime',
                        isActive: true,
                        activationDate: new Date().toISOString(),
                        expiryDate: 'مدى الحياة'
                    },
                    {
                        id: 'test2',
                        key: 'TRIAL-USER456-ABC',
                        clientName: 'عميل تجريبي 2',
                        type: 'trial',
                        isActive: true,
                        activationDate: new Date().toISOString(),
                        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                    }
                ];
                
                localStorage.setItem('activeLicenses', JSON.stringify(testLicenses));
                showResult('licenseResults', '✅ إنشاء وحفظ التراخيص يعمل بشكل صحيح', 'success');
                
                // اختبار إحصائيات التراخيص
                const activeLicenses = testLicenses.filter(lic => lic.isActive).length;
                const lifetimeLicenses = testLicenses.filter(lic => lic.type === 'lifetime').length;
                const trialLicenses = testLicenses.filter(lic => lic.type === 'trial').length;
                
                showResult('licenseResults', `📊 الإحصائيات: ${activeLicenses} ترخيص مفعل، ${lifetimeLicenses} مدى الحياة، ${trialLicenses} تجريبي`, 'success');
                
            } catch (error) {
                showResult('licenseResults', `❌ خطأ في إدارة التراخيص: ${error.message}`, 'error');
            }
        }

        async function testAlgerianData() {
            clearResults('algerianDataResults');
            
            try {
                const response = await fetch('src/data/algeria-states-municipalities.js');
                const text = await response.text();
                
                if (text.includes('الجزائر') && text.includes('وهران') && text.includes('قسنطينة')) {
                    showResult('algerianDataResults', '✅ بيانات الولايات والبلديات الجزائرية متوفرة', 'success');
                } else {
                    showResult('algerianDataResults', '⚠️ بيانات الولايات والبلديات قد تكون ناقصة', 'warning');
                }
                
                // اختبار عدد الولايات
                const stateMatches = text.match(/name:\s*['"][^'"]*['"]/g);
                if (stateMatches && stateMatches.length >= 15) {
                    showResult('algerianDataResults', `✅ تم العثور على ${stateMatches.length} ولاية`, 'success');
                } else {
                    showResult('algerianDataResults', '⚠️ عدد الولايات أقل من المتوقع', 'warning');
                }
                
            } catch (error) {
                showResult('algerianDataResults', `❌ خطأ في تحميل البيانات الجزائرية: ${error.message}`, 'error');
            }
        }

        function testElectronIntegration() {
            clearResults('electronResults');
            
            if (typeof window !== 'undefined' && window.electronAPI) {
                showResult('electronResults', '✅ Electron API متوفر', 'success');
                
                // اختبار الوظائف المتوفرة
                const expectedFunctions = [
                    'activateLicense',
                    'submitActivationRequest',
                    'checkActivationStatus',
                    'openAdminDashboard',
                    'redirectToAuth'
                ];
                
                expectedFunctions.forEach(func => {
                    if (typeof window.electronAPI[func] === 'function') {
                        showResult('electronResults', `✅ ${func} متوفر`, 'success');
                    } else {
                        showResult('electronResults', `❌ ${func} غير متوفر`, 'error');
                    }
                });
                
            } else {
                showResult('electronResults', '⚠️ Electron API غير متوفر (يعمل في المتصفح)', 'warning');
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', () => {
            showResult('fileLoadingResults', '🚀 بدء اختبار النظام الجديد...', 'success');
        });
    </script>
</body>
</html>
