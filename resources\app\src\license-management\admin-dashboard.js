// نظام إدارة التراخيص - لوحة التحكم
class LicenseManagementDashboard {
    constructor() {
        this.currentSection = 'overview';
        this.activationRequests = [];
        this.activeLicenses = [];
        this.settings = this.loadSettings();

        this.init();
        this.loadData();
        this.initializeSampleData();
    }

    // إضافة بيانات تجريبية للعرض
    initializeSampleData() {
        // إضافة طلبات تفعيل تجريبية
        if (this.activationRequests.length === 0) {
            this.activationRequests = [
                {
                    id: 'req_001',
                    userName: 'أحمد محمد علي',
                    userPhone: '**********',
                    userState: 'الجزائر',
                    userMunicipality: 'الجزائر الوسطى',
                    requestDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                    status: 'pending',
                    deviceInfo: 'Windows 10 - Chrome 120.0.0.0'
                },
                {
                    id: 'req_002',
                    userName: 'فاطمة بن عيسى',
                    userPhone: '0666789012',
                    userState: 'وهران',
                    userMunicipality: 'وهران',
                    requestDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    status: 'pending',
                    deviceInfo: 'Windows 11 - Edge 120.0.0.0'
                },
                {
                    id: 'req_003',
                    userName: 'محمد الطاهر بوعلام',
                    userPhone: '**********',
                    userState: 'قسنطينة',
                    userMunicipality: 'قسنطينة',
                    requestDate: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
                    status: 'pending',
                    deviceInfo: 'Windows 10 - Firefox *********'
                }
            ];
            this.saveActivationRequests();
        }

        // إضافة تراخيص مفعلة تجريبية
        if (this.activeLicenses.length === 0) {
            this.activeLicenses = [
                {
                    id: 'lic_001',
                    key: 'LIFE-ABC12345-XYZ789',
                    clientName: 'خالد بن سعد',
                    clientPhone: '**********',
                    type: 'lifetime',
                    isActive: true,
                    activationDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
                    expiryDate: 'مدى الحياة',
                    notes: 'ترخيص مدى الحياة للعميل المميز',
                    createdBy: 'admin',
                    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
                },
                {
                    id: 'lic_002',
                    key: 'TRIAL-DEF67890-ABC123',
                    clientName: 'سارة أحمد',
                    clientPhone: '**********',
                    type: 'trial',
                    isActive: true,
                    activationDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
                    expiryDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
                    notes: 'ترخيص تجريبي لمدة 30 يوم',
                    createdBy: 'admin',
                    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
                },
                {
                    id: 'lic_003',
                    key: 'TRIAL-GHI45678-DEF456',
                    clientName: 'عبد الرحمن مصطفى',
                    clientPhone: '0777456123',
                    type: 'trial',
                    isActive: true,
                    activationDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
                    expiryDate: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),
                    notes: 'ترخيص تجريبي - عميل جديد',
                    createdBy: 'admin',
                    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
                },
                {
                    id: 'lic_004',
                    key: 'LIFE-JKL78901-GHI789',
                    clientName: 'نادية بوزيد',
                    clientPhone: '0555321654',
                    type: 'lifetime',
                    isActive: true,
                    activationDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
                    expiryDate: 'مدى الحياة',
                    notes: 'ترخيص مدى الحياة - عميل VIP',
                    createdBy: 'admin',
                    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()
                }
            ];
            this.saveActiveLicenses();
        }

        // إضافة نشاط حديث تجريبي
        this.recentActivity = [
            {
                type: 'license_generated',
                description: 'تم إنشاء ترخيص جديد للعميل عبد الرحمن مصطفى',
                date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
                icon: 'fas fa-key',
                color: 'success'
            },
            {
                type: 'request_approved',
                description: 'تم الموافقة على طلب تفعيل سارة أحمد',
                date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
                icon: 'fas fa-check-circle',
                color: 'success'
            },
            {
                type: 'license_generated',
                description: 'تم إنشاء ترخيص مدى الحياة للعميل خالد بن سعد',
                date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
                icon: 'fas fa-infinity',
                color: 'info'
            },
            {
                type: 'request_received',
                description: 'طلب تفعيل جديد من محمد الطاهر بوعلام',
                date: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
                icon: 'fas fa-inbox',
                color: 'warning'
            },
            {
                type: 'license_generated',
                description: 'تم إنشاء ترخيص مدى الحياة للعميل نادية بوزيد',
                date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
                icon: 'fas fa-infinity',
                color: 'info'
            }
        ];
    }

    init() {
        this.bindEvents();
        this.updateStats();
        this.loadRecentActivity();
    }

    bindEvents() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // Refresh buttons
        document.getElementById('refreshBtn').addEventListener('click', () => this.refreshData());
        document.getElementById('refreshRequestsBtn').addEventListener('click', () => this.loadActivationRequests());
        document.getElementById('refreshLicensesBtn').addEventListener('click', () => this.loadActiveLicenses());

        // Generate license form
        document.getElementById('generateLicenseForm').addEventListener('submit', (e) => this.handleGenerateLicense(e));

        // Settings
        document.getElementById('saveSettingsBtn').addEventListener('click', () => this.saveSettings());

        // Modal events
        document.getElementById('closeRequestModal').addEventListener('click', () => this.hideModal('requestModal'));
        document.getElementById('cancelRequestModal').addEventListener('click', () => this.hideModal('requestModal'));
        document.getElementById('approveRequestBtn').addEventListener('click', () => this.approveRequest());
        document.getElementById('rejectRequestBtn').addEventListener('click', () => this.rejectRequest());

        // Logout
        document.getElementById('logoutBtn').addEventListener('click', () => this.logout());
    }

    switchSection(section) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${section}-section`).classList.add('active');

        // Update page title
        const titles = {
            overview: 'نظرة عامة',
            requests: 'طلبات التفعيل',
            licenses: 'التراخيص المفعلة',
            generate: 'إنشاء ترخيص',
            statistics: 'الإحصائيات',
            settings: 'الإعدادات'
        };

        const descriptions = {
            overview: 'إدارة شاملة لتراخيص النظام',
            requests: 'مراجعة والموافقة على طلبات التفعيل',
            licenses: 'إدارة التراخيص المفعلة والمنتهية الصلاحية',
            generate: 'إنشاء تراخيص جديدة للعملاء',
            statistics: 'تقارير وإحصائيات مفصلة',
            settings: 'إعدادات النظام والتفضيلات'
        };

        document.getElementById('pageTitle').textContent = titles[section];
        document.getElementById('pageDescription').textContent = descriptions[section];

        this.currentSection = section;

        // Load section-specific data
        this.loadSectionData(section);
    }

    loadSectionData(section) {
        switch(section) {
            case 'requests':
                this.loadActivationRequests();
                break;
            case 'licenses':
                this.loadActiveLicenses();
                break;
            case 'statistics':
                this.loadStatistics();
                break;
            case 'settings':
                this.loadSettingsForm();
                break;
        }
    }

    async loadData() {
        this.showLoading();

        try {
            // Load activation requests
            this.activationRequests = this.getActivationRequests();

            // Load active licenses
            this.activeLicenses = this.getActiveLicenses();

            this.updateStats();
            this.updateRequestsBadge();

        } catch (error) {
            this.showNotification('خطأ في تحميل البيانات', 'error');
        } finally {
            this.hideLoading();
        }
    }

    refreshData() {
        this.loadData();
        this.showNotification('تم تحديث البيانات', 'success');
    }

    updateStats() {
        const pendingRequests = this.activationRequests.filter(req => req.status === 'pending').length;
        const activeLicenses = this.activeLicenses.filter(lic => lic.isActive).length;
        const trialLicenses = this.activeLicenses.filter(lic => lic.type === 'trial' && lic.isActive).length;
        const lifetimeLicenses = this.activeLicenses.filter(lic => lic.type === 'lifetime' && lic.isActive).length;

        document.getElementById('pendingRequestsCount').textContent = pendingRequests;
        document.getElementById('activeLicensesCount').textContent = activeLicenses;
        document.getElementById('trialLicensesCount').textContent = trialLicenses;
        document.getElementById('lifetimeLicensesCount').textContent = lifetimeLicenses;
    }

    updateRequestsBadge() {
        const pendingCount = this.activationRequests.filter(req => req.status === 'pending').length;
        document.getElementById('requestsBadge').textContent = pendingCount;
    }

    loadRecentActivity() {
        const activityContainer = document.getElementById('recentActivity');
        const activities = this.getRecentActivities();

        if (activities.length === 0) {
            activityContainer.innerHTML = '<p class="text-muted">لا توجد أنشطة حديثة</p>';
            return;
        }

        const activitiesHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon" style="background: ${this.getActivityColor(activity.type)}">
                    <i class="${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <p>${activity.description}</p>
                    <small>${this.formatDate(activity.date)}</small>
                </div>
            </div>
        `).join('');

        activityContainer.innerHTML = activitiesHTML;
    }

    getRecentActivities() {
        // استخدام النشاط المحفوظ أو البيانات التجريبية
        const savedActivity = localStorage.getItem('recentActivity');
        if (savedActivity) {
            return JSON.parse(savedActivity).slice(0, 5);
        }

        // إرجاع النشاط التجريبي إذا لم يكن هناك نشاط محفوظ
        return this.recentActivity ? this.recentActivity.slice(0, 5) : [];
    }

    getActivityIcon(type) {
        const icons = {
            'license_generated': 'fas fa-key',
            'request_approved': 'fas fa-check-circle',
            'request_rejected': 'fas fa-times-circle',
            'request_received': 'fas fa-inbox',
            'license_revoked': 'fas fa-ban',
            'settings_updated': 'fas fa-cog'
        };
        return icons[type] || 'fas fa-info-circle';
    }

    getActivityColor(type) {
        const colors = {
            'license_generated': '#27ae60',
            'request_approved': '#27ae60',
            'request_rejected': '#e74c3c',
            'request_received': '#3498db',
            'license_revoked': '#f39c12',
            'settings_updated': '#3498db'
        };
        return colors[type] || '#95a5a6';
    }

    loadActivationRequests() {
        const requestsList = document.getElementById('requestsList');
        const requests = this.activationRequests;

        if (requests.length === 0) {
            requestsList.innerHTML = '<div class="empty-state"><p>لا توجد طلبات تفعيل</p></div>';
            return;
        }

        const requestsHTML = requests.map(request => `
            <div class="request-item" data-request-id="${request.id}">
                <div class="item-info">
                    <h4>${request.fullName}</h4>
                    <p><i class="fas fa-phone"></i> ${request.phone}</p>
                    <p><i class="fas fa-map-marker-alt"></i> ${request.state} - ${request.municipality}</p>
                    <p><i class="fas fa-calendar"></i> ${this.formatDate(request.requestDate)}</p>
                    <span class="status-badge status-${request.status}">${this.getStatusText(request.status)}</span>
                </div>
                <div class="item-actions">
                    <button class="btn btn-info btn-sm" onclick="dashboard.viewRequestDetails('${request.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    ${request.status === 'pending' ? `
                        <button class="btn btn-success btn-sm" onclick="dashboard.quickApprove('${request.id}')">
                            <i class="fas fa-check"></i> موافقة
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="dashboard.quickReject('${request.id}')">
                            <i class="fas fa-times"></i> رفض
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');

        requestsList.innerHTML = requestsHTML;
    }

    loadActiveLicenses() {
        const licensesList = document.getElementById('licensesList');
        const licenses = this.activeLicenses;

        if (licenses.length === 0) {
            licensesList.innerHTML = '<div class="empty-state"><p>لا توجد تراخيص مفعلة</p></div>';
            return;
        }

        const licensesHTML = licenses.map(license => `
            <div class="license-item" data-license-id="${license.id}">
                <div class="item-info">
                    <h4>${license.clientName || 'غير محدد'}</h4>
                    <p><i class="fas fa-key"></i> ${license.key}</p>
                    <p><i class="fas fa-tag"></i> ${license.type === 'trial' ? 'تجريبي' : 'مدى الحياة'}</p>
                    <p><i class="fas fa-calendar"></i> تاريخ التفعيل: ${this.formatDate(license.activationDate)}</p>
                    <p><i class="fas fa-clock"></i> انتهاء الصلاحية: ${license.expiryDate}</p>
                    <span class="status-badge status-${license.isActive ? 'active' : 'expired'}">
                        ${license.isActive ? 'مفعل' : 'منتهي الصلاحية'}
                    </span>
                </div>
                <div class="item-actions">
                    <button class="btn btn-info btn-sm" onclick="dashboard.viewLicenseDetails('${license.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    ${license.isActive ? `
                        <button class="btn btn-warning btn-sm" onclick="dashboard.extendLicense('${license.id}')">
                            <i class="fas fa-clock"></i> تمديد
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="dashboard.revokeLicense('${license.id}')">
                            <i class="fas fa-ban"></i> إلغاء
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');

        licensesList.innerHTML = licensesHTML;
    }

    async handleGenerateLicense(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const licenseData = {
            clientName: formData.get('clientName'),
            clientPhone: formData.get('clientPhone'),
            type: formData.get('licenseType'),
            customDuration: formData.get('customDuration'),
            notes: formData.get('notes')
        };

        this.showLoading();

        try {
            const newLicense = this.generateLicense(licenseData);
            this.activeLicenses.push(newLicense);
            this.saveActiveLicenses();

            this.showNotification(`تم إنشاء الترخيص بنجاح: ${newLicense.key}`, 'success');
            e.target.reset();

            // Add to recent activity
            this.addActivity({
                type: 'license_generated',
                description: `تم إنشاء ترخيص جديد للعميل ${licenseData.clientName}`,
                date: new Date().toISOString()
            });

            this.updateStats();

        } catch (error) {
            this.showNotification('خطأ في إنشاء الترخيص', 'error');
        } finally {
            this.hideLoading();
        }
    }

    generateLicense(data) {
        const licenseKey = this.generateLicenseKey(data.type);
        const isLifetime = data.type === 'lifetime';
        const duration = data.customDuration ? parseInt(data.customDuration) : (isLifetime ? null : 30);

        return {
            id: this.generateId(),
            key: licenseKey,
            clientName: data.clientName,
            clientPhone: data.clientPhone,
            type: data.type,
            isActive: true,
            activationDate: new Date().toISOString(),
            expiryDate: isLifetime ? 'مدى الحياة' : this.calculateExpiryDate(duration),
            notes: data.notes,
            createdBy: 'admin',
            createdAt: new Date().toISOString()
        };
    }

    generateLicenseKey(type) {
        const prefix = type === 'lifetime' ? 'LIFE' : 'TRIAL';
        const random = Math.random().toString(36).substr(2, 8).toUpperCase();
        const timestamp = Date.now().toString(36).toUpperCase();
        return `${prefix}-${random}-${timestamp}`;
    }

    viewRequestDetails(requestId) {
        const request = this.activationRequests.find(req => req.id === requestId);
        if (!request) return;

        const modalBody = document.getElementById('requestModalBody');
        modalBody.innerHTML = `
            <div class="request-details">
                <div class="detail-row">
                    <strong>الاسم الكامل:</strong>
                    <span>${request.fullName}</span>
                </div>
                <div class="detail-row">
                    <strong>رقم الهاتف:</strong>
                    <span>${request.phone}</span>
                </div>
                <div class="detail-row">
                    <strong>الولاية:</strong>
                    <span>${request.state}</span>
                </div>
                <div class="detail-row">
                    <strong>البلدية:</strong>
                    <span>${request.municipality}</span>
                </div>
                <div class="detail-row">
                    <strong>نوع الترخيص المطلوب:</strong>
                    <span>${request.licenseType === 'trial' ? 'تجريبي' : 'مدى الحياة'}</span>
                </div>
                <div class="detail-row">
                    <strong>تاريخ الطلب:</strong>
                    <span>${this.formatDate(request.requestDate)}</span>
                </div>
                <div class="detail-row">
                    <strong>معرف الجهاز:</strong>
                    <span>${request.deviceId}</span>
                </div>
                ${request.notes ? `
                    <div class="detail-row">
                        <strong>ملاحظات:</strong>
                        <span>${request.notes}</span>
                    </div>
                ` : ''}
                <div class="detail-row">
                    <strong>الحالة:</strong>
                    <span class="status-badge status-${request.status}">${this.getStatusText(request.status)}</span>
                </div>
            </div>
        `;

        this.currentRequestId = requestId;
        this.showModal('requestModal');
    }

    async approveRequest() {
        if (!this.currentRequestId) return;

        this.showLoading();

        try {
            const request = this.activationRequests.find(req => req.id === this.currentRequestId);
            if (!request) throw new Error('الطلب غير موجود');

            // Generate license
            const licenseData = {
                clientName: request.fullName,
                clientPhone: request.phone,
                type: request.licenseType,
                notes: `تم إنشاؤه من طلب التفعيل - ${request.fullName}`
            };

            const newLicense = this.generateLicense(licenseData);
            this.activeLicenses.push(newLicense);

            // Update request status
            request.status = 'approved';
            request.licenseKey = newLicense.key;
            request.approvedAt = new Date().toISOString();

            this.saveActivationRequests();
            this.saveActiveLicenses();

            this.showNotification('تم الموافقة على الطلب وإنشاء الترخيص', 'success');
            this.hideModal('requestModal');

            this.addActivity({
                type: 'request_approved',
                description: `تم الموافقة على طلب ${request.fullName} وإنشاء ترخيص`,
                date: new Date().toISOString()
            });

            this.loadActivationRequests();
            this.updateStats();

        } catch (error) {
            this.showNotification('خطأ في الموافقة على الطلب', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async rejectRequest() {
        if (!this.currentRequestId) return;

        this.showLoading();

        try {
            const request = this.activationRequests.find(req => req.id === this.currentRequestId);
            if (!request) throw new Error('الطلب غير موجود');

            request.status = 'rejected';
            request.rejectedAt = new Date().toISOString();

            this.saveActivationRequests();

            this.showNotification('تم رفض الطلب', 'warning');
            this.hideModal('requestModal');

            this.addActivity({
                type: 'request_rejected',
                description: `تم رفض طلب ${request.fullName}`,
                date: new Date().toISOString()
            });

            this.loadActivationRequests();
            this.updateStats();

        } catch (error) {
            this.showNotification('خطأ في رفض الطلب', 'error');
        } finally {
            this.hideLoading();
        }
    }

    quickApprove(requestId) {
        this.currentRequestId = requestId;
        this.approveRequest();
    }

    quickReject(requestId) {
        this.currentRequestId = requestId;
        this.rejectRequest();
    }

    loadStatistics() {
        // Load chart data and statistics
        this.updateStatsTable();
    }

    updateStatsTable() {
        const tableBody = document.querySelector('#statsTable tbody');
        const totalLicenses = this.activeLicenses.length;
        const activeLicenses = this.activeLicenses.filter(lic => lic.isActive).length;
        const trialLicenses = this.activeLicenses.filter(lic => lic.type === 'trial').length;
        const lifetimeLicenses = this.activeLicenses.filter(lic => lic.type === 'lifetime').length;

        const stats = [
            { type: 'إجمالي التراخيص', count: totalLicenses, percentage: '100%' },
            { type: 'التراخيص المفعلة', count: activeLicenses, percentage: totalLicenses ? `${Math.round((activeLicenses / totalLicenses) * 100)}%` : '0%' },
            { type: 'التراخيص التجريبية', count: trialLicenses, percentage: totalLicenses ? `${Math.round((trialLicenses / totalLicenses) * 100)}%` : '0%' },
            { type: 'تراخيص مدى الحياة', count: lifetimeLicenses, percentage: totalLicenses ? `${Math.round((lifetimeLicenses / totalLicenses) * 100)}%` : '0%' }
        ];

        tableBody.innerHTML = stats.map(stat => `
            <tr>
                <td>${stat.type}</td>
                <td>${stat.count}</td>
                <td>${stat.percentage}</td>
            </tr>
        `).join('');
    }

    // Utility methods
    getActivationRequests() {
        try {
            const requests = localStorage.getItem('activationRequests');
            return requests ? JSON.parse(requests).map(req => ({
                ...req,
                id: req.id || this.generateId()
            })) : [];
        } catch {
            return [];
        }
    }

    saveActivationRequests() {
        localStorage.setItem('activationRequests', JSON.stringify(this.activationRequests));
    }

    getActiveLicenses() {
        try {
            const licenses = localStorage.getItem('activeLicenses');
            return licenses ? JSON.parse(licenses) : [];
        } catch {
            return [];
        }
    }

    saveActiveLicenses() {
        localStorage.setItem('activeLicenses', JSON.stringify(this.activeLicenses));
    }

    getRecentActivities() {
        try {
            const activities = localStorage.getItem('recentActivities');
            return activities ? JSON.parse(activities).slice(0, 5) : [];
        } catch {
            return [];
        }
    }

    addActivity(activity) {
        const activities = this.getRecentActivities();
        activities.unshift({
            ...activity,
            id: this.generateId()
        });
        localStorage.setItem('recentActivities', JSON.stringify(activities.slice(0, 20)));
    }

    loadSettings() {
        try {
            const settings = localStorage.getItem('licenseSettings');
            return settings ? JSON.parse(settings) : {
                defaultTrialDays: 30,
                autoApproval: false,
                emailNotifications: false,
                expiryWarning: 7
            };
        } catch {
            return {
                defaultTrialDays: 30,
                autoApproval: false,
                emailNotifications: false,
                expiryWarning: 7
            };
        }
    }

    saveSettings() {
        const settings = {
            defaultTrialDays: parseInt(document.getElementById('defaultTrialDays').value),
            autoApproval: document.getElementById('autoApproval').checked,
            emailNotifications: document.getElementById('emailNotifications').checked,
            expiryWarning: parseInt(document.getElementById('expiryWarning').value)
        };

        localStorage.setItem('licenseSettings', JSON.stringify(settings));
        this.settings = settings;
        this.showNotification('تم حفظ الإعدادات', 'success');
    }

    loadSettingsForm() {
        document.getElementById('defaultTrialDays').value = this.settings.defaultTrialDays;
        document.getElementById('autoApproval').checked = this.settings.autoApproval;
        document.getElementById('emailNotifications').checked = this.settings.emailNotifications;
        document.getElementById('expiryWarning').value = this.settings.expiryWarning;
    }

    calculateExpiryDate(days) {
        const date = new Date();
        date.setDate(date.getDate() + days);
        return date.toLocaleDateString('ar-SA');
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    generateId() {
        return 'id_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    }

    getStatusText(status) {
        const statusTexts = {
            pending: 'معلق',
            approved: 'موافق عليه',
            rejected: 'مرفوض'
        };
        return statusTexts[status] || status;
    }

    getActivityColor(type) {
        const colors = {
            request_approved: 'var(--success-color)',
            request_rejected: 'var(--danger-color)',
            license_generated: 'var(--info-color)',
            license_expired: 'var(--warning-color)'
        };
        return colors[type] || 'var(--secondary-color)';
    }

    getActivityIcon(type) {
        const icons = {
            request_approved: 'fas fa-check',
            request_rejected: 'fas fa-times',
            license_generated: 'fas fa-key',
            license_expired: 'fas fa-clock'
        };
        return icons[type] || 'fas fa-info';
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.add('show');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
    }

    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        icon.className = `notification-icon ${icons[type]}`;
        messageEl.textContent = message;
        notification.className = `notification ${type}`;
        notification.style.display = 'block';

        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }

    logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            // Clear session
            localStorage.removeItem('adminSession');

            // Redirect to login
            if (typeof window !== 'undefined' && window.electronAPI) {
                window.electronAPI.redirectToAuth();
            } else {
                window.location.href = '../auth/activation-login.html';
            }
        }
    }
}

// Initialize dashboard
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new LicenseManagementDashboard();
});

    switchSection(section) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${section}-section`).classList.add('active');

        // Update page title
        const titles = {
            overview: 'نظرة عامة',
            requests: 'طلبات التفعيل',
            licenses: 'التراخيص المفعلة',
            generate: 'إنشاء ترخيص',
            statistics: 'الإحصائيات',
            settings: 'الإعدادات'
        };

        const descriptions = {
            overview: 'إدارة شاملة لتراخيص النظام',
            requests: 'مراجعة والموافقة على طلبات التفعيل',
            licenses: 'إدارة التراخيص المفعلة والمنتهية الصلاحية',
            generate: 'إنشاء تراخيص جديدة للعملاء',
            statistics: 'تقارير وإحصائيات مفصلة',
            settings: 'إعدادات النظام والتفضيلات'
        };

        document.getElementById('pageTitle').textContent = titles[section];
        document.getElementById('pageDescription').textContent = descriptions[section];

        this.currentSection = section;

        // Load section-specific data
        this.loadSectionData(section);
    }

    loadSectionData(section) {
        switch(section) {
            case 'requests':
                this.loadActivationRequests();
                break;
            case 'licenses':
                this.loadActiveLicenses();
                break;
            case 'statistics':
                this.loadStatistics();
                break;
            case 'settings':
                this.loadSettingsForm();
                break;
        }
    }

    // تحميل طلبات التفعيل
    loadActivationRequests() {
        const requestsList = document.getElementById('requestsList');
        if (!requestsList) return;

        if (this.activationRequests.length === 0) {
            requestsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>لا توجد طلبات تفعيل</h3>
                    <p>لم يتم استلام أي طلبات تفعيل حتى الآن</p>
                </div>
            `;
            return;
        }

        const requestsHTML = this.activationRequests.map(request => `
            <div class="request-card" data-request-id="${request.id}">
                <div class="request-header">
                    <div class="request-info">
                        <h4>${request.userName}</h4>
                        <p><i class="fas fa-phone"></i> ${request.userPhone}</p>
                        <p><i class="fas fa-map-marker-alt"></i> ${request.userState} - ${request.userMunicipality}</p>
                    </div>
                    <div class="request-status">
                        <span class="status-badge status-${request.status}">
                            ${this.getStatusText(request.status)}
                        </span>
                    </div>
                </div>
                <div class="request-details">
                    <p><i class="fas fa-calendar"></i> ${this.formatDate(request.requestDate)}</p>
                    <p><i class="fas fa-desktop"></i> ${request.deviceInfo}</p>
                </div>
                <div class="request-actions">
                    <button class="btn btn-sm btn-success" onclick="dashboard.approveRequest('${request.id}')">
                        <i class="fas fa-check"></i> موافقة
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="dashboard.rejectRequest('${request.id}')">
                        <i class="fas fa-times"></i> رفض
                    </button>
                    <button class="btn btn-sm btn-info" onclick="dashboard.viewRequestDetails('${request.id}')">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                </div>
            </div>
        `).join('');

        requestsList.innerHTML = requestsHTML;
    }

    // تحميل التراخيص المفعلة
    loadActiveLicenses() {
        const licensesList = document.getElementById('licensesList');
        if (!licensesList) return;

        if (this.activeLicenses.length === 0) {
            licensesList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-key"></i>
                    <h3>لا توجد تراخيص مفعلة</h3>
                    <p>لم يتم إنشاء أي تراخيص حتى الآن</p>
                </div>
            `;
            return;
        }

        const licensesHTML = this.activeLicenses.map(license => `
            <div class="license-card" data-license-id="${license.id}">
                <div class="license-header">
                    <div class="license-info">
                        <h4>${license.clientName}</h4>
                        <p class="license-key"><i class="fas fa-key"></i> ${license.key}</p>
                        <p><i class="fas fa-phone"></i> ${license.clientPhone}</p>
                    </div>
                    <div class="license-type">
                        <span class="type-badge type-${license.type}">
                            ${license.type === 'lifetime' ? 'مدى الحياة' : 'تجريبي'}
                        </span>
                    </div>
                </div>
                <div class="license-details">
                    <div class="detail-row">
                        <span><i class="fas fa-calendar-plus"></i> تاريخ التفعيل:</span>
                        <span>${this.formatDate(license.activationDate)}</span>
                    </div>
                    <div class="detail-row">
                        <span><i class="fas fa-calendar-times"></i> تاريخ الانتهاء:</span>
                        <span class="${license.type === 'lifetime' ? 'lifetime-text' : this.isExpiringSoon(license.expiryDate) ? 'expiring-soon' : ''}">
                            ${license.expiryDate === 'مدى الحياة' ? 'مدى الحياة' : this.formatDate(license.expiryDate)}
                        </span>
                    </div>
                    ${license.notes ? `
                    <div class="detail-row">
                        <span><i class="fas fa-sticky-note"></i> ملاحظات:</span>
                        <span>${license.notes}</span>
                    </div>
                    ` : ''}
                </div>
                <div class="license-actions">
                    <button class="btn btn-sm btn-warning" onclick="dashboard.editLicense('${license.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="dashboard.revokeLicense('${license.id}')">
                        <i class="fas fa-ban"></i> إلغاء
                    </button>
                    <button class="btn btn-sm btn-info" onclick="dashboard.copyLicenseKey('${license.key}')">
                        <i class="fas fa-copy"></i> نسخ المفتاح
                    </button>
                </div>
            </div>
        `).join('');

        licensesList.innerHTML = licensesHTML;
    }

    // تحميل الإحصائيات
    loadStatistics() {
        const statsTable = document.getElementById('statsTable');
        if (!statsTable) return;

        const stats = this.calculateStatistics();

        const statsHTML = `
            <tr>
                <td>إجمالي التراخيص</td>
                <td>${stats.total}</td>
                <td>100%</td>
            </tr>
            <tr>
                <td>تراخيص مدى الحياة</td>
                <td>${stats.lifetime}</td>
                <td>${stats.total > 0 ? Math.round((stats.lifetime / stats.total) * 100) : 0}%</td>
            </tr>
            <tr>
                <td>تراخيص تجريبية</td>
                <td>${stats.trial}</td>
                <td>${stats.total > 0 ? Math.round((stats.trial / stats.total) * 100) : 0}%</td>
            </tr>
            <tr>
                <td>طلبات معلقة</td>
                <td>${stats.pendingRequests}</td>
                <td>-</td>
            </tr>
            <tr>
                <td>تراخيص منتهية الصلاحية</td>
                <td>${stats.expired}</td>
                <td>${stats.total > 0 ? Math.round((stats.expired / stats.total) * 100) : 0}%</td>
            </tr>
        `;

        statsTable.querySelector('tbody').innerHTML = statsHTML;

        // رسم الإحصائيات
        this.drawStatisticsChart(stats);
    }

    // حساب الإحصائيات
    calculateStatistics() {
        const total = this.activeLicenses.length;
        const lifetime = this.activeLicenses.filter(l => l.type === 'lifetime').length;
        const trial = this.activeLicenses.filter(l => l.type === 'trial').length;
        const pendingRequests = this.activationRequests.filter(r => r.status === 'pending').length;
        const expired = this.activeLicenses.filter(l => this.isLicenseExpired(l)).length;

        return { total, lifetime, trial, pendingRequests, expired };
    }

    // رسم مخطط الإحصائيات
    drawStatisticsChart(stats) {
        const canvas = document.getElementById('licensesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // مسح الرسم السابق
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // إعداد الرسم
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;

        const data = [
            { label: 'مدى الحياة', value: stats.lifetime, color: '#27ae60' },
            { label: 'تجريبي', value: stats.trial, color: '#3498db' },
            { label: 'منتهي الصلاحية', value: stats.expired, color: '#e74c3c' }
        ];

        let currentAngle = -Math.PI / 2;

        data.forEach(item => {
            if (item.value > 0) {
                const sliceAngle = (item.value / stats.total) * 2 * Math.PI;

                // رسم القطعة
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fillStyle = item.color;
                ctx.fill();

                // رسم النص
                const textAngle = currentAngle + sliceAngle / 2;
                const textX = centerX + Math.cos(textAngle) * (radius * 0.7);
                const textY = centerY + Math.sin(textAngle) * (radius * 0.7);

                ctx.fillStyle = '#fff';
                ctx.font = '14px Cairo';
                ctx.textAlign = 'center';
                ctx.fillText(item.value.toString(), textX, textY);

                currentAngle += sliceAngle;
            }
        });
    }

    async loadData() {
        this.showLoading();
        
        try {
            // Load activation requests
            this.activationRequests = this.getActivationRequests();
            
            // Load active licenses
            this.activeLicenses = this.getActiveLicenses();
            
            this.updateStats();
            this.updateRequestsBadge();
            
        } catch (error) {
            this.showNotification('خطأ في تحميل البيانات', 'error');
        } finally {
            this.hideLoading();
        }
    }

    refreshData() {
        this.loadData();
        this.showNotification('تم تحديث البيانات', 'success');
    }

    updateStats() {
        const pendingRequests = this.activationRequests.filter(req => req.status === 'pending').length;
        const activeLicenses = this.activeLicenses.filter(lic => lic.isActive).length;
        const trialLicenses = this.activeLicenses.filter(lic => lic.type === 'trial' && lic.isActive).length;
        const lifetimeLicenses = this.activeLicenses.filter(lic => lic.type === 'lifetime' && lic.isActive).length;

        document.getElementById('pendingRequestsCount').textContent = pendingRequests;
        document.getElementById('activeLicensesCount').textContent = activeLicenses;
        document.getElementById('trialLicensesCount').textContent = trialLicenses;
        document.getElementById('lifetimeLicensesCount').textContent = lifetimeLicenses;
    }

    updateRequestsBadge() {
        const pendingCount = this.activationRequests.filter(req => req.status === 'pending').length;
        document.getElementById('requestsBadge').textContent = pendingCount;
    }

    loadRecentActivity() {
        const activityContainer = document.getElementById('recentActivity');
        const activities = this.getRecentActivities();

        if (activities.length === 0) {
            activityContainer.innerHTML = '<p class="text-muted">لا توجد أنشطة حديثة</p>';
            return;
        }

        const activitiesHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon" style="background: ${this.getActivityColor(activity.type)}">
                    <i class="${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <p>${activity.description}</p>
                    <small>${this.formatDate(activity.date)}</small>
                </div>
            </div>
        `).join('');

        activityContainer.innerHTML = activitiesHTML;
    }

    loadActivationRequests() {
        const requestsList = document.getElementById('requestsList');
        const requests = this.activationRequests;

        if (requests.length === 0) {
            requestsList.innerHTML = '<div class="empty-state"><p>لا توجد طلبات تفعيل</p></div>';
            return;
        }

        const requestsHTML = requests.map(request => `
            <div class="request-item" data-request-id="${request.id}">
                <div class="item-info">
                    <h4>${request.fullName}</h4>
                    <p><i class="fas fa-phone"></i> ${request.phone}</p>
                    <p><i class="fas fa-map-marker-alt"></i> ${request.state} - ${request.municipality}</p>
                    <p><i class="fas fa-calendar"></i> ${this.formatDate(request.requestDate)}</p>
                    <span class="status-badge status-${request.status}">${this.getStatusText(request.status)}</span>
                </div>
                <div class="item-actions">
                    <button class="btn btn-info btn-sm" onclick="dashboard.viewRequestDetails('${request.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    ${request.status === 'pending' ? `
                        <button class="btn btn-success btn-sm" onclick="dashboard.quickApprove('${request.id}')">
                            <i class="fas fa-check"></i> موافقة
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="dashboard.quickReject('${request.id}')">
                            <i class="fas fa-times"></i> رفض
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');

        requestsList.innerHTML = requestsHTML;
    }

    loadActiveLicenses() {
        const licensesList = document.getElementById('licensesList');
        const licenses = this.activeLicenses;

        if (licenses.length === 0) {
            licensesList.innerHTML = '<div class="empty-state"><p>لا توجد تراخيص مفعلة</p></div>';
            return;
        }

        const licensesHTML = licenses.map(license => `
            <div class="license-item" data-license-id="${license.id}">
                <div class="item-info">
                    <h4>${license.clientName || 'غير محدد'}</h4>
                    <p><i class="fas fa-key"></i> ${license.key}</p>
                    <p><i class="fas fa-tag"></i> ${license.type === 'trial' ? 'تجريبي' : 'مدى الحياة'}</p>
                    <p><i class="fas fa-calendar"></i> تاريخ التفعيل: ${this.formatDate(license.activationDate)}</p>
                    <p><i class="fas fa-clock"></i> انتهاء الصلاحية: ${license.expiryDate}</p>
                    <span class="status-badge status-${license.isActive ? 'active' : 'expired'}">
                        ${license.isActive ? 'مفعل' : 'منتهي الصلاحية'}
                    </span>
                </div>
                <div class="item-actions">
                    <button class="btn btn-info btn-sm" onclick="dashboard.viewLicenseDetails('${license.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    ${license.isActive ? `
                        <button class="btn btn-warning btn-sm" onclick="dashboard.extendLicense('${license.id}')">
                            <i class="fas fa-clock"></i> تمديد
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="dashboard.revokeLicense('${license.id}')">
                            <i class="fas fa-ban"></i> إلغاء
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');

        licensesList.innerHTML = licensesHTML;
    }

    async handleGenerateLicense(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const licenseData = {
            clientName: formData.get('clientName'),
            clientPhone: formData.get('clientPhone'),
            type: formData.get('licenseType'),
            customDuration: formData.get('customDuration'),
            notes: formData.get('notes')
        };

        this.showLoading();

        try {
            const newLicense = this.generateLicense(licenseData);
            this.activeLicenses.push(newLicense);
            this.saveActiveLicenses();
            
            this.showNotification(`تم إنشاء الترخيص بنجاح: ${newLicense.key}`, 'success');
            e.target.reset();
            
            // Add to recent activity
            this.addActivity({
                type: 'license_generated',
                description: `تم إنشاء ترخيص جديد للعميل ${licenseData.clientName}`,
                date: new Date().toISOString()
            });
            
            this.updateStats();
            
        } catch (error) {
            this.showNotification('خطأ في إنشاء الترخيص', 'error');
        } finally {
            this.hideLoading();
        }
    }

    generateLicense(data) {
        const licenseKey = this.generateLicenseKey(data.type);
        const isLifetime = data.type === 'lifetime';
        const duration = data.customDuration ? parseInt(data.customDuration) : (isLifetime ? null : 30);
        
        return {
            id: this.generateId(),
            key: licenseKey,
            clientName: data.clientName,
            clientPhone: data.clientPhone,
            type: data.type,
            isActive: true,
            activationDate: new Date().toISOString(),
            expiryDate: isLifetime ? 'مدى الحياة' : this.calculateExpiryDate(duration),
            notes: data.notes,
            createdBy: 'admin',
            createdAt: new Date().toISOString()
        };
    }

    generateLicenseKey(type) {
        const prefix = type === 'lifetime' ? 'LIFE' : 'TRIAL';
        const random = Math.random().toString(36).substr(2, 8).toUpperCase();
        const timestamp = Date.now().toString(36).toUpperCase();
        return `${prefix}-${random}-${timestamp}`;
    }

    viewRequestDetails(requestId) {
        const request = this.activationRequests.find(req => req.id === requestId);
        if (!request) return;

        const modalBody = document.getElementById('requestModalBody');
        modalBody.innerHTML = `
            <div class="request-details">
                <div class="detail-row">
                    <strong>الاسم الكامل:</strong>
                    <span>${request.fullName}</span>
                </div>
                <div class="detail-row">
                    <strong>رقم الهاتف:</strong>
                    <span>${request.phone}</span>
                </div>
                <div class="detail-row">
                    <strong>الولاية:</strong>
                    <span>${request.state}</span>
                </div>
                <div class="detail-row">
                    <strong>البلدية:</strong>
                    <span>${request.municipality}</span>
                </div>
                <div class="detail-row">
                    <strong>نوع الترخيص المطلوب:</strong>
                    <span>${request.licenseType === 'trial' ? 'تجريبي' : 'مدى الحياة'}</span>
                </div>
                <div class="detail-row">
                    <strong>تاريخ الطلب:</strong>
                    <span>${this.formatDate(request.requestDate)}</span>
                </div>
                <div class="detail-row">
                    <strong>معرف الجهاز:</strong>
                    <span>${request.deviceId}</span>
                </div>
                ${request.notes ? `
                    <div class="detail-row">
                        <strong>ملاحظات:</strong>
                        <span>${request.notes}</span>
                    </div>
                ` : ''}
                <div class="detail-row">
                    <strong>الحالة:</strong>
                    <span class="status-badge status-${request.status}">${this.getStatusText(request.status)}</span>
                </div>
            </div>
        `;

        this.currentRequestId = requestId;
        this.showModal('requestModal');
    }

    async approveRequest() {
        if (!this.currentRequestId) return;

        this.showLoading();
        
        try {
            const request = this.activationRequests.find(req => req.id === this.currentRequestId);
            if (!request) throw new Error('الطلب غير موجود');

            // Generate license
            const licenseData = {
                clientName: request.fullName,
                clientPhone: request.phone,
                type: request.licenseType,
                notes: `تم إنشاؤه من طلب التفعيل - ${request.fullName}`
            };

            const newLicense = this.generateLicense(licenseData);
            this.activeLicenses.push(newLicense);
            
            // Update request status
            request.status = 'approved';
            request.licenseKey = newLicense.key;
            request.approvedAt = new Date().toISOString();
            
            this.saveActivationRequests();
            this.saveActiveLicenses();
            
            this.showNotification('تم الموافقة على الطلب وإنشاء الترخيص', 'success');
            this.hideModal('requestModal');
            
            this.addActivity({
                type: 'request_approved',
                description: `تم الموافقة على طلب ${request.fullName} وإنشاء ترخيص`,
                date: new Date().toISOString()
            });
            
            this.loadActivationRequests();
            this.updateStats();
            
        } catch (error) {
            this.showNotification('خطأ في الموافقة على الطلب', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async rejectRequest() {
        if (!this.currentRequestId) return;

        this.showLoading();
        
        try {
            const request = this.activationRequests.find(req => req.id === this.currentRequestId);
            if (!request) throw new Error('الطلب غير موجود');

            request.status = 'rejected';
            request.rejectedAt = new Date().toISOString();
            
            this.saveActivationRequests();
            
            this.showNotification('تم رفض الطلب', 'warning');
            this.hideModal('requestModal');
            
            this.addActivity({
                type: 'request_rejected',
                description: `تم رفض طلب ${request.fullName}`,
                date: new Date().toISOString()
            });
            
            this.loadActivationRequests();
            this.updateStats();
            
        } catch (error) {
            this.showNotification('خطأ في رفض الطلب', 'error');
        } finally {
            this.hideLoading();
        }
    }

    quickApprove(requestId) {
        this.currentRequestId = requestId;
        this.approveRequest();
    }

    quickReject(requestId) {
        this.currentRequestId = requestId;
        this.rejectRequest();
    }

    // Utility methods
    getActivationRequests() {
        try {
            const requests = localStorage.getItem('activationRequests');
            return requests ? JSON.parse(requests).map(req => ({
                ...req,
                id: req.id || this.generateId()
            })) : [];
        } catch {
            return [];
        }
    }

    saveActivationRequests() {
        localStorage.setItem('activationRequests', JSON.stringify(this.activationRequests));
    }

    getActiveLicenses() {
        try {
            const licenses = localStorage.getItem('activeLicenses');
            return licenses ? JSON.parse(licenses) : [];
        } catch {
            return [];
        }
    }

    saveActiveLicenses() {
        localStorage.setItem('activeLicenses', JSON.stringify(this.activeLicenses));
    }

    getRecentActivities() {
        try {
            const activities = localStorage.getItem('recentActivities');
            return activities ? JSON.parse(activities).slice(0, 5) : [];
        } catch {
            return [];
        }
    }

    addActivity(activity) {
        const activities = this.getRecentActivities();
        activities.unshift({
            ...activity,
            id: this.generateId()
        });
        localStorage.setItem('recentActivities', JSON.stringify(activities.slice(0, 20)));
    }

    loadSettings() {
        try {
            const settings = localStorage.getItem('licenseSettings');
            return settings ? JSON.parse(settings) : {
                defaultTrialDays: 30,
                autoApproval: false,
                emailNotifications: false,
                expiryWarning: 7
            };
        } catch {
            return {
                defaultTrialDays: 30,
                autoApproval: false,
                emailNotifications: false,
                expiryWarning: 7
            };
        }
    }

    saveSettings() {
        const settings = {
            defaultTrialDays: parseInt(document.getElementById('defaultTrialDays').value),
            autoApproval: document.getElementById('autoApproval').checked,
            emailNotifications: document.getElementById('emailNotifications').checked,
            expiryWarning: parseInt(document.getElementById('expiryWarning').value)
        };

        localStorage.setItem('licenseSettings', JSON.stringify(settings));
        this.settings = settings;
        this.showNotification('تم حفظ الإعدادات', 'success');
    }

    loadSettingsForm() {
        document.getElementById('defaultTrialDays').value = this.settings.defaultTrialDays;
        document.getElementById('autoApproval').checked = this.settings.autoApproval;
        document.getElementById('emailNotifications').checked = this.settings.emailNotifications;
        document.getElementById('expiryWarning').value = this.settings.expiryWarning;
    }

    calculateExpiryDate(days) {
        const date = new Date();
        date.setDate(date.getDate() + days);
        return date.toLocaleDateString('ar-SA');
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    generateId() {
        return 'id_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    }

    getStatusText(status) {
        const statusTexts = {
            pending: 'معلق',
            approved: 'موافق عليه',
            rejected: 'مرفوض'
        };
        return statusTexts[status] || status;
    }

    getActivityColor(type) {
        const colors = {
            request_approved: 'var(--success-color)',
            request_rejected: 'var(--danger-color)',
            license_generated: 'var(--info-color)',
            license_expired: 'var(--warning-color)'
        };
        return colors[type] || 'var(--secondary-color)';
    }

    getActivityIcon(type) {
        const icons = {
            request_approved: 'fas fa-check',
            request_rejected: 'fas fa-times',
            license_generated: 'fas fa-key',
            license_expired: 'fas fa-clock'
        };
        return icons[type] || 'fas fa-info';
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.add('show');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
    }

    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        icon.className = `notification-icon ${icons[type]}`;
        messageEl.textContent = message;
        notification.className = `notification ${type}`;
        notification.style.display = 'block';

        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }

    logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            // Clear session
            localStorage.removeItem('adminSession');
            
            // Redirect to login
            if (typeof window !== 'undefined' && window.electronAPI) {
                window.electronAPI.redirectToAuth();
            } else {
                window.location.href = '../auth/activation-login.html';
            }
        }
    }
}

    // وظائف مساعدة
    getStatusText(status) {
        const statusTexts = {
            'pending': 'معلق',
            'approved': 'موافق عليه',
            'rejected': 'مرفوض'
        };
        return statusTexts[status] || status;
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    isExpiringSoon(expiryDate) {
        if (expiryDate === 'مدى الحياة') return false;
        const expiry = new Date(expiryDate);
        const now = new Date();
        const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
    }

    isLicenseExpired(license) {
        if (license.expiryDate === 'مدى الحياة') return false;
        const expiry = new Date(license.expiryDate);
        const now = new Date();
        return expiry < now;
    }

    // وظائف التفاعل مع الطلبات
    approveRequest(requestId) {
        const request = this.activationRequests.find(r => r.id === requestId);
        if (!request) return;

        if (confirm(`هل أنت متأكد من الموافقة على طلب ${request.userName}؟`)) {
            // إنشاء ترخيص جديد
            const newLicense = this.generateLicense({
                clientName: request.userName,
                clientPhone: request.userPhone,
                type: 'trial',
                notes: `تم إنشاؤه من طلب التفعيل ${request.id}`
            });

            this.activeLicenses.push(newLicense);
            this.saveActiveLicenses();

            // تحديث حالة الطلب
            request.status = 'approved';
            this.saveActivationRequests();

            // إضافة نشاط
            this.addActivity({
                type: 'request_approved',
                description: `تم الموافقة على طلب تفعيل ${request.userName}`,
                date: new Date().toISOString()
            });

            this.showNotification(`تم الموافقة على الطلب وإنشاء ترخيص: ${newLicense.key}`, 'success');
            this.updateStats();
            this.loadActivationRequests();
        }
    }

    rejectRequest(requestId) {
        const request = this.activationRequests.find(r => r.id === requestId);
        if (!request) return;

        if (confirm(`هل أنت متأكد من رفض طلب ${request.userName}؟`)) {
            request.status = 'rejected';
            this.saveActivationRequests();

            this.addActivity({
                type: 'request_rejected',
                description: `تم رفض طلب تفعيل ${request.userName}`,
                date: new Date().toISOString()
            });

            this.showNotification(`تم رفض طلب ${request.userName}`, 'warning');
            this.updateStats();
            this.loadActivationRequests();
        }
    }

    viewRequestDetails(requestId) {
        const request = this.activationRequests.find(r => r.id === requestId);
        if (!request) return;

        const modal = document.getElementById('requestModal');
        const modalBody = document.getElementById('requestModalBody');

        modalBody.innerHTML = `
            <div class="request-details-full">
                <div class="detail-section">
                    <h4>معلومات العميل</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>الاسم:</label>
                            <span>${request.userName}</span>
                        </div>
                        <div class="detail-item">
                            <label>رقم الهاتف:</label>
                            <span>${request.userPhone}</span>
                        </div>
                        <div class="detail-item">
                            <label>الولاية:</label>
                            <span>${request.userState}</span>
                        </div>
                        <div class="detail-item">
                            <label>البلدية:</label>
                            <span>${request.userMunicipality}</span>
                        </div>
                    </div>
                </div>
                <div class="detail-section">
                    <h4>معلومات الطلب</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>تاريخ الطلب:</label>
                            <span>${this.formatDate(request.requestDate)}</span>
                        </div>
                        <div class="detail-item">
                            <label>معلومات الجهاز:</label>
                            <span>${request.deviceInfo}</span>
                        </div>
                        <div class="detail-item">
                            <label>الحالة:</label>
                            <span class="status-badge status-${request.status}">
                                ${this.getStatusText(request.status)}
                            </span>
                        </div>
                        <div class="detail-item">
                            <label>رقم الطلب:</label>
                            <span>${request.id}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        modal.style.display = 'flex';

        // ربط أزرار الموافقة والرفض
        document.getElementById('approveRequestBtn').onclick = () => {
            this.approveRequest(requestId);
            modal.style.display = 'none';
        };

        document.getElementById('rejectRequestBtn').onclick = () => {
            this.rejectRequest(requestId);
            modal.style.display = 'none';
        };
    }

    // وظائف التفاعل مع التراخيص
    editLicense(licenseId) {
        const license = this.activeLicenses.find(l => l.id === licenseId);
        if (!license) return;

        const newNotes = prompt('تعديل الملاحظات:', license.notes || '');
        if (newNotes !== null) {
            license.notes = newNotes;
            this.saveActiveLicenses();
            this.showNotification('تم تحديث الترخيص بنجاح', 'success');
            this.loadActiveLicenses();
        }
    }

    revokeLicense(licenseId) {
        const license = this.activeLicenses.find(l => l.id === licenseId);
        if (!license) return;

        if (confirm(`هل أنت متأكد من إلغاء ترخيص ${license.clientName}؟`)) {
            license.isActive = false;
            license.revokedAt = new Date().toISOString();
            this.saveActiveLicenses();

            this.addActivity({
                type: 'license_revoked',
                description: `تم إلغاء ترخيص ${license.clientName}`,
                date: new Date().toISOString()
            });

            this.showNotification(`تم إلغاء ترخيص ${license.clientName}`, 'warning');
            this.updateStats();
            this.loadActiveLicenses();
        }
    }

    copyLicenseKey(licenseKey) {
        navigator.clipboard.writeText(licenseKey).then(() => {
            this.showNotification('تم نسخ مفتاح الترخيص', 'success');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = licenseKey;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('تم نسخ مفتاح الترخيص', 'success');
        });
    }
}

// Initialize dashboard
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new LicenseManagementDashboard();
});
