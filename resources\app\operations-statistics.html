<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات العمليات - نظام إدارة الوقود</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/styles.css">
    <link rel="stylesheet" href="styles/operations-statistics.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css">
</head>
<body>
    <div class="statistics-dashboard">
        <div class="dashboard-container">
            <!-- رأس لوحة الإحصائيات -->
            <div class="dashboard-header">
                <div class="dashboard-title">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات العمليات
                </div>
                <div class="dashboard-subtitle">
                    تحليل شامل لأداء العمليات والأنشطة في النظام
                </div>
                <div class="dashboard-controls">
                    <select class="time-range-selector" id="timeRangeSelector">
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="quarter">هذا الربع</option>
                        <option value="year">هذا العام</option>
                        <option value="all">جميع الأوقات</option>
                    </select>
                    <button class="export-btn" onclick="exportStatistics('pdf')">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                    <button class="export-btn" onclick="exportStatistics('excel')">
                        <i class="fas fa-file-excel"></i>
                        تصدير Excel
                    </button>
                    <button class="export-btn" onclick="refreshStatistics()">
                        <i class="fas fa-sync"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <!-- الإحصائيات الأساسية -->
            <div class="stats-grid" id="basicStatsGrid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">إجمالي العمليات</div>
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="totalOperations">0</div>
                    <div class="stat-change positive" id="operationsChange">
                        <i class="fas fa-arrow-up"></i>
                        <span>+12.5%</span>
                        <span>من الشهر الماضي</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">إجمالي الزبائن</div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="totalCustomers">0</div>
                    <div class="stat-change positive" id="customersChange">
                        <i class="fas fa-arrow-up"></i>
                        <span>+8.3%</span>
                        <span>من الشهر الماضي</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">إجمالي الإيرادات</div>
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="totalRevenue">0 د.ج</div>
                    <div class="stat-change positive" id="revenueChange">
                        <i class="fas fa-arrow-up"></i>
                        <span>+15.7%</span>
                        <span>من الشهر الماضي</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">معدل الإنجاز</div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="completionRate">0%</div>
                    <div class="stat-change neutral" id="completionChange">
                        <i class="fas fa-minus"></i>
                        <span>0.0%</span>
                        <span>من الشهر الماضي</span>
                    </div>
                </div>
            </div>

            <!-- المخططات -->
            <div class="charts-section">
                <div class="chart-container">
                    <div class="chart-header">
                        <div class="chart-title">اتجاه العمليات الزمني</div>
                        <div class="chart-controls">
                            <button class="chart-btn active" onclick="switchChart('operations')">العمليات</button>
                            <button class="chart-btn" onclick="switchChart('revenue')">الإيرادات</button>
                            <button class="chart-btn" onclick="switchChart('customers')">الزبائن</button>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="timeChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <div class="chart-title">توزيع العمليات حسب النوع</div>
                    </div>
                    <div class="operations-distribution" id="operationsDistribution">
                        <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
                    </div>
                </div>
            </div>

            <!-- مؤشرات الأداء -->
            <div class="performance-indicators">
                <div class="performance-card">
                    <div class="performance-value" id="avgOperationsPerDay">0</div>
                    <div class="performance-label">متوسط العمليات اليومية</div>
                    <div class="performance-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+5.2%</span>
                    </div>
                </div>

                <div class="performance-card">
                    <div class="performance-value" id="customerRetention">0%</div>
                    <div class="performance-label">معدل الاحتفاظ بالزبائن</div>
                    <div class="performance-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+2.1%</span>
                    </div>
                </div>

                <div class="performance-card">
                    <div class="performance-value" id="avgOperationValue">0 د.ج</div>
                    <div class="performance-label">متوسط قيمة العملية</div>
                    <div class="performance-trend neutral">
                        <i class="fas fa-minus"></i>
                        <span>0.0%</span>
                    </div>
                </div>

                <div class="performance-card">
                    <div class="performance-value" id="operationalEfficiency">0%</div>
                    <div class="performance-label">الكفاءة التشغيلية</div>
                    <div class="performance-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+3.8%</span>
                    </div>
                </div>
            </div>

            <!-- الجداول الإحصائية -->
            <div class="stats-tables">
                <div class="stats-table">
                    <div class="table-header">
                        <div class="table-title">أفضل الزبائن</div>
                        <button class="chart-btn" onclick="viewAllCustomers()">عرض الكل</button>
                    </div>
                    <div class="table-content">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>الزبون</th>
                                    <th>العمليات</th>
                                    <th>الإنفاق</th>
                                </tr>
                            </thead>
                            <tbody id="topCustomersTable">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="stats-table">
                    <div class="table-header">
                        <div class="table-title">أكثر السيارات نشاطاً</div>
                        <button class="chart-btn" onclick="viewAllVehicles()">عرض الكل</button>
                    </div>
                    <div class="table-content">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم اللوحة</th>
                                    <th>النوع</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="activeVehiclesTable">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- مخطط دائري للتوزيع الجغرافي -->
            <div class="chart-container">
                <div class="chart-header">
                    <div class="chart-title">التوزيع الجغرافي للزبائن</div>
                    <div class="chart-controls">
                        <button class="chart-btn active" onclick="switchRegionView('pie')">دائري</button>
                        <button class="chart-btn" onclick="switchRegionView('bar')">أعمدة</button>
                    </div>
                </div>
                <div class="chart-content">
                    <canvas id="regionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="scripts/operations-statistics.js"></script>
    <script>
        // متغيرات عامة
        let timeChart, regionChart;
        let currentTimeRange = 'month';
        let currentChartType = 'operations';
        let currentRegionView = 'pie';

        // بيانات وهمية للاختبار
        const mockData = {
            appData: {
                transmissionTable: [
                    {
                        id: 'trans-1',
                        customerId: 'cust-1',
                        vehicleId: 'veh-1',
                        operationType: 'تركيب',
                        operationDate: '2024-01-15',
                        cost: 5000,
                        status: 'completed',
                        createdAt: '2024-01-15T10:00:00Z'
                    },
                    {
                        id: 'trans-2',
                        customerId: 'cust-2',
                        vehicleId: 'veh-2',
                        operationType: 'مراقبة',
                        operationDate: '2024-01-20',
                        cost: 3000,
                        status: 'completed',
                        createdAt: '2024-01-20T14:00:00Z'
                    }
                ],
                gasCards: [
                    {
                        id: 'card-1',
                        customerId: 'cust-1',
                        vehicleId: 'veh-1',
                        cardNumber: 'GC-12345',
                        issueDate: '2024-01-10',
                        renewalCost: 2000,
                        status: 'active',
                        createdAt: '2024-01-10T09:00:00Z'
                    }
                ],
                customers: [
                    {
                        id: 'cust-1',
                        name: 'أحمد محمد',
                        phone: '0555123456',
                        address: 'الجزائر العاصمة, الجزائر',
                        createdAt: '2024-01-01T08:00:00Z'
                    },
                    {
                        id: 'cust-2',
                        name: 'فاطمة علي',
                        phone: '0666789012',
                        address: 'وهران, الجزائر',
                        createdAt: '2024-01-05T10:00:00Z'
                    }
                ],
                vehicles: [
                    {
                        id: 'veh-1',
                        plateNumber: '123-456-78',
                        type: 'سيارة خاصة',
                        year: 2020,
                        customerId: 'cust-1',
                        createdAt: '2024-01-01T08:30:00Z'
                    },
                    {
                        id: 'veh-2',
                        plateNumber: '987-654-32',
                        type: 'شاحنة',
                        year: 2018,
                        customerId: 'cust-2',
                        createdAt: '2024-01-05T10:30:00Z'
                    }
                ]
            }
        };

        // تعيين البيانات الوهمية
        if (typeof appData === 'undefined') {
            window.appData = mockData.appData;
        }

        // تهيئة لوحة الإحصائيات
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadStatistics();
            setupEventListeners();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('timeRangeSelector').addEventListener('change', function() {
                currentTimeRange = this.value;
                loadStatistics();
                updateCharts();
            });
        }

        // تهيئة المخططات
        function initializeCharts() {
            // مخطط الاتجاه الزمني
            const timeCtx = document.getElementById('timeChart').getContext('2d');
            timeChart = new Chart(timeCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'العمليات',
                        data: [],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    }
                }
            });

            // مخطط التوزيع الجغرافي
            const regionCtx = document.getElementById('regionChart').getContext('2d');
            regionChart = new Chart(regionCtx, {
                type: 'pie',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6',
                            '#06b6d4'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // تحميل الإحصائيات
        function loadStatistics() {
            const basicStats = operationsStats.calculateBasicStats(currentTimeRange);
            const operationsByType = operationsStats.calculateOperationsByType(currentTimeRange);
            const performanceStats = operationsStats.calculatePerformanceStats(currentTimeRange);
            const customerStats = operationsStats.calculateCustomerStats(currentTimeRange);
            const vehicleStats = operationsStats.calculateVehicleStats(currentTimeRange);

            // تحديث الإحصائيات الأساسية
            document.getElementById('totalOperations').textContent = basicStats.totalOperations || 0;
            document.getElementById('totalCustomers').textContent = basicStats.totalCustomers || 0;
            document.getElementById('totalRevenue').textContent = (basicStats.totalRevenue || 0).toLocaleString('ar-DZ') + ' د.ج';
            document.getElementById('completionRate').textContent = (basicStats.completionRate || 0) + '%';

            // تحديث مؤشرات الأداء
            document.getElementById('avgOperationsPerDay').textContent = (basicStats.averageOperationsPerDay || 0).toFixed(1);
            document.getElementById('customerRetention').textContent = (performanceStats.customerRetentionRate || 0).toFixed(1) + '%';
            document.getElementById('avgOperationValue').textContent = (performanceStats.averageOperationValue || 0).toLocaleString('ar-DZ') + ' د.ج';
            document.getElementById('operationalEfficiency').textContent = (performanceStats.operationalEfficiency || 0).toFixed(1) + '%';

            // تحديث توزيع العمليات
            updateOperationsDistribution(operationsByType);

            // تحديث الجداول
            updateTopCustomersTable(customerStats.topCustomers || []);
            updateActiveVehiclesTable(vehicleStats.mostActiveVehicles || []);
        }

        // تحديث توزيع العمليات
        function updateOperationsDistribution(operationsByType) {
            const container = document.getElementById('operationsDistribution');
            container.innerHTML = '';

            Object.entries(operationsByType).forEach(([type, data]) => {
                const operationInfo = operationsStats.operationTypes[type] || {
                    label: type,
                    icon: '📋',
                    color: '#6b7280'
                };

                const item = document.createElement('div');
                item.className = 'operation-item';
                item.innerHTML = `
                    <div class="operation-info">
                        <div class="operation-icon" style="background-color: ${operationInfo.color}">
                            ${operationInfo.icon}
                        </div>
                        <div class="operation-details">
                            <div class="operation-name">${operationInfo.label}</div>
                            <div class="operation-count">${data.count} عملية</div>
                        </div>
                    </div>
                    <div class="operation-percentage">${data.percentage}%</div>
                `;

                // إضافة شريط التقدم
                const progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';
                progressBar.innerHTML = `<div class="progress-fill" style="width: ${data.percentage}%; background-color: ${operationInfo.color}"></div>`;
                item.appendChild(progressBar);

                container.appendChild(item);
            });
        }

        // تحديث جدول أفضل الزبائن
        function updateTopCustomersTable(topCustomers) {
            const tbody = document.getElementById('topCustomersTable');
            tbody.innerHTML = '';

            topCustomers.slice(0, 5).forEach(customer => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer.name}</td>
                    <td>${customer.operationsCount}</td>
                    <td>${customer.totalSpent.toLocaleString('ar-DZ')} د.ج</td>
                `;
                tbody.appendChild(row);
            });
        }

        // تحديث جدول السيارات النشطة
        function updateActiveVehiclesTable(activeVehicles) {
            const tbody = document.getElementById('activeVehiclesTable');
            tbody.innerHTML = '';

            activeVehicles.slice(0, 5).forEach(vehicle => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${vehicle.plateNumber}</td>
                    <td>${vehicle.type}</td>
                    <td>${vehicle.operationsCount}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // تحديث المخططات
        function updateCharts() {
            const timeBasedStats = operationsStats.calculateTimeBasedStats(currentTimeRange);
            const customerStats = operationsStats.calculateCustomerStats(currentTimeRange);

            // تحديث مخطط الاتجاه الزمني
            timeChart.data.labels = timeBasedStats.labels;
            timeChart.data.datasets[0].data = timeBasedStats.datasets[currentChartType] || timeBasedStats.datasets.operations;
            timeChart.update();

            // تحديث مخطط التوزيع الجغرافي
            const regionData = customerStats.customersByRegion || {};
            regionChart.data.labels = Object.keys(regionData);
            regionChart.data.datasets[0].data = Object.values(regionData);
            regionChart.update();
        }

        // تبديل نوع المخطط
        function switchChart(type) {
            currentChartType = type;
            
            // تحديث الأزرار
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // تحديث المخطط
            const timeBasedStats = operationsStats.calculateTimeBasedStats(currentTimeRange);
            timeChart.data.datasets[0].data = timeBasedStats.datasets[type] || timeBasedStats.datasets.operations;
            
            // تحديث التسمية
            const labels = {
                operations: 'العمليات',
                revenue: 'الإيرادات',
                customers: 'الزبائن'
            };
            timeChart.data.datasets[0].label = labels[type] || 'البيانات';
            timeChart.update();
        }

        // تبديل عرض المناطق
        function switchRegionView(viewType) {
            currentRegionView = viewType;
            
            // تحديث الأزرار
            event.target.parentElement.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // إعادة إنشاء المخطط
            regionChart.destroy();
            const regionCtx = document.getElementById('regionChart').getContext('2d');
            
            const customerStats = operationsStats.calculateCustomerStats(currentTimeRange);
            const regionData = customerStats.customersByRegion || {};

            regionChart = new Chart(regionCtx, {
                type: viewType === 'pie' ? 'pie' : 'bar',
                data: {
                    labels: Object.keys(regionData),
                    datasets: [{
                        data: Object.values(regionData),
                        backgroundColor: viewType === 'pie' ? [
                            '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'
                        ] : '#3b82f6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: viewType === 'pie' ? 'bottom' : 'top',
                            display: viewType === 'pie'
                        }
                    },
                    scales: viewType === 'bar' ? {
                        y: {
                            beginAtZero: true
                        }
                    } : {}
                }
            });
        }

        // تصدير الإحصائيات
        function exportStatistics(format) {
            const stats = operationsStats.exportStats(format, currentTimeRange);
            
            if (format === 'pdf') {
                // منطق تصدير PDF
                alert('سيتم تصدير الإحصائيات كملف PDF');
            } else if (format === 'excel') {
                // منطق تصدير Excel
                alert('سيتم تصدير الإحصائيات كملف Excel');
            }
        }

        // تحديث الإحصائيات
        function refreshStatistics() {
            loadStatistics();
            updateCharts();
            
            // إظهار رسالة التحديث
            const refreshBtn = event.target;
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            
            setTimeout(() => {
                refreshBtn.innerHTML = originalText;
            }, 1000);
        }

        // عرض جميع الزبائن
        function viewAllCustomers() {
            alert('سيتم فتح صفحة جميع الزبائن');
        }

        // عرض جميع السيارات
        function viewAllVehicles() {
            alert('سيتم فتح صفحة جميع السيارات');
        }
    </script>
</body>
</html>
