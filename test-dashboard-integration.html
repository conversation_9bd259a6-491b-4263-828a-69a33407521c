<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل لوحة التراخيص</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
            padding: 2rem;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .test-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .btn-success { background: #48bb78; }
        .btn-success:hover { background: #38a169; }
        .btn-warning { background: #ed8936; }
        .btn-warning:hover { background: #dd6b20; }
        .btn-danger { background: #f56565; }
        .btn-danger:hover { background: #e53e3e; }
        .btn-info { background: #4299e1; }
        .btn-info:hover { background: #3182ce; }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 1rem;
            white-space: pre-wrap;
        }

        .status-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .status-success {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .status-error {
            border-color: #f56565;
            background: #fff5f5;
        }

        .status-warning {
            border-color: #ed8936;
            background: #fffaf0;
        }

        .iframe-container {
            grid-column: 1 / -1;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .iframe-header {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cogs"></i> اختبار تكامل لوحة التراخيص</h1>
            <p>أداة شاملة لاختبار التكامل بين طلبات التفعيل ولوحة التراخيص</p>
        </div>

        <div class="test-grid">
            <div class="test-section">
                <h2><i class="fas fa-paper-plane"></i> إرسال طلبات التفعيل</h2>
                
                <button class="btn btn-success" onclick="sendSingleRequest()">
                    <i class="fas fa-user-plus"></i>
                    إرسال طلب واحد
                </button>
                
                <button class="btn btn-warning" onclick="sendMultipleRequests()">
                    <i class="fas fa-users"></i>
                    إرسال 5 طلبات
                </button>
                
                <button class="btn btn-info" onclick="sendRandomRequest()">
                    <i class="fas fa-random"></i>
                    طلب عشوائي
                </button>
                
                <button class="btn btn-danger" onclick="clearAllRequests()">
                    <i class="fas fa-trash"></i>
                    مسح جميع الطلبات
                </button>

                <div id="requestStatus" class="status-card">
                    <h4><i class="fas fa-info-circle"></i> حالة الطلبات</h4>
                    <p id="requestCount">لم يتم إرسال أي طلبات بعد</p>
                </div>
            </div>

            <div class="test-section">
                <h2><i class="fas fa-chart-line"></i> مراقبة النظام</h2>
                
                <button class="btn" onclick="checkLocalStorage()">
                    <i class="fas fa-database"></i>
                    فحص localStorage
                </button>
                
                <button class="btn btn-info" onclick="monitorChanges()">
                    <i class="fas fa-eye"></i>
                    مراقبة التغييرات
                </button>
                
                <button class="btn btn-warning" onclick="simulateStorageEvent()">
                    <i class="fas fa-broadcast-tower"></i>
                    محاكاة حدث التخزين
                </button>
                
                <button class="btn btn-danger" onclick="clearLog()">
                    <i class="fas fa-eraser"></i>
                    مسح السجل
                </button>

                <div id="systemStatus" class="status-card">
                    <h4><i class="fas fa-heartbeat"></i> حالة النظام</h4>
                    <p id="systemInfo">جاري التحقق...</p>
                </div>
            </div>
        </div>

        <div class="iframe-container">
            <div class="iframe-header">
                <h3><i class="fas fa-tachometer-alt"></i> لوحة التراخيص المباشرة</h3>
                <div>
                    <button class="btn" onclick="refreshDashboard()" style="background: rgba(255,255,255,0.2);">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <button class="btn" onclick="openDashboardNewTab()" style="background: rgba(255,255,255,0.2);">
                        <i class="fas fa-external-link-alt"></i>
                        فتح في تبويب جديد
                    </button>
                </div>
            </div>
            <iframe id="dashboardFrame" src="resources/app/src/license-management/admin-dashboard.html"></iframe>
        </div>

        <div class="test-section" style="grid-column: 1 / -1; margin-top: 2rem;">
            <h2><i class="fas fa-terminal"></i> سجل النشاط</h2>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('logArea');
        let monitoringInterval = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icons = {
                'info': '🔵',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            };
            
            const logEntry = `${icons[type]} [${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function clearLog() {
            logElement.textContent = '';
        }

        function generateRandomRequest() {
            const names = [
                'أحمد محمد الجزائري',
                'فاطمة الزهراء بن علي', 
                'خالد بن سعد',
                'عائشة بوعلام',
                'محمد الطاهر',
                'زينب العربي',
                'يوسف بن عمر',
                'مريم الحسني'
            ];
            
            const states = ['الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'سطيف', 'بجاية'];
            const municipalities = ['الجزائر الوسطى', 'وهران', 'قسنطينة', 'عنابة', 'سطيف', 'بجاية'];
            
            return {
                id: 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5),
                fullName: names[Math.floor(Math.random() * names.length)],
                phone: '055' + Math.floor(Math.random() * 10000000).toString().padStart(7, '0'),
                state: states[Math.floor(Math.random() * states.length)],
                municipality: municipalities[Math.floor(Math.random() * municipalities.length)],
                licenseType: Math.random() > 0.5 ? 'trial' : 'lifetime',
                notes: 'طلب تفعيل تلقائي من أداة الاختبار',
                deviceId: 'device_' + Math.random().toString(36).substr(2, 9),
                requestDate: new Date().toISOString(),
                status: 'pending'
            };
        }

        function saveRequest(request) {
            const existingRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            existingRequests.push(request);
            localStorage.setItem('activationRequests', JSON.stringify(existingRequests));
            
            // إشعار النوافذ الأخرى
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'activationRequests',
                newValue: JSON.stringify(existingRequests)
            }));
            
            return existingRequests.length;
        }

        function sendSingleRequest() {
            log('📤 إرسال طلب تفعيل واحد...', 'info');
            
            const request = generateRandomRequest();
            const totalCount = saveRequest(request);
            
            log(`✅ تم إرسال طلب من: ${request.fullName}`, 'success');
            log(`📊 إجمالي الطلبات: ${totalCount}`, 'info');
            
            updateRequestStatus();
        }

        function sendMultipleRequests() {
            log('📤 إرسال 5 طلبات متعددة...', 'info');
            
            for (let i = 0; i < 5; i++) {
                const request = generateRandomRequest();
                saveRequest(request);
                log(`✅ طلب ${i + 1}: ${request.fullName}`, 'success');
            }
            
            log('🎉 تم إرسال جميع الطلبات بنجاح!', 'success');
            updateRequestStatus();
        }

        function sendRandomRequest() {
            setInterval(() => {
                const request = generateRandomRequest();
                saveRequest(request);
                log(`🔄 طلب تلقائي: ${request.fullName}`, 'info');
                updateRequestStatus();
            }, 3000);
            
            log('🔄 تم تفعيل الإرسال التلقائي (كل 3 ثوان)', 'warning');
        }

        function clearAllRequests() {
            if (confirm('هل أنت متأكد من مسح جميع الطلبات؟')) {
                localStorage.removeItem('activationRequests');
                log('🗑️ تم مسح جميع الطلبات', 'warning');
                
                // إشعار النوافذ الأخرى
                window.dispatchEvent(new StorageEvent('storage', {
                    key: 'activationRequests',
                    newValue: null
                }));
                
                updateRequestStatus();
            }
        }

        function checkLocalStorage() {
            log('🔍 فحص localStorage...', 'info');
            
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            log(`📊 عدد الطلبات المحفوظة: ${requests.length}`, 'info');
            
            if (requests.length > 0) {
                log('📋 آخر 3 طلبات:', 'info');
                requests.slice(-3).forEach((req, index) => {
                    log(`${index + 1}. ${req.fullName} - ${req.phone}`, 'info');
                });
            }
        }

        function monitorChanges() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                log('⏹️ تم إيقاف المراقبة', 'warning');
                return;
            }
            
            log('👁️ بدء مراقبة التغييرات...', 'info');
            
            let lastCount = JSON.parse(localStorage.getItem('activationRequests') || '[]').length;
            
            monitoringInterval = setInterval(() => {
                const currentCount = JSON.parse(localStorage.getItem('activationRequests') || '[]').length;
                if (currentCount !== lastCount) {
                    log(`📈 تغيير في عدد الطلبات: ${lastCount} → ${currentCount}`, 'success');
                    lastCount = currentCount;
                    updateRequestStatus();
                }
            }, 1000);
        }

        function simulateStorageEvent() {
            log('📡 محاكاة حدث تخزين...', 'info');
            
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'activationRequests',
                newValue: JSON.stringify(requests)
            }));
            
            log('✅ تم إرسال حدث التخزين', 'success');
        }

        function updateRequestStatus() {
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            const statusElement = document.getElementById('requestCount');
            
            if (requests.length === 0) {
                statusElement.textContent = 'لا توجد طلبات محفوظة';
                document.getElementById('requestStatus').className = 'status-card';
            } else {
                statusElement.textContent = `إجمالي الطلبات: ${requests.length}`;
                document.getElementById('requestStatus').className = 'status-card status-success';
            }
        }

        function refreshDashboard() {
            const iframe = document.getElementById('dashboardFrame');
            iframe.src = iframe.src;
            log('🔄 تم تحديث لوحة التراخيص', 'info');
        }

        function openDashboardNewTab() {
            window.open('resources/app/src/license-management/admin-dashboard.html', '_blank');
            log('🔗 تم فتح لوحة التراخيص في تبويب جديد', 'info');
        }

        // مراقبة أحداث التخزين
        window.addEventListener('storage', (e) => {
            if (e.key === 'activationRequests') {
                log('📡 تم رصد تغيير في localStorage', 'info');
                updateRequestStatus();
            }
        });

        // تهيئة الصفحة
        window.addEventListener('load', () => {
            log('🚀 تم تحميل أداة اختبار التكامل', 'success');
            updateRequestStatus();
            
            // فحص حالة النظام
            const systemInfo = document.getElementById('systemInfo');
            systemInfo.textContent = `localStorage متاح: ${!!localStorage}, إطار لوحة التراخيص محمل`;
            document.getElementById('systemStatus').className = 'status-card status-success';
        });
    </script>
</body>
</html>
