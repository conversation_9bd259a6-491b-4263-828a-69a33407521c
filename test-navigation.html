<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التنقل - لوحة التراخيص</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .test-header {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .test-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
        }

        .status {
            padding: 0.5rem 1rem;
            border-radius: 5px;
            margin: 0.5rem 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .dashboard-frame {
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1><i class="fas fa-bug"></i> اختبار التنقل - لوحة التراخيص</h1>
            <p>اختبار مفصل لوظائف التنقل في لوحة إدارة التراخيص</p>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-play"></i> اختبارات التنقل</h2>
            <button class="btn" onclick="testDashboardLoad()">
                <i class="fas fa-download"></i> اختبار تحميل اللوحة
            </button>
            <button class="btn btn-success" onclick="testNavigation()">
                <i class="fas fa-arrows-alt"></i> اختبار التنقل
            </button>
            <button class="btn btn-danger" onclick="clearLog()">
                <i class="fas fa-trash"></i> مسح السجل
            </button>
            
            <div id="status"></div>
            <div id="log" class="log-area"></div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-external-link-alt"></i> لوحة التراخيص</h2>
            <iframe id="dashboardFrame" class="dashboard-frame" src="resources/app/src/license-management/admin-dashboard.html"></iframe>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            
            logElement.innerHTML += logEntry + '\n';
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function setStatus(message, type = 'info') {
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            logElement.innerHTML = '';
            statusElement.innerHTML = '';
        }

        function testDashboardLoad() {
            log('بدء اختبار تحميل لوحة التراخيص...');
            setStatus('جاري اختبار التحميل...', 'info');

            const frame = document.getElementById('dashboardFrame');
            
            frame.onload = function() {
                try {
                    const frameDoc = frame.contentDocument || frame.contentWindow.document;
                    
                    // Test if HTML loaded
                    if (frameDoc.body) {
                        log('✓ تم تحميل HTML بنجاح');
                        
                        // Test navigation elements
                        const navItems = frameDoc.querySelectorAll('.nav-item');
                        log(`✓ تم العثور على ${navItems.length} عنصر تنقل`);
                        
                        // Test content sections
                        const sections = frameDoc.querySelectorAll('.content-section');
                        log(`✓ تم العثور على ${sections.length} قسم محتوى`);
                        
                        // Test dashboard object
                        if (frame.contentWindow.dashboard) {
                            log('✓ تم تحميل كائن اللوحة بنجاح');
                            setStatus('تم تحميل اللوحة بنجاح', 'success');
                        } else {
                            log('⚠ كائن اللوحة غير متاح');
                            setStatus('كائن اللوحة غير متاح', 'error');
                        }
                        
                    } else {
                        log('✗ فشل في تحميل HTML');
                        setStatus('فشل في تحميل HTML', 'error');
                    }
                    
                } catch (error) {
                    log('✗ خطأ في الوصول للإطار: ' + error.message);
                    setStatus('خطأ في الوصول للإطار', 'error');
                }
            };

            frame.onerror = function() {
                log('✗ خطأ في تحميل الإطار');
                setStatus('خطأ في تحميل الإطار', 'error');
            };

            // Reload frame
            frame.src = frame.src;
        }

        function testNavigation() {
            log('بدء اختبار التنقل...');
            setStatus('جاري اختبار التنقل...', 'info');

            const frame = document.getElementById('dashboardFrame');
            
            try {
                const frameDoc = frame.contentDocument || frame.contentWindow.document;
                const dashboard = frame.contentWindow.dashboard;
                
                if (!dashboard) {
                    log('✗ كائن اللوحة غير متاح');
                    setStatus('كائن اللوحة غير متاح', 'error');
                    return;
                }

                // Test sections
                const sections = ['overview', 'requests', 'licenses', 'generate', 'statistics', 'settings'];
                let testIndex = 0;

                function testNextSection() {
                    if (testIndex >= sections.length) {
                        log('✓ تم اختبار جميع الأقسام بنجاح');
                        setStatus('تم اختبار التنقل بنجاح', 'success');
                        return;
                    }

                    const section = sections[testIndex];
                    log(`اختبار القسم: ${section}`);
                    
                    try {
                        dashboard.switchSection(section);
                        
                        // Check if section is active
                        setTimeout(() => {
                            const activeSection = frameDoc.querySelector('.content-section.active');
                            if (activeSection && activeSection.id === `${section}-section`) {
                                log(`✓ تم التبديل إلى ${section} بنجاح`);
                            } else {
                                log(`✗ فشل في التبديل إلى ${section}`);
                            }
                            
                            testIndex++;
                            setTimeout(testNextSection, 500);
                        }, 300);
                        
                    } catch (error) {
                        log(`✗ خطأ في اختبار ${section}: ${error.message}`);
                        testIndex++;
                        setTimeout(testNextSection, 500);
                    }
                }

                testNextSection();

            } catch (error) {
                log('✗ خطأ في اختبار التنقل: ' + error.message);
                setStatus('خطأ في اختبار التنقل', 'error');
            }
        }

        // Auto-start testing
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('بدء الاختبار التلقائي...');
                testDashboardLoad();
            }, 1000);
        });
    </script>
</body>
</html>
