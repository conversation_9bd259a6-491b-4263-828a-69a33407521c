const { app, BrowserWindow, Menu, dialog, ipcMain, shell, nativeTheme } = require('electron');
const { autoUpdater } = require('electron-updater');
const log = require('electron-log');
const path = require('path');

// إعداد نظام السجلات
log.transports.file.level = 'info';
autoUpdater.logger = log;

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env.NODE_ENV = 'production';

// المتغير العام للنافذة الرئيسية
let mainWindow;

// التحقق من وجود نسخة واحدة فقط من التطبيق
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // إذا حاول المستخدم فتح نسخة ثانية، ركز على النافذة الموجودة
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// فحص حالة تفعيل الترخيص
function checkLicenseActivation() {
  try {
    const fs = require('fs');

    // فحص الترخيص من localStorage (محاكاة)
    // في التطبيق الحقيقي، سيتم فحص الترخيص من ملف محمي أو خادم
    const licenseData = getLicenseFromStorage();

    if (licenseData && licenseData.isActive) {
      // فحص انتهاء الصلاحية
      if (licenseData.expiryDate === 'مدى الحياة') {
        return true;
      }

      const now = new Date();
      const expiryDate = new Date(licenseData.expiryDate);
      return now < expiryDate;
    }

    return false;
  } catch (error) {
    console.error('خطأ في فحص حالة الترخيص:', error);
    return false;
  }
}

// الحصول على بيانات الترخيص من التخزين المحلي
function getLicenseFromStorage() {
  try {
    const fs = require('fs');
    const licensePath = path.join(__dirname, 'data', 'license.json');

    if (fs.existsSync(licensePath)) {
      return JSON.parse(fs.readFileSync(licensePath, 'utf8'));
    }
    return null;
  } catch {
    return null;
  }
}

// إنشاء النافذة الرئيسية
function createMainWindow() {
  const isActivated = checkLicenseActivation();

  mainWindow = new BrowserWindow({
    title: isActivated ? 'نظام إدارة مؤسسة وقود المستقبل' : 'تفعيل الترخيص - مؤسسة وقود المستقبل',
    width: isActivated ? 1200 : 900,
    height: isActivated ? 800 : 700,
    minWidth: isActivated ? 800 : 700,
    minHeight: isActivated ? 600 : 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false
    },
    icon: path.join(__dirname, 'assets/icons/app-icon.ico'),
    show: false, // لا تظهر النافذة حتى تكتمل عملية التحميل
    titleBarStyle: 'default',
    frame: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    center: true
  });

  // تحميل الصفحة المناسبة
  if (isActivated) {
    mainWindow.loadFile('index.html');
  } else {
    mainWindow.loadFile('src/new-login.html');
  }

  // إظهار النافذة عند اكتمال التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // إنشاء قائمة التطبيق
  const mainMenu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(mainMenu);

  // إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إنشاء قائمة التطبيق
const menuTemplate = [
  {
    label: 'ملف',
    submenu: [
      {
        label: 'حفظ البيانات',
        accelerator: process.platform === 'darwin' ? 'Command+S' : 'Ctrl+S',
        click() {
          mainWindow.webContents.executeJavaScript('saveData()');
        }
      },
      {
        label: 'طباعة',
        accelerator: process.platform === 'darwin' ? 'Command+P' : 'Ctrl+P',
        click() {
          mainWindow.webContents.print();
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'خروج',
        accelerator: process.platform === 'darwin' ? 'Command+Q' : 'Ctrl+Q',
        click() {
          app.quit();
        }
      }
    ]
  },
  {
    label: 'عرض',
    submenu: [
      {
        label: 'إعادة تحميل',
        accelerator: 'F5',
        click() {
          mainWindow.reload();
        }
      },
      {
        label: 'تكبير',
        accelerator: process.platform === 'darwin' ? 'Command+Plus' : 'Ctrl+Plus',
        click() {
          mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() + 1);
        }
      },
      {
        label: 'تصغير',
        accelerator: process.platform === 'darwin' ? 'Command+-' : 'Ctrl+-',
        click() {
          mainWindow.webContents.setZoomLevel(mainWindow.webContents.getZoomLevel() - 1);
        }
      },
      {
        label: 'حجم طبيعي',
        accelerator: process.platform === 'darwin' ? 'Command+0' : 'Ctrl+0',
        click() {
          mainWindow.webContents.setZoomLevel(0);
        }
      }
    ]
  },
  {
    label: 'مساعدة',
    submenu: [
      {
        label: 'حول البرنامج',
        click() {
          dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'حول البرنامج',
            message: 'نظام إدارة مؤسسة وقود المستقبل',
            detail: 'الإصدار 2.2.0\nنظام متكامل لإدارة مؤسسة وقود المستقبل\n\nالميزات الجديدة:\n• نسخ احتياطية تلقائية\n• تكامل تيليجرام\n• اختصارات لوحة المفاتيح\n• الوضع المظلم'
          });
        }
      }
    ]
  }
];

// معالجة أحداث IPC
const setupIpcHandlers = () => {
  // فتح مربع حوار حفظ الملف
  ipcMain.handle('save-dialog', async (_, options) => {
    const { canceled, filePath } = await dialog.showSaveDialog(mainWindow, options);
    if (canceled) {
      return null;
    }
    return filePath;
  });

  // فتح مربع حوار فتح الملف
  ipcMain.handle('open-dialog', async (_, options) => {
    const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, options);
    if (canceled) {
      return null;
    }
    return filePaths[0];
  });

  // إنشاء نسخة احتياطية تلقائية
  ipcMain.on('create-auto-backup', () => {
    log.info('إنشاء نسخة احتياطية تلقائية...');
    // يمكن إضافة كود لإنشاء نسخة احتياطية تلقائية هنا
  });

  // فتح رابط خارجي
  ipcMain.handle('open-external', async (_, url) => {
    await shell.openExternal(url);
  });

  // الحصول على معلومات النظام
  ipcMain.handle('get-system-info', () => {
    return {
      platform: process.platform,
      arch: process.arch,
      version: app.getVersion(),
      electronVersion: process.versions.electron,
      nodeVersion: process.versions.node
    };
  });

  // تغيير الثيم
  ipcMain.handle('set-theme', (_, theme) => {
    nativeTheme.themeSource = theme;
    return nativeTheme.shouldUseDarkColors;
  });

  // الحصول على الثيم الحالي
  ipcMain.handle('get-theme', () => {
    return {
      shouldUseDarkColors: nativeTheme.shouldUseDarkColors,
      themeSource: nativeTheme.themeSource
    };
  });

  // معالجات نظام التفعيل والترخيص
  ipcMain.handle('activate-license', async (_, licenseKey) => {
    try {
      // التحقق من صحة مفتاح الترخيص
      const isValid = validateLicenseKey(licenseKey);

      if (isValid) {
        const licenseData = {
          key: licenseKey,
          isActive: true,
          activationDate: new Date().toISOString(),
          expiryDate: licenseKey.startsWith('LIFE') ? 'مدى الحياة' : calculateExpiryDate(30),
          type: licenseKey.startsWith('LIFE') ? 'lifetime' : 'trial'
        };

        // حفظ الترخيص
        const fs = require('fs');
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(
          path.join(dataDir, 'license.json'),
          JSON.stringify(licenseData, null, 2)
        );

        return { success: true, license: licenseData };
      } else {
        return { success: false, error: 'مفتاح الترخيص غير صحيح' };
      }
    } catch (error) {
      return { success: false, error: 'حدث خطأ أثناء تفعيل الترخيص' };
    }
  });

  // إرسال طلب تفعيل
  ipcMain.handle('submit-activation-request', async (_, requestData) => {
    try {
      // حفظ الطلب محلياً
      const fs = require('fs');
      const dataDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const requestsFile = path.join(dataDir, 'activation-requests.json');
      let requests = [];

      if (fs.existsSync(requestsFile)) {
        requests = JSON.parse(fs.readFileSync(requestsFile, 'utf8'));
      }

      const newRequest = {
        ...requestData,
        id: generateRequestId(),
        requestDate: new Date().toISOString(),
        status: 'pending',
        deviceId: generateDeviceId()
      };

      requests.push(newRequest);
      fs.writeFileSync(requestsFile, JSON.stringify(requests, null, 2));

      return { success: true, requestId: newRequest.id };
    } catch (error) {
      return { success: false, error: 'فشل في إرسال طلب التفعيل' };
    }
  });

  // فحص حالة طلب التفعيل
  ipcMain.handle('check-activation-status', async (_, requestId) => {
    try {
      const fs = require('fs');
      const requestsFile = path.join(__dirname, 'data', 'activation-requests.json');

      if (fs.existsSync(requestsFile)) {
        const requests = JSON.parse(fs.readFileSync(requestsFile, 'utf8'));
        const request = requests.find(req => req.id === requestId);

        if (request) {
          return { success: true, status: request.status, licenseKey: request.licenseKey };
        }
      }

      return { success: false, error: 'الطلب غير موجود' };
    } catch (error) {
      return { success: false, error: 'خطأ في فحص حالة الطلب' };
    }
  });

  // إعادة تحميل التطبيق بعد التفعيل
  ipcMain.handle('reload-after-activation', () => {
    mainWindow.loadFile('index.html');
    mainWindow.setSize(1200, 800);
    mainWindow.center();
    mainWindow.setTitle('نظام إدارة مؤسسة وقود المستقبل');
  });

  // فتح لوحة تحكم المدير
  ipcMain.handle('open-admin-dashboard', () => {
    const adminWindow = new BrowserWindow({
      title: 'لوحة تحكم المدير - نظام إدارة التراخيص',
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 800,
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        webSecurity: true
      },
      icon: path.join(__dirname, 'assets/icons/app-icon.ico'),
      parent: mainWindow,
      modal: false,
      show: false
    });

    adminWindow.loadFile('src/license-management/admin-dashboard.html');

    adminWindow.once('ready-to-show', () => {
      adminWindow.show();
    });

    return { success: true };
  });

  // التوجه إلى صفحة التفعيل
  ipcMain.handle('redirect-to-auth', () => {
    mainWindow.loadFile('src/new-login.html');
    mainWindow.setSize(900, 700);
    mainWindow.center();
    mainWindow.setTitle('تسجيل الدخول - مؤسسة وقود المستقبل');
  });

// وظائف مساعدة للترخيص
function validateLicenseKey(key) {
  // التحقق من تنسيق مفتاح الترخيص
  const patterns = [
    /^LIFE-[A-Z0-9]{8}-[A-Z0-9]+$/,  // مدى الحياة
    /^TRIAL-[A-Z0-9]{8}-[A-Z0-9]+$/ // تجريبي
  ];

  return patterns.some(pattern => pattern.test(key));
}

function calculateExpiryDate(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString();
}

function generateRequestId() {
  return 'req_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

function generateDeviceId() {
  const os = require('os');
  const crypto = require('crypto');

  const machineId = os.hostname() + os.platform() + os.arch();
  return crypto.createHash('md5').update(machineId).digest('hex').substr(0, 16);
}
};

// إعداد نظام التحديث التلقائي
const setupAutoUpdater = () => {
  // التحقق من التحديثات عند بدء التطبيق
  autoUpdater.checkForUpdatesAndNotify();

  // أحداث التحديث
  autoUpdater.on('checking-for-update', () => {
    log.info('جاري البحث عن تحديثات...');
  });

  autoUpdater.on('update-available', (info) => {
    log.info('تحديث متوفر:', info);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'تحديث متوفر',
      message: 'يتوفر إصدار جديد من التطبيق',
      detail: `الإصدار الجديد: ${info.version}\nسيتم تنزيل التحديث في الخلفية.`,
      buttons: ['موافق']
    });
  });

  autoUpdater.on('update-not-available', (info) => {
    log.info('لا توجد تحديثات متوفرة:', info);
  });

  autoUpdater.on('error', (err) => {
    log.error('خطأ في التحديث:', err);
  });

  autoUpdater.on('download-progress', (progressObj) => {
    let log_message = `سرعة التنزيل: ${progressObj.bytesPerSecond}`;
    log_message = log_message + ` - تم تنزيل ${progressObj.percent}%`;
    log_message = log_message + ` (${progressObj.transferred}/${progressObj.total})`;
    log.info(log_message);
  });

  autoUpdater.on('update-downloaded', (info) => {
    log.info('تم تنزيل التحديث:', info);
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'تحديث جاهز',
      message: 'تم تنزيل التحديث بنجاح',
      detail: 'سيتم إعادة تشغيل التطبيق لتطبيق التحديث.',
      buttons: ['إعادة التشغيل الآن', 'لاحقاً']
    }).then((result) => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  });
};

// تشغيل التطبيق
app.whenReady().then(() => {
  setupIpcHandlers();
  createMainWindow();
  setupAutoUpdater();
});

// إغلاق التطبيق عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// إعادة إنشاء النافذة عند تفعيل التطبيق (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});