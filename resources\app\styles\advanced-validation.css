/**
 * أنماط نظام التحقق المتقدم
 * Advanced Validation System Styles
 */

/* حاوي رسائل التحقق */
.validation-container {
    margin-top: 10px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* رسائل التحقق */
.validation-message {
    padding: 12px 16px;
    margin: 4px 0;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.4;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideInDown 0.3s ease-out;
}

.validation-message.error {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border-left: 4px solid #f44336;
    color: #c62828;
}

.validation-message.warning {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border-left: 4px solid #ff9800;
    color: #ef6c00;
}

.validation-message.success {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-left: 4px solid #4caf50;
    color: #2e7d32;
}

.validation-message.info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 4px solid #2196f3;
    color: #1565c0;
}

/* أيقونات رسائل التحقق */
.validation-message .icon {
    font-size: 16px;
    flex-shrink: 0;
}

.validation-message.error .icon::before {
    content: "❌";
}

.validation-message.warning .icon::before {
    content: "⚠️";
}

.validation-message.success .icon::before {
    content: "✅";
}

.validation-message.info .icon::before {
    content: "ℹ️";
}

/* حقول الإدخال مع حالات التحقق */
.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea {
    border-color: #f44336;
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
    background-color: #ffebee;
}

.form-group.has-warning input,
.form-group.has-warning select,
.form-group.has-warning textarea {
    border-color: #ff9800;
    box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.2);
    background-color: #fff8e1;
}

.form-group.has-success input,
.form-group.has-success select,
.form-group.has-success textarea {
    border-color: #4caf50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    background-color: #e8f5e8;
}

/* مؤشرات التحقق في الوقت الفعلي */
.validation-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    pointer-events: none;
}

.form-group {
    position: relative;
}

.validation-indicator.checking {
    animation: spin 1s linear infinite;
}

.validation-indicator.checking::before {
    content: "⏳";
}

.validation-indicator.valid::before {
    content: "✅";
    color: #4caf50;
}

.validation-indicator.invalid::before {
    content: "❌";
    color: #f44336;
}

.validation-indicator.warning::before {
    content: "⚠️";
    color: #ff9800;
}

/* لوحة إحصائيات التحقق */
.validation-stats {
    background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.validation-stats h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.stat-item.total .stat-value {
    color: #2196f3;
}

.stat-item.success .stat-value {
    color: #4caf50;
}

.stat-item.error .stat-value {
    color: #f44336;
}

.stat-item.rate .stat-value {
    color: #ff9800;
}

/* قائمة التحقق التفاعلية */
.validation-checklist {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.validation-checklist h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.checklist-item:last-child {
    border-bottom: none;
}

.checklist-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.checklist-text {
    flex: 1;
    font-size: 14px;
}

.checklist-item.pending .checklist-icon::before {
    content: "⏳";
}

.checklist-item.valid .checklist-icon::before {
    content: "✅";
}

.checklist-item.invalid .checklist-icon::before {
    content: "❌";
}

.checklist-item.warning .checklist-icon::before {
    content: "⚠️";
}

/* تأثيرات الحركة */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from {
        transform: translateY(-50%) rotate(0deg);
    }
    to {
        transform: translateY(-50%) rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .validation-message {
        padding: 10px 12px;
        font-size: 13px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .stat-item {
        padding: 12px;
    }
    
    .stat-value {
        font-size: 20px;
    }
    
    .validation-stats,
    .validation-checklist {
        padding: 15px;
        margin: 15px 0;
    }
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .validation-message.error {
        background: linear-gradient(135deg, #3e2723 0%, #5d4037 100%);
        color: #ffcdd2;
    }
    
    .validation-message.warning {
        background: linear-gradient(135deg, #3e2723 0%, #5d4037 100%);
        color: #ffecb3;
    }
    
    .validation-message.success {
        background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%);
        color: #c8e6c9;
    }
    
    .validation-message.info {
        background: linear-gradient(135deg, #0d47a1 0%, #1565c0 100%);
        color: #bbdefb;
    }
    
    .validation-stats {
        background: linear-gradient(135deg, #424242 0%, #616161 100%);
    }
    
    .validation-stats h3 {
        color: #fff;
    }
    
    .stat-item {
        background: #333;
        color: #fff;
    }
    
    .validation-checklist {
        background: #333;
    }
    
    .validation-checklist h4 {
        color: #fff;
    }
    
    .checklist-item {
        border-bottom-color: #555;
    }
    
    .checklist-text {
        color: #ccc;
    }
}

/* تحسينات إضافية للتفاعل */
.validation-message {
    cursor: pointer;
    transition: all 0.2s ease;
}

.validation-message:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.validation-container.collapsed .validation-message {
    display: none;
}

.validation-container.collapsed .validation-message:first-child {
    display: flex;
}

.validation-toggle {
    background: #f5f5f5;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    transition: all 0.2s ease;
}

.validation-toggle:hover {
    background: #e0e0e0;
    color: #333;
}

.validation-toggle.expanded::after {
    content: " ▼";
}

.validation-toggle.collapsed::after {
    content: " ▶";
}
